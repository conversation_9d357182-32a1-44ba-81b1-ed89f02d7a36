"""This lookup plugin finds the last modified time of the provided input file"""
import os
from ansible.errors import AnsibleError
from ansible.plugins.lookup import LookupBase

class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        if not terms or len(terms) != 1:
            raise AnsibleError("THis plugin takes one and only one argument")
        path = self._templar.template(terms[0])
        try:
            if not os.path.exists(path):
                return [0]
            mod_time = os.path.getmtime(path)
        except Exception as e:
            raise AnsibleError("Error while getting file mode time") from e
        return [mod_time]
