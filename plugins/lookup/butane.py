"""This module runs butane on input files"""
import subprocess
import tempfile

from ansible.plugins.lookup import LookupBase
from ansible.errors import AnsibleError
from ansible.template import Templar

class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        if not terms or len(terms) != 1:
            raise AnsibleError("This plugin takes one and only one argument")
        butane_input_template = terms[0]
        templar = Templar(loader=self._loader, variables=variables)
        with open(butane_input_template, 'r', encoding="utf-8") as template_file:
            butane_input_template_content = template_file.read()
        rendered_butane_file = templar.template(butane_input_template_content)
        with tempfile.NamedTemporaryFile(delete=False) as temporary_butane_input_file:
            temporary_butane_input_file.write(rendered_butane_file.encode())
        butane_command = ['butane', temporary_butane_input_file.name ]
        with subprocess.Popen(
            butane_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        ) as process:
            result, error = process.communicate()
            if process.returncode == 0:
                return [result]
        raise AnsibleError(
            "Butane returned non zero error code because:"
            f"{error} with input {rendered_butane_file}"
        )
