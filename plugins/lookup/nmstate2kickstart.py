"""This lookup plugin converts a basic nmstate file into kickstart network lines"""
from dataclasses import dataclass
from ansible.plugins.lookup import LookupBase
from ansible.errors import AnsibleError
from ansible.utils.display import Display
display = Display()

@dataclass
class Interface:
    def __init__(self, name: str, data: dict):
        self.name: str = name
        self.type: str = data.get("type")
        self.mtu: int = data.get("mtu")
        self.ipv4: dict = data.get("ipv4", {})
        self.link_aggregation: dict = data.get("link-aggregation", {})
        self.vlan = data.get("vlan", {})

@dataclass
class KickstartConverter:
    def __init__(self, nmstate: dict):
        self.interfaces = [
            Interface(interface.get('name'), interface)
            for interface in nmstate.get('interfaces', [])
        ]
        self.routes = nmstate.get('routes', {}).get('config', [])
        self.slave_names = {
            port
            for interface in self.interfaces
            for port in interface.link_aggregation.get('port', [])
        }

    def convert(self) -> list[str]:
        commands = []
        for interface in self.interfaces:
            if interface.name not in self.slave_names:
                self.update_gateway(interface)
                commands.append(build_command(interface))
        return ["\n".join(commands)]

    def update_gateway(self, interface) -> None:
        gateway = next(
            (
                route['next-hop-address']
                for route in self.routes
                if route.get('next-hop-interface') == interface.name
                and route.get('destination') == '0.0.0.0/0'
            ), None)
        if gateway:
            interface.ipv4['gateway'] = gateway

class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        if not terms or len(terms) != 1:
            raise AnsibleError("This plugin takes one and only one argument")
        network_data = terms[0]
        if not isinstance(network_data, dict):
            raise AnsibleError("Expected nmstate data as dict with 'interfaces' and 'routes'")
        return KickstartConverter(network_data).convert()

def build_command(interface: Interface) -> None:
    parts = ["network", "--noipv6", "--activate"]
    if interface.mtu:
        parts.append(f"--mtu={interface.mtu}")
    if interface.type == "bond":
        process_bond(parts, interface)
    elif interface.type == "vlan":
        process_vlan(parts, interface)
    else:
        parts.append(f"--device={interface.name}")
    process_ipv4(parts, interface)
    return ' '.join(parts)

def process_bond(parts: list[str], interface: Interface) -> None:
    ports = interface.link_aggregation.get('port', [])
    parts.append(f"--device={interface.name}")
    parts.append(f"--bondslaves={','.join(ports)}")
    bond_options = []
    mode = interface.link_aggregation.get('mode')
    if mode:
        bond_options.append(f"mode={mode}")
    if bond_options:
        parts.append(f"--bondopts={','.join(bond_options)}")

def process_vlan(parts: list[str], interface: Interface) -> None:
    base_iface = interface.vlan.get('base-iface')
    parts.append(f"--device={base_iface}")
    parts.append(f"--vlanid={interface.vlan.get('id')}")
    default_name = f"{base_iface}.{interface.vlan.get('id')}"
    if interface.name != default_name:
        parts.append(f"--interfacename={interface.name}")

def process_ipv4(parts: list[str], interface: Interface) -> None:
    address: dict = interface.ipv4.get('address', [{}])[0]
    ip: str = address.get('ip')
    prefix: int = address.get('prefix-length')
    if ip and prefix is not None:
        parts.extend([
            "--bootproto=static",
            f"--ip={ip}",
            f"--netmask={prefix_to_netmask(prefix)}"
        ])
    else:
        parts.extend(["--noipv4"])
    gateway = interface.ipv4.get('gateway')
    if gateway:
        parts.append(f"--gateway={gateway}")

def prefix_to_netmask(prefix: int) -> str:
    mask = (0xffffffff << (32 - prefix)) & 0xffffffff
    return '.'.join(str((mask >> (i * 8)) & 0xff) for i in [3,2,1,0])
