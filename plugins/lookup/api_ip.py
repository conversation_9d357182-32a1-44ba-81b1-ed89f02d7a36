"""This lookup finds ip address for the api endpoint of a cluster"""
from ansible.plugins.lookup import LookupBase
from ansible.errors import AnsibleError
import boto3

TAG_KEY="galaxy-cluster"

class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        if not terms or len(terms) != 1:
            raise AnsibleError("This plugin takes one and only one argument")
        cluster_name = terms[0]
        client = boto3.client('route53')
        hosted_zones = client.list_hosted_zones()['HostedZones']
        matching_zone_id = None
        for zone in hosted_zones:
            zone_id = zone['Id'].split("/")[-1]
            tag_sets = client.list_tags_for_resource(ResourceType='hostedzone', ResourceId=zone_id)
            tags = tag_sets['ResourceTagSet']['Tags']
            if any(tag['Key'] == TAG_KEY and tag['Value'] == cluster_name for tag in tags):
                matching_zone_id = zone_id
                break
        if not matching_zone_id:
            raise AnsibleError(f"No hosted zone found with tag {TAG_KEY}={cluster_name}")
        aws_records = client.list_resource_record_sets(HostedZoneId=matching_zone_id)
        records = aws_records['ResourceRecordSets']
        for record in records:
            if f"api.{cluster_name}" in record['Name'] and record['Type'] == 'A':
                return [record['ResourceRecords'][0]['Value']]

        raise AnsibleError(f"Record api.{cluster_name} not found in hosted zone {matching_zone_id}")
