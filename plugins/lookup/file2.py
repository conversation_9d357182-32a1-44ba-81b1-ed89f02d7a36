# pylint: skip-file
from ansible.errors import AnsibleParser<PERSON>rror
from ansible.plugins.lookup import LookupBase
from ansible.module_utils._text import to_text
from ansible.utils.display import Display
display = Display()

class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        ret = []
        self.set_options(var_options=variables, direct=kwargs)
        for term in terms:
            try:
                lookupfile = self.find_file_in_search_path(variables, 'files', term)
                if lookupfile:
                    b_contents, _ = self._loader._get_file_contents(lookupfile)
                    contents = to_text(b_contents, errors='surrogate_or_strict')
                    contents = contents.lstrip()
                    contents = contents.rstrip()
                    ret.append(contents)
                else:
                    raise AnsibleParserError()
            except Exception as e:
                display.v(f"Exception was thrown {e}")
                return [None]
        return ret
