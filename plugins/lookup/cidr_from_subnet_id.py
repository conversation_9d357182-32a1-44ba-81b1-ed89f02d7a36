"""This lookup finds the cidr of an inputted subnet id"""
from ansible.plugins.lookup import LookupBase
from ansible.errors import AnsibleError
import boto3

class LookupModule(LookupBase):
    def run(self, terms, variables=None, **kwargs):
        if not terms or len(terms) != 1:
            raise AnsibleError("This plugin takes one and only one argument")
        subnet_id = terms[0]
        ec2_client = boto3.client('ec2')
        cidr_block = ec2_client.describe_subnets(SubnetIds=[subnet_id])['Subnets'][0]['CidrBlock']
        return  [cidr_block]
