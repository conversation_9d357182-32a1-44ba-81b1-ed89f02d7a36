"""<PERSON><PERSON><PERSON> communicates with A<PERSON> and creates the bastion ec2 instance."""
import base64
import hashlib
from dataclasses import dataclass
import boto3
from ansible.module_utils.basic import AnsibleModule

module: AnsibleModule = None
DEFAULT_VOLUME_SIZE = 200
DEFAULT_VOLUME_TYPE = "gp3"
DEFAULT_INSTANCE_TYPE = "m6i.xlarge"
DEFAULT_DELETE_ON_TERMINATION = True
DEFAULT_ENABLE_MONITORING = True
SECURITY_GROUP_DESCRIPTION = "Security group with open rules for SSH, HTTP, HTTPS"
DEFAULT_INBOUND_RULES = [
    {"IpProtocol": "tcp", "FromPort": 22,  "ToPort": 22,  "CidrIp": "0.0.0.0/0"},
    {"IpProtocol": "tcp", "FromPort": 80,  "ToPort": 80,  "CidrIp": "0.0.0.0/0"},
    {"IpProtocol": "tcp", "FromPort": 443, "ToPort": 443, "CidrIp": "0.0.0.0/0"}
]
EBS = {
    'VolumeSize': DEFAULT_VOLUME_SIZE,
    'VolumeType': DEFAULT_VOLUME_TYPE,
    'DeleteOnTermination': DEFAULT_DELETE_ON_TERMINATION
}
BLOCK_DEVICE_MAPPINGS = [
    {
        'DeviceName': '/dev/sda1',
        'Ebs': EBS
    }
]
DEFAULT_BASE_INSTANCE_CONFIG =  {
    "InstanceType": DEFAULT_INSTANCE_TYPE,
    "BlockDeviceMappings": BLOCK_DEVICE_MAPPINGS,
    "MinCount": 1,
    "MaxCount": 1,
    "Monitoring": {
        "Enabled": DEFAULT_ENABLE_MONITORING
    },
}
IP_PERMISSIONS = [
        {
            'IpProtocol': rule["IpProtocol"],
            'FromPort': rule["FromPort"],
            'ToPort':   rule["ToPort"],
            'IpRanges': [{'CidrIp': rule["CidrIp"]}]
        } for rule in DEFAULT_INBOUND_RULES
    ]
USER_DATA_TEMPLATE="""#cloud-config
users:
  - name: {username}
    ssh-authorized-keys:
      - {public_ssh_key}
    passwd: {password}
    ssh_pwauth: true
    sudo: ALL=(ALL) NOPASSWD:ALL
"""

MODULE_ARGS={
        "ami_id":{"type":'str', "required":True},
        "subnet_id":{"type":'str', "required":True},
        "ipv4_address":{"type":'str', "required":True},
        "name":{"type":'str', "required":True},
        "username":{"type":'str', "required":True},
        "password":{"type":'str', 'no_log':True, "required":True},
        "public_ssh_key":{"type":'str', "required":False, "default":''},
        "force_create":{"type":'bool', "required":False, "default":False},
        "volume_size":{"type":'int', "required":False, "default":DEFAULT_VOLUME_SIZE}
    }
ec2_client = boto3.client('ec2')

def compute_hash() -> str:
    relevant = [
        module.params['ipv4_address'],
        module.params['name'],
        module.params['ami_id'],
        module.params['subnet_id'],
        module.params['username'],
        module.params['password'],
        module.params['public_ssh_key'],
        module.params['volume_size']
    ]
    h = hashlib.sha256()
    for item in relevant:
        h.update(str(item).encode("utf-8"))
    full_hash = h.hexdigest()
    return full_hash[:16]

@dataclass
class EC2Network:
    def __init__(self, ec2_name: str):
        self.ipv4_address: str = module.params['ipv4_address']
        self.subnet_id: str = module.params['subnet_id']
        self.sg_name: str = f"{ec2_name}-sg"
        self.sg_id: str = None
        subnets = ec2_client.describe_subnets(SubnetIds=[self.subnet_id])
        self.vpc_id: str = subnets['Subnets'][0]['VpcId']

@dataclass
class EC2Tag:
    def __init__(self, ec2_name: str):
        self.hash = compute_hash()
        self.key: str = f"{ec2_name}-hash"
        self.hash_tag: dict = {"Key": self.key, "Value": self.hash}

@dataclass
class EC2Instance:
    def __init__(self):
        self.name: str = module.params['name']
        self.tag: EC2Tag = EC2Tag(self.name)
        self.volume_size: str = module.params['volume_size']
        self.changed: bool = False
        self.id: str = None
        self.network: EC2Network = EC2Network(self.name)
        self.force_create: bool = module.params['force_create']

    def create_instance_params(self):
        ami_id: str = module.params['ami_id']
        user_data_encoded: bytes = create_user_encoded_data()
        tags = [
            {"Key": "Name", "Value": self.name},
            self.tag.hash_tag
        ]
        tag_specifications = [
            {
                "ResourceType": "instance",
                "Tags": tags
            }
        ]
        interface = {
            "DeviceIndex": 0,
            "PrivateIpAddress": self.network.ipv4_address,
            "Groups": [self.network.sg_id],
            "SubnetId": self.network.subnet_id,
        }
        ebs = EBS | {'VolumeSize': self.volume_size}
        block_device_mappings = [
            {
                'DeviceName': '/dev/sda1',
                'Ebs': ebs
            }
        ]
        base_instance_config = DEFAULT_BASE_INSTANCE_CONFIG | {
            "BlockDeviceMappings": block_device_mappings
        }
        return base_instance_config | {
            "ImageId": ami_id,
            "TagSpecifications": tag_specifications,
            "UserData": user_data_encoded,
            "NetworkInterfaces": [interface]
        }

    def cleanup_old_resources(self) -> None:
        deprecated_instance_ids: list[str] = self.get_deprecated_instances()
        self.delete_deprecated_instances(deprecated_instance_ids)
        depecrated_sg_ids: list[str] = self.get_deprecated_sgs()
        self.delete_deprecated_sgs(depecrated_sg_ids)

    def get_deprecated_instances(self) -> list[str]:
        deprecated_instance_ids = []
        resp = ec2_client.describe_instances(
            Filters=[
                {"Name": "tag-key", "Values": [self.tag.key]}
            ]
        )
        for reservation in resp.get("Reservations", []):
            for inst in reservation.get("Instances", []):
                tags = inst.get("Tags", [])
                rh_value = None
                for t in tags:
                    if t["Key"] == self.tag.key:
                        rh_value = t["Value"]
                        break
                if rh_value and (rh_value != self.tag.hash or self.force_create):
                    deprecated_instance_ids.append(inst["InstanceId"])
        return deprecated_instance_ids

    def delete_deprecated_instances(self, deprecated_instance_ids: list[str]) -> None:
        if deprecated_instance_ids:
            ec2_client.terminate_instances(InstanceIds=deprecated_instance_ids)
            waiter = ec2_client.get_waiter("instance_terminated")
            waiter.wait(InstanceIds=deprecated_instance_ids)

    def get_deprecated_sgs(self) -> list[str]:
        depecrated_sg_ids = []
        sg_resp = ec2_client.describe_security_groups(
            Filters=[
                {"Name": "tag-key", "Values": [self.tag.key]}
            ]
        )
        for sg in sg_resp.get("SecurityGroups", []):
            rh_value = None
            for t in sg.get("Tags", []):
                if t["Key"] == self.tag.key:
                    rh_value = t["Value"]
                    break
            if rh_value and (rh_value != self.tag.hash or self.force_create):
                depecrated_sg_ids.append(sg["GroupId"])
        return depecrated_sg_ids

    def delete_deprecated_sgs(self, depecrated_sg_ids: list[str]) -> None:
        for sg_id in depecrated_sg_ids:
            ec2_client.delete_security_group(GroupId=sg_id)

    def set_sg(self):
        existing_sg_id = self.find_existing_sg()
        if existing_sg_id:
            self.network.sg_id = existing_sg_id
        else:
            self.network.sg_id = self.create_security_group()

    def find_existing_sg(self) -> str:
        resp = ec2_client.describe_security_groups(
            Filters=[
                {"Name": f"tag:{self.tag.key}", "Values": [self.tag.hash]}
            ]
        )
        sgs = resp.get("SecurityGroups", [])
        if len(sgs) > 0:
            return sgs[0]["GroupId"]
        return None

    def create_security_group(self):
        create_resp = ec2_client.create_security_group(
            GroupName=self.network.sg_name,
            Description=SECURITY_GROUP_DESCRIPTION,
            VpcId=self.network.vpc_id
        )
        sg_id = create_resp["GroupId"]
        ec2_client.create_tags(
            Resources=[sg_id],
            Tags=[
                self.tag.hash_tag,
                {"Key": "Name", "Value": self.network.sg_name}
            ]
        )
        ec2_client.authorize_security_group_ingress(
            GroupId=sg_id,
            IpPermissions=IP_PERMISSIONS
        )
        self.changed = True
        return sg_id

    def find_existing_instance(self):
        resp = ec2_client.describe_instances(
            Filters=[
                {"Name": f"tag:{self.tag.key}", "Values": [self.tag.hash]}
            ]
        )
        for reservation in resp.get("Reservations", []):
            for inst in reservation.get("Instances", []):
                state_name = inst["State"]["Name"]
                if state_name not in ["terminated", "shutting-down"]:
                    return inst
        return None

    def set_instance(self, instance_params):
        existing_instance = self.find_existing_instance()
        if existing_instance:
            self.id = existing_instance["InstanceId"]
        else:
            response = ec2_client.run_instances(**instance_params)
            self.changed = True
            self.id = response["Instances"][0]["InstanceId"]

    def ensure_on(self):
        response = ec2_client.describe_instances(InstanceIds=[self.id])
        state = response["Reservations"][0]["Instances"][0]["State"]["Name"]
        if state == "stopped":
            ec2_client.start_instances(InstanceIds=[self.id])

def create_user_encoded_data() -> bytes:
    user_data: str = USER_DATA_TEMPLATE.format(
        username=module.params['username'],
        password=module.params['password'],
        public_ssh_key=module.params['public_ssh_key'])
    return base64.b64encode(user_data.encode())

def create_ansible_module() -> AnsibleModule:
    module_args: dict = MODULE_ARGS
    return AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

def ec2_create() -> None:
    global module
    module = create_ansible_module()
    instance: EC2Instance = EC2Instance()
    instance.cleanup_old_resources()
    instance.set_sg()
    instance_parames = instance.create_instance_params()
    instance.set_instance(instance_parames)
    instance.ensure_on()
    msg: str =f"Successfully launched instance with resource_hash={instance.tag.hash}"
    module.exit_json(changed=instance.changed, msg=msg, instance_id=instance.id)

if __name__ == '__main__':
    ec2_create()
