"""This module is used to compare two install configs and figure out if there are any differences"""
import json
import yaml
from ansible.module_utils.basic import AnsibleModule

module: AnsibleModule = None

idompotent_vars = [
    "imageContentSources",
    "platform.baremetal.bootstrapExternalStaticGateway",
    "platform.baremetal.bootstrapExternalStaticIP",
    "platform.baremetal.bootstrapOSImage",
    "platform.baremetal.externalBridge"
]
reinstall_vars = [
    "apiVersion",
    "baseDomain",
    "controlPlane.name",
    "controlPlane.replicas",
    "fips",
    "metadata.name",
    "networking.clusterNetwork",
    "networking.machineNetwork",
    "networking.networkType",
    "networking.serviceNetwork.**********/16",
    "platform.baremetal.apiVIP",
    "platform.baremetal.hosts.master-",
    "platform.baremetal.ingressVIP",
    "platform.baremetal.provisioningNetwork",
    "platform.baremetal.provisioningNetworkCIDR"
]
unsupported_vars = [
    "additionalTrustBundle",
    "compute.worker.name",
    "compute.worker.replicas",
    "platform.baremetal.hosts.worker-*",
    "pullSecret",
    "sshKey"
]

EQUIVALENT = "equivalent"
DIFFERENT_VALUES = "different-values"
DIFFERENT_ADDITIONAL = "different-additional"
DIFFERENT_MISSING = "different-missing"
DIFFERENT_TYPE = "error-different-type"
REINSTALL_ERROR_MSG = (
    "Changes in the install config exist that require the cluster to be reinstalled."
    "Please fix these items and try again. "
    "Or turn on force redeployment to blow away existing cluster."
)

def main():
    global module
    module = create_ansible_module()
    install_config_1 = module.params['install_config_1']
    install_config_2 = module.params['install_config_2']
    result = determine_result(install_config_1, install_config_2)
    module.exit_json(**result)

def create_ansible_module() -> AnsibleModule:
    module_args = {
        "install_config_1" : {"type":'dict', "required":True},
        "install_config_2" : {"type":'dict', "required":True}
    }
    return AnsibleModule(argument_spec=module_args)

def determine_result(install_config_1: dict, install_config_2: dict) -> dict:
    result = compare_dicts(install_config_1, install_config_2)
    result = remove_equivalent_items(result)
    if check_if_any_string_in_list_1_starts_with_any_string_in_list_2(result, reinstall_vars):
        error_list = extract_error_string(reinstall_vars, result)
        msg=f"{REINSTALL_ERROR_MSG}\n{yaml.dump(error_list, default_flow_style=False)}"
        return {"changed":True, "msg":msg, "failed":True}
    if check_if_any_string_in_list_1_starts_with_any_string_in_list_2(result, unsupported_vars):
        unsupported_list = extract_error_string(unsupported_vars, result)
        return {"changed":True, "unsupported_list":unsupported_list, "failed":False}
    return {"changed":False, "failed":False, "unsupported_list":[]}

def compare_dicts(dict_1: dict, dict_2: dict) -> list[str]:
    diffs = []
    all_properties = set(dict_1.keys()).union(set(dict_2.keys()))
    for key in all_properties:
        difference = determine_difference(dict_1.get(key), dict_2.get(key))
        if isinstance(difference, list):
            for diff in difference:
                diffs.append(f"{key}.{diff}")
        else:
            diffs.append(f"{key}.{difference}")
    return diffs

def determine_difference(value_1, value_2):
    if value_1 is None and value_2 is not None:
        return DIFFERENT_ADDITIONAL
    if value_2 is None and value_1 is not None:
        return DIFFERENT_MISSING
    if not isinstance(value_1, type(value_2)):
        return DIFFERENT_TYPE
    return compare_items(value_1, value_2)

def compare_items(value_1, value_2):
    if isinstance(value_1, dict):
        return compare_dicts(value_1, value_2)
    if isinstance(value_1,list):
        return compare_lists(value_1, value_2)
    if value_1 != value_2:
        return DIFFERENT_VALUES
    return EQUIVALENT

def compare_lists(list_1: list, list_2: list):
    if type_of_list(list_1) != type_of_list(list_2):
        return DIFFERENT_TYPE
    if type_of_list(list_1) == list:
        return compare_list_of_lists(list_1, list_2)
    if type_of_list(list_1) == dict:
        return compare_list_of_dicts(list_1, list_2)
    return compare_list_of_items(list_1, list_2)

def type_of_list(input_list: list) -> type:
    if all(isinstance(item, type(input_list[0])) for item in input_list):
        return type(input_list[0])
    return DIFFERENT_TYPE

def compare_list_of_lists(list_1: list[list], list_2: list[list]) -> str:
    if hash(json.dumps(list_1)) == hash(json.dumps(list_2)):
        return EQUIVALENT
    return DIFFERENT_VALUES

def compare_list_of_dicts(list_1: list[dict], list_2: list[dict]):
    if all_dicts_have_name(list_1) and all_dicts_have_name(list_2):
        list_1 = convert_list_of_dicts_to_dict_of_dicts_with_name_as_key(list_1)
        list_2 = convert_list_of_dicts_to_dict_of_dicts_with_name_as_key(list_2)
        return compare_dicts(list_1, list_2)
    if hash(json.dumps(list_1)) == hash(json.dumps(list_2)):
        return EQUIVALENT
    return DIFFERENT_VALUES

def all_dicts_have_name(input_list: list[dict]) -> bool:
    return all('name' in d for d in input_list)

def convert_list_of_dicts_to_dict_of_dicts_with_name_as_key(list_of_dicts: list[dict]) -> dict:
    return {d['name']: d for d in list_of_dicts if 'name' in d}

def compare_list_of_items(list_1: list, list_2: list) -> list[str]:
    diffs = []
    for item in list_1:
        if item in list_2:
            difference = EQUIVALENT
            list_2.remove(item)
        else:
            difference = DIFFERENT_MISSING
        diffs.append(f"{item}.{difference}")
    for item in list_2:
        difference = DIFFERENT_ADDITIONAL
        diffs.append(f"{item}.{difference}")
    return diffs

def remove_equivalent_items(input_list: list[str]) -> list[str]:
    return [s for s in input_list if not s.endswith(EQUIVALENT)]

def check_if_any_string_in_list_1_starts_with_any_string_in_list_2(
        list_1: list[str],
        list_2: list[str]) -> bool:
    return any(s1.startswith(s2) for s1 in list_1 for s2 in list_2)

def extract_error_string(error_list: list[str], check_list: list[str]) -> list[str]:
    result = []
    for string_to_check in check_list:
        if any(string_to_check.startswith(error) for error in error_list):
            result.append(string_to_check)
    return result

if __name__ == '__main__':
    main()
