"""This module is used to asynchronously wait for a set of machines to boot"""
import time
from dataclasses import dataclass
import requests
from requests import Response
from requests.auth import HTTPBasicAuth
from ansible.module_utils.basic import AnsibleModule
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    create_ansible_module, BaseModuleData, wait_for
)

module: AnsibleModule = None
module_args: dict = {
    "hosts":{"type":'list', "required":True, "elements":'dict'},
    "timeout":{"type":'int', "required":False, "default":1200},
    "power_state":{"type":'str', "required":False, "default":'On'}
}

@dataclass
class BMCInfo:
    def __init__(self, name: str, host: dict):
        self.name: str = name
        base_url: str = f"https://{ host['ipv4_address'] }"
        self.redfish_api: str = f"{base_url}/redfish/v1/Systems/{host['system_id']}"
        self.auth: HTTPBasicAuth = HTTPBasicAuth(
            username=host['username'],
            password=host['password'])

    def api_request(self, **kwargs) -> Response:
        return requests.request(
            auth=self.auth,
            verify=False,
            timeout=60,
            proxies={'http': '','https': ''},
            **kwargs
        )

def create_list_of_bmcs(hosts):
    list_of_bmcs: list[BMCInfo] = []
    for host in hosts:
        list_of_bmcs.append(BMCInfo(host['abstract']['name'], host['bmc']))
    return list_of_bmcs

@dataclass
class ModuleData(BaseModuleData):
    def __init__(self):
        super().__init__(module.params)
        self._hosts: list[BMCInfo] = create_list_of_bmcs(module.params['hosts'])
        self.out: dict[dict] = {}
        self.power_state = module.params['power_state']

    @property
    def hosts(self) -> list[BMCInfo]:
        return self._hosts

def async_wait_for_boot():
    global module
    module = create_ansible_module(module_args)
    module_data = ModuleData()
    wait_for(module_data, wait_for_power_state)

def wait_for_power_state(host: BMCInfo, module_data: ModuleData) -> None:
    module_data.out[host.name] = {"success": None}
    while (
        time.time() - module_data.start_time < module_data.timeout and
        module_data.out[host.name]["success"] is None
    ):
        check_if_server_is_at_desired_state(host, module_data)

    if module_data.out[host.name]["success"] is None:
        module_data.out[host.name] = {
            "stderr": "Timed out waiting for server to boot",
            "success": False
        }

def check_if_server_is_at_desired_state(host: BMCInfo, module_data: ModuleData) -> None:
    try:
        if is_server_at_desired_state(host, module_data.power_state):
            module_data.out[host.name] = {"stdout": f"{host.name} has turned on!", "success": True}
        time.sleep(1)
    except Exception as e:
        module_data.out[host.name] = {
            "stderr": (
                "An exception was thrown when checking"
                f"for server status through redfish api: {e}"
            ),
            "success": False
        }

def is_server_at_desired_state(bmc_info: BMCInfo, power_state: str) -> bool:
    response =  bmc_info.api_request(url=bmc_info.redfish_api, method='GET')
    return response.json().get('PowerState', '') == power_state

if __name__ == '__main__':
    async_wait_for_boot()
