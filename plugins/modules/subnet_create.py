"""<PERSON><PERSON><PERSON> communicates with AWS and creates a subnet."""
import hashlib
from dataclasses import dataclass
import boto3
from ansible.module_utils.basic import AnsibleModule

module: AnsibleModule = None

MODULE_ARGS={
        "vpc_id":{"type":'str', "required":True},
        "az":{"type":'str', "required":True},
        "cidr":{"type":'str', "required":True},
        "name":{"type":'str', "required":True},
    }

def compute_hash() -> str:
    relevant = [
        module.params['vpc_id'],
        module.params['az'],
        module.params['cidr'],
    ]
    h = hashlib.sha256()
    for item in relevant:
        h.update(str(item).encode("utf-8"))
    full_hash = h.hexdigest()
    return full_hash[:16]

@dataclass
class SubnetTag:
    def __init__(self, name: str):
        self.hash = compute_hash()
        self.key: str = f"{name}-hash"
        self.hash_tag: dict = {"Key": self.key, "Value": self.hash}

@dataclass
class Subnet:
    def __init__(self):
        self.name: str = module.params['name']
        self.vpc_id: str = module.params['vpc_id']
        self.az: str = module.params['az']
        self.cidr: str = module.params['cidr']
        self.client = boto3.client('ec2')
        self.tag: SubnetTag = SubnetTag(self.name)
        self.changed: bool = False

    def cleanup_old_resources(self) -> None:
        depecrated_subnets: list[str] = self.get_depecrated_subnets()
        self.delete_depecrated_subnets(depecrated_subnets)

    def get_depecrated_subnets(self) -> list[str]:
        depecrated_subnet_ids = []
        subnet_resp = self.client.describe_subnets(
            Filters=[
                {
                    "Name": "tag-key",
                    "Values": [self.tag.key]
                }
            ]
        )
        for subnet in subnet_resp.get("Subnets", []):
            subnet_id = subnet['SubnetId']
            tags = {tag['Key']: tag['Value'] for tag in subnet.get('Tags', [])}
            if tags.get(self.tag.key) != self.tag.hash:
                depecrated_subnet_ids.append(subnet_id)
        return depecrated_subnet_ids

    def delete_depecrated_subnets(self, depecrated_subnets: list[str]) -> None:
        for subnet_id in depecrated_subnets:
            self.client.delete_subnet(SubnetId=subnet_id)

    def set_subnet(self):
        existing_sg_id = self.find_existing_subnet()
        if existing_sg_id:
            return existing_sg_id
        return self.create_security_group()

    def find_existing_subnet(self) -> str:
        resp = self.client.describe_subnets(
            Filters=[
                {"Name": f"tag:{self.tag.key}", "Values": [self.tag.hash]}
            ]
        )
        subnets = resp.get("Subnets", [])
        if len(subnets) > 0:
            return subnets[0]["SubnetId"]
        return None

    def create_security_group(self):
        create_resp = self.client.create_subnet(
            AvailabilityZone=self.az,
            VpcId=self.vpc_id,
            CidrBlock=self.cidr
        )
        subnet_id = create_resp["Subnet"]["SubnetId"]
        self.client.create_tags(
            Resources=[subnet_id],
            Tags=[
                self.tag.hash_tag,
                {"Key": "Name", "Value": self.name}
            ]
        )
        self.changed = True
        return subnet_id

def create_ansible_module() -> AnsibleModule:
    module_args: dict = MODULE_ARGS
    return AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

def subnet_create() -> None:
    global module
    module = create_ansible_module()
    subnet: Subnet = Subnet()
    subnet.cleanup_old_resources()
    subnet_id = subnet.set_subnet()
    msg: str =f"Successfully launched subnet with hash={subnet.tag.hash}"
    module.exit_json(changed=subnet.changed, msg=msg, id=subnet_id)

if __name__ == '__main__':
    subnet_create()
