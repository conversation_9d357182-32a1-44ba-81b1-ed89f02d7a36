"""This module communicates with the redfish api to set nic boot order"""
import json
from dataclasses import dataclass
import requests
from ansible.module_utils.basic import AnsibleModule
from requests import Response
from requests.auth import HTTPBasicAuth
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    create_ansible_module
)

module: AnsibleModule = None
module_args = {
    "username":{"type":'str', "required":True, "no_log":True},
    "password":{"type":'str', "required":True, "no_log":True},
    "ipv4_address":{"type":'str', "required":True, "no_log":True},
    "boot_mac":{"type":'str', "required":True},
    "system_id":{"type":'str', "required":True},
    "manager_id":{"type":'str', "required":False, "default":"1"},
}

@dataclass
class BMCInfo:
    def __init__(self, module_params):
        self.boot_mac: str = module_params['boot_mac']
        self.redfish_base_api = f"https://{module_params['ipv4_address']}/redfish/v1"
        self.redfish_api = f"{self.redfish_base_api}/Systems/{module_params['system_id']}"
        self.auth = HTTPBasicAuth(
            username=module_params['username'],
            password=module_params['password']
        )
        self.manager_id = module_params['manager_id']
        self.boot_sources_url = get_boot_url(self)
        self.persistent_boot_config_uri = f"{self.boot_sources_url}Settings/"

    def api_request(self, **kwargs) -> Response:
        return requests.request(
            auth=self.auth,
            verify=False,
            proxies={'http': '','https': ''},
            **kwargs,
            timeout=60
        )

def set_nic_boot_order() -> None:
    global module
    module = create_ansible_module(module_args)
    bmc_info = BMCInfo(module.params)
    boot_sources = get_boot_sources(bmc_info)
    structured_boot_string = determine_structure_boot_string_with_provided_mac(
        boot_sources, bmc_info.boot_mac
    )
    old_persisten_boot_config = get_old_persisten_boot_config(bmc_info)
    new_persisten_boot_config = reorder_persisten_boot_config(
        structured_boot_string,
        old_persisten_boot_config
    )
    send_updated_persistent_boot_config(new_persisten_boot_config, bmc_info)
    module.exit_json(changed=True)

def get_boot_sources(bmc_info: BMCInfo) -> Response:
    try:
        response =  bmc_info.api_request(url=bmc_info.boot_sources_url, method='GET')
        response_json = response.json()['BootSources']
    except Exception:
        msg=(
            "An error occured when trying to get the boot sources from"
            f"[{bmc_info.boot_sources_url}] the response was [{str(response)}]"
        )
        module.fail_json(msg=msg)
    return response_json

def get_boot_url(bmc_info: BMCInfo) -> str:
    ilo_major_version = get_bmc_firmware_version(bmc_info).split(' ')[1]
    if ilo_major_version in ['4', '5']:
        return f"{bmc_info.redfish_api}/bios/boot/"
    if ilo_major_version == '6':
        return f"{bmc_info.redfish_api}/bios/oem/hpe/boot/"
    raise TypeError(f"Unsupported BMC: ILO version: {ilo_major_version}")

def get_bmc_firmware_version(bmc_info: BMCInfo) -> str:
    bmc_version_url = f"{bmc_info.redfish_base_api}/Managers/{bmc_info.manager_id}/"
    api_response = requests.get(
        url=bmc_version_url,
        auth=bmc_info.auth,
        verify=False,
        proxies={'http': '','https': ''},
        timeout=60
        )
    return api_response.json()['FirmwareVersion']

def determine_structure_boot_string_with_provided_mac(boot_sources: list, boot_mac: str) -> str:
    structured_boot_string = None
    formatted_mac = boot_mac.upper().replace(':','')
    for bootsource in boot_sources:
        if (
            formatted_mac in bootsource.get('UEFIDevicePath') and
            'IPv4' in bootsource.get('StructuredBootString')
        ):
            structured_boot_string = bootsource.get('StructuredBootString')
    if structured_boot_string is None:
        module.fail_json(msg=f"No PXE boot NIC found with provided mac address: {boot_mac}")
    return structured_boot_string

def get_old_persisten_boot_config(bmc_info: BMCInfo) -> str:
    try:
        response = bmc_info.api_request(url=bmc_info.persistent_boot_config_uri, method='GET')
        response_json = response.json()['PersistentBootConfigOrder']
    except Exception:
        msg = (
            "An error occured trying to get the persistent boot config from"
            f"[{bmc_info.persistent_boot_config_uri}] the response was [{str(response)}]"
        )
        module.fail_json(msg=msg)
    return response_json

def reorder_persisten_boot_config(structured_boot_string, old_persisten_boot_config) -> str:
    new_persisten_boot_config = []
    for boot_string in old_persisten_boot_config:
        if "NIC" not in boot_string or structured_boot_string == boot_string:
            new_persisten_boot_config.append(boot_string)
    return new_persisten_boot_config

def send_updated_persistent_boot_config(new_persisten_boot_config, bmc_info: BMCInfo) -> None:
    data = {"PersistentBootConfigOrder": new_persisten_boot_config}
    response = bmc_info.api_request(
        method='PATCH',
        url=bmc_info.persistent_boot_config_uri,
        data=json.dumps(data),
        headers={"Content-Type": "application/json"},
        )
    if response.status_code != 200:
        msg = (
            f"An error occured trying set the boot config to "
            f"[{str(new_persisten_boot_config)}] at [{bmc_info.persistent_boot_config_uri}]"
        )
        module.fail_json(msg=msg)

if __name__ == '__main__':
    set_nic_boot_order()
