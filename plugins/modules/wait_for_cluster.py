"""This module waits for all cluster operators to be up and running"""
import time
from dataclasses import dataclass
from ansible.module_utils.basic import AnsibleModule
from kubernetes import client, config

module: AnsibleModule = None
MAX_NUMBER_OF_FAILED_ATTEMPTS = 5

def main():
    module_args = define_module_args()
    global module
    module = create_ansible_module(module_args)
    connection_data = extract_connection_data(module.params)
    connection_config = create_connection_config(connection_data)
    client.Configuration.set_default(connection_config)
    custom_resource_api = client.CustomObjectsApi()
    wait_for_cluster_operators(custom_resource_api)

def create_ansible_module(module_args):
    return AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

def define_module_args():
    module_args = {
        "kubeconfig_file":{"type":'str', "required":False},
        "host":{"type":'str', "required":False},
        "ca_cert":{"type":'str', "required":False},
        "verify_ssl":{"type":'bool', "required":False},
        "api_key":{"type":'str', "required":False},
        "retries":{"type":'int', "required":False, "default":180},
        "delay":{"type":'int', "required":False, "default":20},
        "initial_delay":{"type":'int', "required":False, "default":0}
    }
    return module_args

def create_connection_config(connection_data):
    connection_config = client.Configuration()
    if connection_data.kubeconfig_file:
        config.load_kube_config(
            config_file=connection_data.kubeconfig_file,
            client_configuration=connection_config
        )
    elif connection_data.host and connection_data.api_key:
        set_config_with_api_key(connection_config, connection_data)
    else:
        module.fail_json(msg="Either kubeconfig_file or host and api_key must be defined.")
    if connection_data.ca_cert and connection_config.ssl_ca_cert is None:
        connection_config.ssl_ca_cert = connection_data.ca_cert
    if connection_data.verify_ssl is not None:
        connection_config.verify_ssl = connection_data.verify_ssl
    return connection_config

def set_config_with_api_key(connection_config, connection_data):
    connection_config.host = connection_data.host
    connection_config.api_key["authorization"] = connection_data.api_key
    connection_config.api_key_prefix['authorization'] = 'Bearer'

def extract_connection_data(module_parameters):
    return ConnectionData(module_parameters)

def wait_for_cluster_operators(custom_resource_api):
    fails = 0
    time.sleep(module.params['initial_delay'])
    for _ in range(module.params['retries']):
        try:
            list_of_cluster_operators = get_list_of_operator_data_from_api(custom_resource_api)
            if (all_operators_are_ready(list_of_cluster_operators) and
                are_master_mcps_done_updating(custom_resource_api) and
                are_worker_mcps_done_updating(custom_resource_api)):
                module.exit_json(changed=False)
        except Exception as e:
            if fails > MAX_NUMBER_OF_FAILED_ATTEMPTS:
                module.fail_json(msg=str(e))
            else:
                fails = fails + 1
        time.sleep(module.params['delay'])
    module.fail_json(msg="Cluster timed out and never became fully available")

def all_operators_are_ready(list_of_cluster_operators):
    return all(
        any(
            condition['type'] == 'Available' and
            condition['status'] == 'True' for condition in cluster_operator['status']['conditions']
        ) and
        any(
            condition['type'] == 'Progressing' and
            condition['status'] == 'False' for condition in cluster_operator['status']['conditions']
        )
        for cluster_operator in list_of_cluster_operators
    )

def get_list_of_operator_data_from_api(custom_resource_api):
    cluster_operator_info = custom_resource_api.list_cluster_custom_object(
        group="config.openshift.io",
        version="v1",
        plural="clusteroperators"
    )
    return cluster_operator_info['items']

def are_master_mcps_done_updating(custom_resource_api):
    name = "master"
    return get_cluster_custom_object(custom_resource_api, name)

def are_worker_mcps_done_updating(custom_resource_api):
    name = "worker"
    return get_cluster_custom_object(custom_resource_api, name)

def get_cluster_custom_object(custom_resource_api, name):
    cluster_operator_info = custom_resource_api.get_cluster_custom_object(
        group="machineconfiguration.openshift.io",
        version="v1",
        plural="machineconfigpools",
        name=name
    )
    conditions = cluster_operator_info['status']['conditions']
    updating_condition = [
        condition for condition in conditions if condition['type'] == 'Updating'
    ][0]
    return updating_condition['status'] == 'False'

@dataclass
class ConnectionData:
    def __init__(self, module_params):
        self.api_key = module_params['api_key']
        self.kubeconfig_file = module_params['kubeconfig_file']
        self.ca_cert = module_params['ca_cert']
        self.verify_ssl = module_params['verify_ssl']
        self.host = module_params['host']

if __name__ == '__main__':
    main()
