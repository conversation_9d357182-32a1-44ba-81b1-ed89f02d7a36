"""<PERSON><PERSON><PERSON> communicates with AWS and and removes a previous openshift cluster by tags."""
import time
import boto3
from ansible.module_utils.basic import AnsibleModule
from botocore.exceptions import ClientError

module: AnsibleModule = None

MODULE_ARGS={
    "cluster":{"type":'str', "required":True},
}
TAG_KEY="galaxy-cluster"
LOAD_BALANCER_DELETION_TIMEOUT=30
LOAD_BALANCER_DELETION_WAIT_INTERVAL=5
r53_client = boto3.client('route53')
elbv2_client = boto3.client('elbv2')
ec2_client = boto3.client('ec2')
ec2_resource = boto3.resource('ec2')
s3_resource = boto3.resource('s3')
s3_client = boto3.client('s3')
iam_client = boto3.client('iam')

def create_ansible_module() -> AnsibleModule:
    module_args: dict = MODULE_ARGS
    return AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

def remove_r53_resources(tag_value: str):
    paginator = r53_client.get_paginator('list_hosted_zones')
    for page in paginator.paginate():
        for zone in page['HostedZones']:
            remove_zones_with_matching_tags(tag_value, zone)

def remove_zones_with_matching_tags(tag_value, zone):
    zone_id = zone['Id'].split('/')[-1]
    zone_tags = get_all_zone_tags(zone_id)
    for tag in zone_tags:
        if tag.get('Key') == TAG_KEY and tag.get('Value') == tag_value:
            remove_hosted_zone(zone_id)
            break

def get_all_zone_tags(zone_id):
    tags_response = r53_client.list_tags_for_resource(
        ResourceType='hostedzone',
        ResourceId=zone_id
    )
    return tags_response.get('ResourceTagSet', {}).get('Tags', [])

def remove_hosted_zone(zone_id):
    record_sets = r53_client.list_resource_record_sets(HostedZoneId=zone_id)['ResourceRecordSets']
    for record in record_sets:
        if record['Type'] not in ['SOA', 'NS']:
            delete_record_set(zone_id, record)
    r53_client.delete_hosted_zone(Id=zone_id)

def delete_record_set(zone_id, record):
    r53_client.change_resource_record_sets(
        HostedZoneId=zone_id,
        ChangeBatch={
            'Changes': [{
                'Action': 'DELETE',
                'ResourceRecordSet': record
            }]
        }
    )

def remove_elbv2_resources(tag_value):
    deleted_lb_arns = remove_load_balancers(tag_value)
    wait_for_load_balancers_deletion(deleted_lb_arns)
    remove_target_groups(tag_value)

def remove_load_balancers(tag_value):
    deleted_lb_arns = []
    paginator = elbv2_client.get_paginator('describe_load_balancers')
    for page in paginator.paginate():
        for lb in page['LoadBalancers']:
            lb_arn = lb['LoadBalancerArn']
            lb_tags = elbv2_client.describe_tags(ResourceArns=[lb_arn])['TagDescriptions']
            for tag_desc in lb_tags:
                for tag in tag_desc['Tags']:
                    if tag.get('Key') == TAG_KEY and tag.get('Value') == tag_value:
                        elbv2_client.delete_load_balancer(LoadBalancerArn=lb_arn)
                        deleted_lb_arns.append(lb_arn)
                        break
    return deleted_lb_arns

def wait_for_load_balancers_deletion(deleted_lb_arns):
    remaining_arns = set(deleted_lb_arns)
    start_time = time.time()
    max_time = start_time + LOAD_BALANCER_DELETION_TIMEOUT
    while remaining_arns and time.time() < max_time:
        time.sleep(LOAD_BALANCER_DELETION_WAIT_INTERVAL)
        response = elbv2_client.describe_load_balancers()
        existing_arns = {lb["LoadBalancerArn"] for lb in response.get("LoadBalancers", [])}
        remaining_arns.intersection_update(existing_arns)
    if remaining_arns:
        module.fail_json(f"Timed out waiting for load balancers to be deleted: {deleted_lb_arns}")

def remove_target_groups(tag_value):
    paginator = elbv2_client.get_paginator('describe_target_groups')
    for page in paginator.paginate():
        for tg in page['TargetGroups']:
            remove_target_group_with_matching_tags(tag_value, tg)

def remove_target_group_with_matching_tags(tag_value, tg):
    tg_arn = tg['TargetGroupArn']
    tags_response = elbv2_client.describe_tags(ResourceArns=[tg_arn])
    for tag_desc in tags_response['TagDescriptions']:
        for tag in tag_desc['Tags']:
            if tag['Key'] == TAG_KEY and tag['Value'] == tag_value:
                elbv2_client.delete_target_group(TargetGroupArn=tg_arn)
                break

def remove_ec2_resources(tag_value):
    instances = get_instances_with_tag(tag_value)
    if instances:
        terminate_instances(instances)
    security_groups = get_sgs_with_tag(tag_value)

    enis = ec2_client.describe_network_interfaces(
        Filters=[
            {
                'Name': f'tag:{TAG_KEY}',
                'Values': [tag_value]
            }
        ]
    )['NetworkInterfaces']

    for eni in enis:
        if 'Attachment' in eni and eni['Attachment']:
            ec2_client.detach_network_interface(
                AttachmentId=eni['Attachment']['AttachmentId'],
                Force=True
            )
        eni_id = eni['NetworkInterfaceId']
        ec2_client.delete_network_interface(NetworkInterfaceId=eni_id)

    for sg in security_groups:
        # detach_enis_from_sgs(enis, sg.id)
        clear_security_group_rules(sg.id)
    for sg in security_groups:
        sg.delete()
    volumes = get_volumes_with_tag(tag_value)
    for volume in volumes:
        volume.delete()

def clear_security_group_rules(sg_id):
    sg_info = ec2_client.describe_security_groups(GroupIds=[sg_id])['SecurityGroups'][0]
    if sg_info.get('IpPermissions'):
        ec2_client.revoke_security_group_ingress(
            GroupId=sg_id,
            IpPermissions=sg_info['IpPermissions']
        )
    if sg_info.get('IpPermissionsEgress'):
        ec2_client.revoke_security_group_egress(
            GroupId=sg_id,
            IpPermissions=sg_info['IpPermissionsEgress']
        )

def detach_enis_from_sgs(enis, sg_id):
    for eni in enis:
        if 'Groups' in eni:
            for group in eni['Groups']:
                if group['GroupId'] == sg_id:
                    ec2_client.modify_network_interface_attribute(
                        NetworkInterfaceId=eni['NetworkInterfaceId'],
                        Groups=[]
                    )

def terminate_instances(instances):
    instance_ids = [instance.id for instance in instances]
    ec2_client.terminate_instances(InstanceIds=instance_ids)
    waiter = ec2_client.get_waiter('instance_terminated')
    waiter.wait(InstanceIds=instance_ids)

def get_instances_with_tag(tag_value):
    return list(ec2_resource.instances.filter(
        Filters=[
            {
                'Name': f'tag:{TAG_KEY}',
                'Values': [tag_value]
            },
            {
                'Name': 'instance-state-name',
                'Values': ['pending', 'running', 'stopping', 'stopped']
            }
        ]
    ))

def get_sgs_with_tag(tag_value):
    return list(ec2_resource.security_groups.filter(
        Filters=[
            {
                'Name': f'tag:{TAG_KEY}',
                'Values': [tag_value]
            }
        ]
    ))

def get_volumes_with_tag(tag_value):
    return list(ec2_resource.volumes.filter(
        Filters=[
            {
                'Name': f'tag:{TAG_KEY}',
                'Values': [tag_value]
            },
            {
                'Name': 'status',
                'Values': ['available']
            }
        ]
    ))

def remove_s3_resources(tag_value):
    buckets = s3_client.list_buckets()['Buckets']
    for bucket in buckets:
        bucket_name = bucket['Name']
        try:
            bucket_tags = s3_client.get_bucket_tagging(Bucket=bucket_name)['TagSet']
        except s3_client.exceptions.ClientError:
            bucket_tags = []
        for tag in bucket_tags:
            if tag.get('Key') == TAG_KEY and tag.get('Value') == tag_value:
                empty_and_delete_bucket(bucket_name)
                break

def empty_and_delete_bucket(bucket_name):
    bucket_obj = s3_resource.Bucket(bucket_name)
    bucket_obj.objects.all().delete()
    bucket_versioning = s3_client.get_bucket_versioning(Bucket=bucket_name)
    if 'Status' in bucket_versioning and bucket_versioning['Status'] == 'Enabled':
        bucket_obj.object_versions.all().delete()
    bucket_obj.delete()

def remove_iam_resources(tag_value):
    paginator = iam_client.get_paginator('list_roles')
    for page in paginator.paginate():
        for role in page['Roles']:
            role_name = role['RoleName']
            role_tags = iam_client.list_role_tags(RoleName=role_name).get('Tags', [])
            for tag in role_tags:
                if tag.get('Key') == TAG_KEY and tag.get('Value') == tag_value:
                    delete_all_attached_policies(role_name)
                    delete_all_inline_policies(role_name)
                    delete_all_instance_profiles(role_name)
                    iam_client.delete_role(RoleName=role_name)
                    break

def delete_all_attached_policies(role_name):
    attached_policies = iam_client.list_attached_role_policies(RoleName=role_name)
    for policy in attached_policies.get('AttachedPolicies', []):
        iam_client.detach_role_policy(
            RoleName=role_name,
            PolicyArn=policy['PolicyArn']
        )

def delete_all_inline_policies(role_name):
    inline_policies = iam_client.list_role_policies(RoleName=role_name)
    for policy_name in inline_policies.get('PolicyNames', []):
        iam_client.delete_role_policy(
            RoleName=role_name,
            PolicyName=policy_name
        )

def delete_all_instance_profiles(role_name):
    instance_profiles = iam_client.list_instance_profiles_for_role(RoleName=role_name)
    for profile in instance_profiles.get('InstanceProfiles', []):
        iam_client.remove_role_from_instance_profile(
            InstanceProfileName=profile['InstanceProfileName'],
            RoleName=role_name
        )
        iam_client.delete_instance_profile(
            InstanceProfileName=profile['InstanceProfileName']
        )

def delete_clean_users(cluster_name):
    """Deletes users with the given cluster name and cleans up associated resources."""
    paginator = iam_client.get_paginator('list_users')
    for page in paginator.paginate():
        for user in page.get('Users', []):
            username = user['UserName']
            if not username.startswith(cluster_name):
                continue
            try:
                detach_user_policies(username)
                delete_inline_policies(username)
                delete_access_keys(username)
                delete_signing_certificates(username)
                delete_ssh_public_keys(username)
                delete_user(username)
            except ClientError as e:
                print(f"Error deleting user {username}: {e}")


def detach_user_policies(username):
    """Detaches policies from the given user."""
    policies = iam_client.list_attached_user_policies(UserName=username)
    for policy in policies.get('AttachedPolicies', []):
        iam_client.detach_user_policy(
            UserName=username,
            PolicyArn=policy['PolicyArn']
        )


def delete_inline_policies(username):
    """Deletes inline policies for the given user."""
    policies = iam_client.list_user_policies(UserName=username)
    for name in policies.get('PolicyNames', []):
        iam_client.delete_user_policy(
            UserName=username,
            PolicyName=name
        )


def delete_access_keys(username):
    """Deletes access keys for the given user."""
    keys = iam_client.list_access_keys(UserName=username)
    for key in keys.get('AccessKeyMetadata', []):
        iam_client.delete_access_key(
            UserName=username,
            AccessKeyId=key['AccessKeyId']
        )


def delete_signing_certificates(username):
    """Deletes signing certificates for the given user."""
    certs = iam_client.list_signing_certificates(UserName=username)
    for cert in certs.get('Certificates', []):
        iam_client.delete_signing_certificate(
            UserName=username,
            CertificateId=cert['CertificateId']
        )


def delete_ssh_public_keys(username):
    """Deletes SSH public keys for the given user."""
    ssh_keys = iam_client.list_ssh_public_keys(UserName=username)
    for key in ssh_keys.get('SSHPublicKeys', []):
        iam_client.delete_ssh_public_key(
            UserName=username,
            SSHPublicKeyId=key['SSHPublicKeyId']
        )


def delete_user(username):
    """Deletes the given user."""
    iam_client.delete_user(UserName=username)


def cluster_remove() -> None:
    global module
    module = create_ansible_module()
    cluster_to_delete = module.params['cluster']
    remove_r53_resources(cluster_to_delete)
    remove_elbv2_resources(cluster_to_delete)
    remove_ec2_resources(cluster_to_delete)
    remove_s3_resources(cluster_to_delete)
    remove_iam_resources(cluster_to_delete)
    delete_clean_users(cluster_to_delete)
    msg = "Successfully ensured cluster doesn't exist"
    module.exit_json(changed=True, msg=msg)

if __name__ == '__main__':
    cluster_remove()
