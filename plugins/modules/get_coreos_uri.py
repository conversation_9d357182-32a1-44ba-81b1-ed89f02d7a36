"""This module gets the coreos file for openshift install"""
import subprocess
import json
import os
from ansible.module_utils.basic import AnsibleModule

module: AnsibleModule = None
coreos_info_command: str = "openshift-install coreos print-stream-json"

def main():
    global module
    module = create_ansible_module()
    coreos_info_string = get_coreos_info_json()
    coreos_info_json = convert_string_to_json(coreos_info_string)
    coreos_property = module.params['coreos_property']
    result = get_coreos_property_from_json(coreos_info_json, coreos_property)
    module.exit_json(**result)

def convert_string_to_json(coreos_info_string):
    try:
        json_dict = json.loads(coreos_info_string)
    except json.JSONDecodeError:
        module.fail_json(msg="JSON Returned by openshift-install was invalid")
    return json_dict

def create_ansible_module():
    module_args = {
        "coreos_property":{
            "type":'str',
            "required":True,
            "choices":[
                'location',
                'uncompressed-sha256',
                'sha256'
            ]
        }
    }
    return AnsibleModule(argument_spec=module_args)

def get_coreos_property_from_json(json_output, coreos_property):
    try:
        formats = (json_output.get('architectures')
                                    .get('x86_64')
                                    .get('artifacts')
                                    .get('qemu')
                                    .get('formats'))
        qcow = [value for key, value in formats.items() if key.startswith('qcow2')][0]
        requested_data = (qcow.get('disk')
                              .get(coreos_property))
    except AttributeError:
        module.fail_json(msg="JSON did not contain expected keys")
    return {"changed":False, "value":requested_data}

def get_env_with_correct_path():
    env = os.environ.copy()
    path_to_add = env["HOME"] + "/.local/bin"
    env["PATH"] = path_to_add + os.pathsep + env["PATH"]
    return env

def get_coreos_info_json():
    env = get_env_with_correct_path()
    with subprocess.Popen(
        coreos_info_command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=True,
        env=env
    ) as process:
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            module.fail_json(msg=stderr)
    return stdout.rstrip()

if __name__ == '__main__':
    main()
