"""This module validates that certs have been correctly configured"""
from enum import Enum
import re
from dataclasses import dataclass
from ansible.module_utils.basic import AnsibleModule
from OpenSSL import crypto, SSL
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    create_ansible_module
)

module: AnsibleModule = None
module_args = {
    "cert_data":{"type":'str', "required":True, "no_log":True},
    "key_data":{"type":'str', "required":True, "no_log":True},
    "ca_data":{"type":'str', "required":True, "no_log":True},
    "domains":{"type":'list', "required":True}
}

@dataclass
class ModuleData:
    def __init__(self, module_params):
        self.cert: CertificateData = CertificateData(
            'cert_data',
            DataType.CERT,
            module_params['cert_data']
        )
        self.key: CertificateData = CertificateData(
            'key_data',
            DataType.KEY,
            module_params['key_data']
        )
        self.ca: CertificateData = CertificateData(
            'ca_data',
            DataType.CERT,
            module_params['ca_data']
        )
        self.domains: list[str] = module_params['domains']
        self.list_of_certs: list[CertificateData] = [
            self.cert,
            self.key,
            self.ca
        ]

    def get_data(self) -> None:
        for cert in self.list_of_certs:
            cert.data = load_data(cert)

@dataclass
class CertificateData:
    def __init__(self, name: str, data_type: str, raw_data: str):
        self.name: str = name
        self.data_type: str = data_type
        self.raw_data: str = raw_data
        self.data = None
        self.crypto_load_function = function_mapping[self.data_type]

class DataType(Enum):
    KEY = "key"
    CERT = "cert"

def load_certs(cert_type: str, raw_data: str):
    pattern = r'(-----BEGIN CERTIFICATE-----.*?-----END CERTIFICATE-----)'
    certs = re.findall(pattern, raw_data, re.DOTALL)
    x509s = []
    for cert in certs:
        x509s.append(crypto.load_certificate(cert_type, cert))
    return x509s

function_mapping = {
    DataType.KEY: crypto.load_privatekey,
    DataType.CERT: load_certs
}

def main() -> None:
    global module
    module = create_ansible_module(module_args)
    module_data = ModuleData(module.params)
    module_data.get_data()
    check_key_cert_match(module_data)
    check_sans(module_data)
    check_ca_signs_cert(module_data)
    module.exit_json(changed=False, msg="Certs are verified")

def check_key_cert_match(module_data: ModuleData):
    try:
        context = SSL.Context(SSL.TLSv1_2_METHOD)
        context.use_privatekey(module_data.key.data)
        context.use_certificate(module_data.cert.data[0])
        context.check_privatekey()
    except Exception as e:
        msg=(
            "Error confirming cert provided is signed by "
            f"key provided. Exception thrown was:\n{str(e)}"
        )
        module.fail_json(msg=msg)

def check_sans(module_data: ModuleData):
    try:
        san_list = get_sanlist(module_data.cert.data[0])
    except Exception as e:
        msg=(
            "Error confirming domains provided are in provided cert SANs list."
             f"Exception thrown was:\n{str(e)}"
        )
        module.fail_json(msg=msg)
    for domain in module_data.domains:
        if domain not in san_list:
            msg=f"Domain {domain} not in cert provided SAN list. Cert SAN list: {san_list}"
            module.fail_json(msg=msg)

def get_sanlist(certificate: crypto.X509) -> list[str]:
    san_list = []
    for extension_index in range(certificate.get_extension_count()):
        extension = certificate.get_extension(extension_index)
        if 'subjectAltName' in str(extension.get_short_name()):
            sans = str(extension)
            san_list.extend(
                [san.split(':')[1].strip() for san in sans.split(', ') if san.startswith('DNS')]
            )
    return san_list

def check_ca_signs_cert(module_data: ModuleData) -> None:
    try:
        store = crypto.X509Store()
        for cert in module_data.ca.data:
            store.add_cert(cert)
        for cert in module_data.cert.data:
            store_ctx = crypto.X509StoreContext(store, cert)
            store_ctx.verify_certificate()
    except Exception as e:
        msg=(
            "Error confirming cert provided is signed by ca provided."
            f"Exception thrown was:\n{str(e)}"
        )
        module.fail_json(msg=msg)

def load_data(cert: CertificateData):
    try:
        cert_data = cert.crypto_load_function(crypto.FILETYPE_PEM, cert.raw_data)
    except Exception as e:
        msg=f"Error trying to load crypto data for {cert.name}. Exception thrown was:\n{str(e)}"
        module.fail_json(msg=msg)
    return cert_data

if __name__ == '__main__':
    main()
