"""This module asynchronously waits for several nodes to have their ssh port open"""
import socket
import time
import subprocess
from dataclasses import dataclass
from ansible.module_utils.basic import AnsibleModule
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    create_ansible_module, BaseModuleData, wait_for
)

module: AnsibleModule = None
module_args: dict = {
    "hosts":{"type":'list', "required":True},
    "command":{"type":'str', "required":False, "default":None},
    "successful_outputs":{"type":'str', "required":False, "default":""},
    "timeout":{"type":'int', "required":False, "default":3600},
    "ssh_port_state":{
        "type":'str',
        "required":False,
        "default":'open',
        "choices":['open', 'closed']},
}

@dataclass
class ModuleData(BaseModuleData):
    def __init__(self, module_params: dict):
        super().__init__(module.params)
        self._hosts: list = module_params['hosts']
        if module_params['ssh_port_state'] == 'open':
            self.command: str = module_params['command']
            self.successful_outputs: list[str] = module_params['successful_outputs']
        else:
            self.command: str = None
            self.successful_outputs: list[str] = []
        self.ssh_port_state: str = module_params['ssh_port_state']

    @property
    def hosts(self) -> list[str]:
        return self._hosts

def async_wait_for_ssh() -> None:
    global module
    module = create_ansible_module(module_args)
    module_data = ModuleData(module.params)
    wait_for(module_data, monitor_ip)

def monitor_ip(host, module_data: ModuleData) -> None:
    while time.time() - module_data.start_time < module_data.timeout:
        if ssh_port_state(host) == module_data.ssh_port_state:
            if module_data.command is not None:
                module_data.out[host] = run_ssh_task(host, module_data)
            else:
                module_data.out[host] = {"success": True}
            return
        time.sleep(1)
    module_data.out[host] = {"stderr": "Timed out waiting for port 22 to open.", "success": False}

def ssh_port_state(host: str) -> str:
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
        sock.settimeout(1)
        result = sock.connect_ex((host, 22))
        return 'open' if result == 0 else 'closed'

def run_ssh_task(host: str, module_data: ModuleData) -> dict:
    result = run_ssh_command(host, module_data.command)
    success = determine_success(result, module_data.successful_outputs)
    return {
        "stdout": result.stdout,
        "stderr": result.stderr,
        "args": result.args,
        "success": success
    }

def determine_success(result: subprocess.CompletedProcess, successful_outputs) -> bool:
    for succesful_output in successful_outputs:
        if succesful_output in result.stdout:
            return True
    if result.returncode == 0:
        return True
    return False

def run_ssh_command(host: str, command: str) -> subprocess.CompletedProcess:
    command_args = [
        "ssh",
        "-o",
        "StrictHostKeyChecking=no",
        "-o",
        "UserKNownHostsFile=/dev/null",
        f"core@{host}",
        command
    ]
    try:
        time.sleep(10)
        result = run_command(command_args)
        return result
    except subprocess.TimeoutExpired:
        return subprocess.CompletedProcess(command_args, 1)

def run_command(command_args) -> subprocess.CompletedProcess:
    return subprocess.run(
                command_args,
                text=True,
                capture_output=True,
                timeout=15,
                check=False
            )

if __name__ == '__main__':
    async_wait_for_ssh()
