"""This MOdule filters unused yum packages from the list of all yum pakcages sbom"""
#!/usr/bin/python
import json
from ansible.module_utils.basic import AnsibleModule

DOCUMENTATION = r'''
---
module: sbom_file_processor
short_description: Slim an SBOM down to packages pertaining to relevant architecture
description:
    - This module takes in an SBOM and a list of packages, removes unused references, and writes the result to an output file.
    - It allows specifying the mode and owner of the output file.
options:
    sbom:
        description: Path to the first input file
        required: true
        type: str
    package_list:
        description: List of packages to include
        required: true
        type: list
    output_sbom:
        description: Path to the output file
        required: true
        type: str
    mode:
        description: Mode of the output file
        required: false
        type: str
        default: '0644'
    owner:
        description: Owner of the output file
        required: false
        type: str
    group:
        description: Group of the output file
        required: false
        type: str
'''

EXAMPLES = r'''
- name: Process files and write output
  custom_file_processor:
    sbom: /path/to/sbom.json
    package_list:
      - package1
      - package1
    output_sbom: /path/to/output_sbom.json
    mode: '0644'
    owner: user
    group: usergroup
'''

RETURN = r'''
message:
    description: Informational message about the operation
    type: str
    returned: always
    sample: "Output file created successfully"
'''

def filter_sbom(sbom, packages):
    # Filter the SPDX JSON based on the package filenames
    # in the packages list using list comprehension
    filtered_packages = [
        package for package in sbom['packages'] if package.get('packageFileName', '') in packages
    ]

    # Get the set of SPDXIDs of the removed packages
    removed_spdx_ids = {
        package['SPDXID'] for package in sbom['packages'] if package not in filtered_packages
    }

    # Filter the relationships array to remove the relationships tied to the removed packages
    filtered_relationships = [relationship for relationship in sbom['relationships']
                            if relationship['spdxElementId'] not in removed_spdx_ids
                            or relationship['relatedSpdxElement'] not in removed_spdx_ids]

    # Filter the license info array to keep only the licenses that pertain to the filtered_packages
    filtered_licensing_infos = [
        li for li in sbom['hasExtractedLicensingInfos']
        if any(
            li['licenseId'] in package['licenseDeclared'].split(' AND ')
            for package in filtered_packages
        )
    ]

    sbom['packages'] = filtered_packages
    sbom['relationships'] = filtered_relationships
    sbom['hasExtractedLicensingInfos'] = filtered_licensing_infos

    return sbom


def run_module():
    module_args = {
        "sbom":{"type":'str', "required":True},
        "package_list":{"type":'list', "required":True},
        "output_sbom":{"type":'str', 'required':True},
        "mode":{"type":'str', "default":'0644'},
        "owner":{"type":'str'},
        "group":{"type":'str'}
    }

    result = {
        "changed":False,
        "message":''
    }

    module = AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

    if module.check_mode:
        module.exit_json(**result)

    # Read input file
    try:
        with open(module.params['sbom'], 'r', encoding="utf-8") as f1:
            sbom_content = json.load(f1)
    except IOError as e:
        module.fail_json(msg=f"Error reading input files: {str(e)}", **result)

    # Process content
    output_content = filter_sbom(sbom_content, module.params['package_list'])

    # Write output file
    try:
        with open(module.params['output_sbom'], 'w', encoding="utf-8") as out_file:
            json.dump(output_content, out_file, indent=2)
    except IOError as e:
        module.fail_json(msg=f"Error writing output file: {str(e)}", **result)

    result['changed'] = True
    result['message'] = "Output file created successfully"
    module.exit_json(**result)

def main():
    # import debugpy
    # debugpy.listen(5678)
    # debugpy.wait_for_client()
    # debugpy.breakpoint()

    run_module()

if __name__ == '__main__':
    main()
