"""This module is used to prompt the user to log in to captive portal"""
import os
import subprocess
from dataclasses import dataclass
from re import Match, search
from ansible.module_utils.basic import AnsibleModule
from requests import Session, Response

CSRF_TOKEN_PATTERN = r'<input id="xsauth_token"[^>]*value="([^"]*)"'
HEARTBEAT_MESSAGE_PATTERN = r'"([a-f0-9]{32})"'

@dataclass
class CaptivePortal:
    def __init__(self, module_params: dict):
        self.csrf_url: str = (
            f"https://{module_params['cp_url']}/"
            f"dana-na/auth/{module_params['dssignin']}/welcome.cgi"
        )
        self.login_url: str = (
            f"https://{module_params['cp_url']}/"
            f"dana-na/auth/{module_params['dssignin']}/login.cgi"
        )
        self.heart_beat_url: str = f"https://{module_params['cp_url']}/dana/home/<USER>"
        self.username: str = module_params['username']
        self.pin: str = module_params['pin']
@dataclass
class AnsibleResult:
    def __init__(self) -> None:
        self.cross_site_token: str = ''
        self.changed: bool = False
        self.heartbeat_message: str = ''
    def out(self) -> dict:
        return {
            "changed":self.changed,
            "cross_site_token":self.cross_site_token,
            "heartbeat_message":self.heartbeat_message
        }

def create_http_session() -> Session:
    local_session: Session = Session()
    if initial_cookies:
        for k, v in initial_cookies.items():
            local_session.cookies.set(k, v)
    return local_session

module: AnsibleModule = None
initial_cookies: dict = {"DSSigninNotif":"1"}
heartbeat_data_template: str = (
    "heartbeat=1&"
    "clientlessEnabled=1&"
    "sessionExtension=1&"
    "notification_originalmsg=&"
    "instruction_originalmsg={heartbeat_message}"
)
pid_file: str = "/tmp/captive_portal_heartbeat.pid"
log_file: str = "/tmp/captive_portal_heartbeat.log"
session: Session = create_http_session()

def captive_portal() -> None:
    global module
    module = create_ansible_module()
    cp: CaptivePortal = CaptivePortal(module.params)
    result: AnsibleResult = AnsibleResult()
    result.cross_site_token = get_csrf_token(cp)
    login_request_data = create_login_request_data(cp, result.cross_site_token)
    result.heartbeat_message = get_heartbeat_message(cp, login_request_data)
    kill_previous_heartbeat()
    send_initial_heartbeat_message(cp, result)
    create_new_heartbeat(cp, result)
    module.exit_json(**result.out())

def create_ansible_module() -> AnsibleModule:
    module_args: dict = define_module_args()
    return AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )

def define_module_args() -> dict:
    module_args: dict = {
        "cp_url":{"type":'str', "required":True},
        "dssignin":{"type":'str', "required":True},
        "username":{"type":'str', "required":True},
        "pin":{"type":'str', "required":True, "no_log":True}
    }
    return module_args

def get_csrf_token(cp: CaptivePortal) -> str:
    try:
        csrf_response: Response = session.post(cp.csrf_url, data={}, allow_redirects=True)
    except Exception as e:
        module.fail_json(msg=f"Failed to make {cp.csrf_url} POST request: {str(e)}")
    match: Match[str] = search(CSRF_TOKEN_PATTERN, csrf_response.text)
    if not match:
        module.fail_json(msg=f"Failed to find xsauth token in csrf repsonse: {csrf_response.text}")
    return match.group(1)

def create_login_request_data(cp: CaptivePortal, cross_site_token: str) -> dict:
    return {
        "username":cp.username,
        "password":cp.pin,
        "tz_offset":'-300',
        "clientMAC":'',
        "xsauth_token":cross_site_token,
        "realm":'LMDC_CP_Realm',
        "btnSubmit":'Sign+In'
    }

def get_heartbeat_message(cp: CaptivePortal, login_request_data: dict) -> str:
    try:
        login_response: Response = session.post(
            cp.login_url,
            data=login_request_data,
            allow_redirects=True
        )
    except Exception as e:
        module.fail_json(msg=f"Failed posting heartbeat request: {str(e)}")
    match: Match[str] = search(HEARTBEAT_MESSAGE_PATTERN, login_response.text)
    if not match:
        msg=f"Failed to find heartbeat message token in login_response: {login_response.text}"
        module.fail_json(msg=msg)
    return match.group(1)

def send_initial_heartbeat_message(cp: CaptivePortal, result: AnsibleResult) -> str:
    heartbeat_data: str = heartbeat_data_template.format(heartbeat_message=result.heartbeat_message)
    try:
        heartbeat_response: Response = session.post(
            cp.heart_beat_url,
            data=heartbeat_data,
            allow_redirects=False
        )
    except Exception as e:
        module.fail_json(msg=f"Failed posting initial heartbeat request: {str(e)}")
    if heartbeat_response.status_code != 200:
        msg=(
            f"Failed posting initial heartbeat request:"
            f"Recieved Status code {str(heartbeat_response.status_code)}"
        )
        module.fail_json(msg=msg)

def kill_previous_heartbeat() -> None:
    if os.path.exists(pid_file):
        with open(pid_file, 'r', encoding="utf-8") as f:
            old_pid: str = f.read().strip()
        if old_pid.isdigit():
            try:
                os.kill(int(old_pid), 9)
            except OSError:
                pass
        os.remove(pid_file)

def extract_cookie_string() -> str:
    cookie_header: list[str] = []
    for c in session.cookies:
        cookie_header.append(f"{c.name}={c.value}")
    return "; ".join(cookie_header)

def create_new_heartbeat(cp: CaptivePortal, result: AnsibleResult) -> None:
    cookie_string: str = extract_cookie_string()
    heartbeat_data: str = heartbeat_data_template.format(heartbeat_message=result.heartbeat_message)
    command: str = (
        f"nohup bash -c 'for i in {{1..180}}; do "
        f"sleep 60; "
        f"curl -s -X POST -b \"{cookie_string}\" -d \"{heartbeat_data}\" \"{cp.heart_beat_url}\"; "
        f"done' >{log_file} 2>&1 & echo $! > {pid_file}"
    )
    run_command(command, result)

def run_command(command: str, result: AnsibleResult):
    try:
        subprocess.run(command, shell=True, check=True)
        result.changed = True
    except subprocess.CalledProcessError as e:
        msg=f"Failed to start heartbeat background process: {str(e)}"
        module.fail_json(msg=msg, **result.out())

if __name__ == '__main__':
    captive_portal()
