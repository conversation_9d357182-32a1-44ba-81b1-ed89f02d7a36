"""This module validates the server to have specific requirements"""
import os
from ansible.module_utils.basic import AnsibleModule

module: AnsibleModule = None
BYTES_IN_GIGABYTE = 1024**3

def get_file_size(file_path):
    try:
        return  os.path.getsize(file_path)
    except OSError:
        return 0

def get_folder_usage(path):
    total_size = 0
    for root, _, files in os.walk(path):
        for file in files:
            file_path = os.path.join(root, file)
            file_size = get_file_size(file_path)
            total_size += file_size
    return total_size / BYTES_IN_GIGABYTE

def main():
    global module
    module = create_ansible_module()
    path = module.params['path']
    if "~" in path:
        path = os.path.expanduser(path)
    min_space_gb = module.params['min_space_gb']
    if not os.path.exists(path):
        module.fail_json(msg=f"Path {path} does not exist")
    available_space_gb = get_available_space(path)
    used_space = get_folder_usage(path)
    total_space = available_space_gb + used_space
    if total_space >= min_space_gb:
        module.exit_json(changed=False, msg=f"Total disk space in {path} is {round(total_space,3)}")
    else:
        msg=(
            f"Total disk space in {path} is {round(total_space,3)} "
            f"which is less than {min_space_gb}"
        )
        module.fail_json(msg=msg)

def get_available_space(path):
    statvfs = os.statvfs(path)
    blocks = statvfs.f_bavail
    fragment_size = statvfs.f_frsize
    return fragment_size * blocks / BYTES_IN_GIGABYTE

def create_ansible_module():
    module_args = {
        "path" : {
            "type":'str',
            "required":True
            },
        "min_space_gb" : {
            "type":'float',
            "required":True
            }
        }
    return AnsibleModule(argument_spec=module_args)

if __name__ == '__main__':
    main()
