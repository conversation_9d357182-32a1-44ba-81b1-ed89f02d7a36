# pylint: skip-file
import yaml
import argparse

from compare_install_configs import determine_result

def yaml_to_dict(yaml_file: str) -> dict:
    with open(yaml_file, 'r') as file:
        return yaml.safe_load(file)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("yaml_file_1", type=str)
    parser.add_argument("yaml_file_2", type=str)
    args = parser.parse_args()
    config_1 = yaml_to_dict(args.yaml_file_1)
    config_2 = yaml_to_dict(args.yaml_file_2)
    diffs = determine_result(config_1,config_2)
    print(yaml.dump(diffs, default_flow_style=False))

if __name__ == '__main__':
    main()
