# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        install_config_2.yaml                                             #
# Version:                                                                        #
#               2024-05-16 espy                                                   #
#               2024-11-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-05-16                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

apiVersion: v1
baseDomain: ssc.lmco.com
additionalTrustBundle: |
  -----BEGIN CERTIFICATE-----
  MIIFYTCCA8mgAwIBAgITMAAAAAYSFOE4BKwUewAAAAAABjANBgkqhkiG9w0BAQsF
  ADCBtDELMAkGA1UEBhMCVVMxETAPBgNVBAgTCENvbG9yYWRvMQ8wDQYDVQQHEwZE
  ZW52ZXIxJDAiBgNVBAoTG0xvY2toZWVkIE1hcnRpbiBDb3Jwb3JhdGlvbjEiMCAG
  A1UECxMZQ2VydGlmaWNhdGlvbiBBdXRob3JpdGllczE3MDUGA1UEAxMuTG9ja2hl
  ZWQgTWFydGluIFJvb3QgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkgNDAeFw0yMDEy
  MTcyMDE0NTNaFw0zMDEyMTcyMDI0NTNaMIGpMQswCQYDVQQGEwJVUzERMA8GA1UE
  CBMIQ29sb3JhZG8xDzANBgNVBAcTBkRlbnZlcjEkMCIGA1UEChMbTG9ja2hlZWQg
  TWFydGluIENvcnBvcmF0aW9uMQwwCgYDVQQLEwNFSVQxQjBABgNVBAMTOUxvY2to
  ZWVkIE1hcnRpbiBUTFMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHktIHpzY2FsZXJ0
  aHJlZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANAqYkFqeXuYM8q6
  3oL3RWHatC//GMMro3i3ahOvFcz/+ROZXGExytT36Ta/VwmLZUUs6sXMiPVuY7T5
  gu1+Su4Yz0tYzrUsq703fyBAO4sFQLo5COYdmyN+vfKkLrQgHb3PMrw2EkFGUfqO
  qOrS/Kz4BkOvWApXz2+fYZoWULp8X5XP2jCDPy+999hfeHnvF+QfR0IZx9lEohnK
  MQMxuc4EVVvblRQChIcKd4gcjuY2z5zydTL4qo1hngEcDn1be/iD1vAZTe289r9X
  DMiXEgyKgare6dXRSFxzn/BztuPYgKz1gXnou2MMm35GNC2erGXPYDo8kB8ws5Ws
  DkYe4+8CAwEAAaOB9DCB8TASBgNVHRMBAf8ECDAGAQH/AgEBMA4GA1UdDwEB/wQE
  AwIBhjAdBgNVHQ4EFgQU+lMIhqTNqoyitHTc3LG//SbDg4QwHwYDVR0jBBgwFoAU
  kh6JjphwB2ZBaukswLq05qH9OD0wdgYDVR0fBG8wbTBroGmgZ4ZlaHR0cDovL2Ny
  bC5leHRlcm5hbC5sbWNvLmNvbS9jcmwvY2VydHVwZC9Mb2NraGVlZCUyME1hcnRp
  biUyMFJvb3QlMjBDZXJ0aWZpY2F0aW9uJTIwQXV0aG9yaXR5JTIwNC5jcmwwEwYD
  VR0lBAwwCgYIKwYBBQUHAwEwDQYJKoZIhvcNAQELBQADggGBAHG4OPbS6Gz0Xeak
  W3o218x0PnqKDcqy8f4k0zIdS4a5WqNHtNxwiDy+C6gum2PLxd4QIfO2YHy1ASUa
  3FlAk/D1VMHa+XmmEy7ENOlJF49GZUv2LztfSm+rfWVVK9Vljf2HMVly0jNaKq9x
  LyRzVw+5c01+bW5vlAfl/zw34O/gGHZAQtrYsH1NRgnLuxafjYtbVHvqbdgqnyGn
  2qjLV2PcLthJbhYMQjMGwdAhfPLZgoIkX0qTyacCCRnf7n+xjdRzj4kAc2lty6PJ
  onQXqMnogwcJva7ZjIizagKw50d294azvluCRdlB3mImprBufIJYw+cw3ELF5IG4
  5qE/wyN9sCQA1Nscry/Ahhgec/HjzK5KN8LmtMOXtSCn/eExwhiE8Kewom2viw42
  45V6qW7ovj4P6uSxDFIrOMHIUd2FhR2HFfzktLfWijMN7UzLRTsEUhICaDw/Ahm4
  qY9CD9KiyExkevvUJMjH6XhoRgXyTz1TZEE0O9DdJxkley6v/g==
  -----END CERTIFICATE-----
metadata:
  name: nebula
fips: true
compute:
  - name: worker
    replicas: 7
controlPlane:
  name: master
  replicas: 3
  platform:
    baremetal: {}
platform:
  baremetal:
    apiVIP: "*************"
    ingressVIP: "*************"
    externalBridge: baremetal
    provisioningNetworkCIDR: "*************/24"
    provisioningNetwork: "Managed"
    bootstrapExternalStaticIP: "*************"
    bootstrapExternalStaticGateway: "*************"
    bootstrapOSImage: "https://nexus.galaxy.lmco.com/repository/galaxy-files/rhcos-412.86.202305030814-0-qemu.x86_64.qcow2.gz?\
      sha256=2403df724fa468cd52de5d16f08dea74b53ba167b1df1bb70c28cf55b7bfb058"
    hosts:
      - name: master-00
        role: master
        bmc:
          address: "redfish://*************/redfish/v1/Systems/1"
          username: "fdghfdghdfg"
          password: "fghdfshdfg"
          disableCertificateVerification: true
        bootMACAddress: "5c:b9:01:c0:48:d0"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: *************0, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: master-01
        role: master
        bmc:
          address: "redfish://*************/redfish/v1/Systems/1"
          username: "dfghhdfgfdg"
          password: "dfghdfgh"
          disableCertificateVerification: true
        bootMACAddress: "5c:b9:01:c0:5d:b0"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: *************1, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: master-02
        role: master
        bmc:
          address: "redfish://**************/redfish/v1/Systems/1"
          username: "dfghdfgh"
          password: "fgdhfdgh"
          disableCertificateVerification: true
        bootMACAddress: "70:10:6f:56:1a:08"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: *************2, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-00
        role: worker
        bmc:
          address: "redfish://*************/redfish/v1/Systems/1"
          username: "dfghdgfh"
          password: "dgfhdfgh"
          disableCertificateVerification: true
        bootMACAddress: "70:10:6f:56:1a:18"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-01
        role: worker
        bmc:
          address: "redfish://*************/redfish/v1/Systems/1"
          username: "dfghfgdh"
          password: "dfghdfghdf"
          disableCertificateVerification: true
        bootMACAddress: "70:10:6f:56:1a:58"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-02
        role: worker
        bmc:
          address: "redfish://*************/redfish/v1/Systems/1"
          username: "dfghdfghdfg"
          password: "dfghdfghdfgh"
          disableCertificateVerification: true
        bootMACAddress: "70:10:6f:56:9a:c8"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-03
        role: worker
        bmc:
          address: "redfish://**************/redfish/v1/Systems/1"
          username: "fdsgfgsfd"
          password: "sdfgsdfgsdf"
          disableCertificateVerification: true
        bootMACAddress: "5c:b9:01:c0:5d:90"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-04
        role: worker
        bmc:
          address: "redfish://**************/redfish/v1/Systems/1"
          username: "dsfgsdfgs"
          password: "sdfgsdfg"
          disableCertificateVerification: true
        bootMACAddress: "5c:b9:01:c0:4b:20"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-05
        role: worker
        bmc:
          address: "redfish://*************/redfish/v1/Systems/1"
          username: "sdfgdsfgsd"
          password: "dsfgdsfg"
          disableCertificateVerification: true
        bootMACAddress: "5c:b9:01:c0:4a:c0"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
      - name: worker-06
        role: worker
        bmc:
          address: "redfish://**************/redfish/v1/Systems/1"
          username: "sdfgsdfgdsf"
          password: "sdfgdsfg"
          disableCertificateVerification: true
        bootMACAddress: "5c:b9:01:c0:70:a0"
        rootDeviceHints:
          deviceName: "/dev/sda"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: **************, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno50
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [nebula.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno50}
networking:
  clusterNetwork:
    - cidr: **********/14
      hostPrefix: 23
  machineNetwork:
    - cidr: *************/24
  networkType: OpenShiftSDN
  serviceNetwork:
    - **********/16
imageContentSources:
  - mirrors:
      - registry.galaxy.lmco.com/galaxy_v1.0.0/openshift/release
    source: quay.io/openshift-release-dev/ocp-v4.0-art-dev
  - mirrors:
      - registry.galaxy.lmco.com/galaxy_v1.0.0/openshift/release-images
    source: quay.io/openshift-release-dev/ocp-release
pullSecret: '{"auths": {"registry.galaxy.lmco.com": {"auth": "hgfdhdfhgfdhfdg", "email": ""}}}'
sshKey: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCn4KtNuUWOWZkhCNukBBTj9dP4qrgIcfmU94MuhMTxCcS1DlhZnEG4nD7yg8fPpcIwqcwVUiOwrXxcUQ6kT0s9Ct7h7AMuXi+4XZVrTjpLYul7X\
  wQ55bhoSXPXDc5fUnId2LhcyDxNEDeZCeYkmurZ3XmUDMM8R3oEdru2GOy3lRnrXc9PrvfCNz/5XfLPZKAHKWKkGLIyXx5Io93EaxrAo3YZmBbt5qZsU8f8Focm/VRjWfcLdNMni6SkfrPux5bAkmPUk/foC\
  SHR1gb4bdcnifv1Ep3Fj0JFInBl4rqKpuoh45mupS6fkLltGp1ZNy76RY+wsypX+s6QzSHMlE16cQTaVjO399j45iDLRoJU2XGcRTsxiFbe5JfKNRCOyKZYYE9eNl8YiEDgv0QI2gZWhF2CYdCd8M+swDrqh\
  lVgnKHLBkSmWVwW97TNDRzmz5j9B2YpWh3QHAjLLDeh/lzVKvyjeSgWmTKLOJPOOYnO5O9ynQyI3IoKwfAYO8yXbq8="
