# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        install_config_1.yaml                                             #
# Version:                                                                        #
#               2024-05-16 espy                                                   #
#               2024-11-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-05-16                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

apiVersion: v1
baseDomain: ssc.lmco.com
additionalTrustBundle: |
  -----BEGIN CERTIFICATE-----
  MIIEZzCCA0+gAwIBAgIUIHN/NvH0treY1iD/lwKWsWEE1GswDQYJKoZIhvcNAQEL
  BQAweDELMAkGA1UEBhMCVVMxFTATBgNVBAgMDFBlbm5zeWx2YW5pYTEVMBMGA1UE
  BwwMVmFsbGV5IEZvcmdlMRgwFgYDVQQKDA9Mb2NraGVlZCBNYXJ0aW4xITAfBgNV
  BAMMGHJlZ2lzdHJ5LmdhbGF4eS5sbWNvLmNvbTAeFw0yNDA1MTUwNjMwMDhaFw0y
  NDExMTEwNjMwMDhaMHgxCzAJBgNVBAYTAlVTMRUwEwYDVQQIDAxQZW5uc3lsdmFu
  aWExFTATBgNVBAcMDFZhbGxleSBGb3JnZTEYMBYGA1UECgwPTG9ja2hlZWQgTWFy
  dGluMSEwHwYDVQQDDBhyZWdpc3RyeS5nYWxheHkubG1jby5jb20wggEiMA0GCSqG
  SIb3DQEBAQUAA4IBDwAwggEKAoIBAQCxlicHZzlUYoJf4njc9fv8U1DCkiXEKSud
  2J+DMN/oOG1JOrJw+qv1YdcLUhtZzER8j+KXuxW0dzwP6KlZXjEJeZdxJc5WJfAS
  HOSoF4lmcwBQDVpnAFeotr80/XuHYfM+wOdVRYlo77PPrnzRIIBiY4mIaodMcVic
  niZYezlgpKH0Wk0q5K02EDwmVuzwmTgHgErTUeE7BYZvUl3RjcuvNOEoSXPTtleT
  XleHOFnD/Rmnp9GRWPu3qgbcHOkW7TfTOaDE3HmUIXD4hdyesmfQC706b2RHO3pW
  gZK9EWqvx4OzZ/VJmWWr5adzcybD7siI+KhP3+kefSzZ+ZNwD6ABAgMBAAGjgegw
  geUwHQYDVR0OBBYEFDLWZrYviIfWeyk6Z5SvIv1XmE6RMB8GA1UdIwQYMBaAFDLW
  ZrYviIfWeyk6Z5SvIv1XmE6RMA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/BAQD
  AgeAMCoGA1UdHgEB/wQgMB6gHDAaghhyZWdpc3RyeS5nYWxheHkubG1jby5jb20w
  VgYDVR0RBE8wTYIYcmVnaXN0cnkuZ2FsYXh5LmxtY28uY29tghoqLnJlZ2lzdHJ5
  LmdhbGF4eS5sbWNvLmNvbYIVbmV4dXMuZ2FsYXh5LmxtY28uY29tMA0GCSqGSIb3
  DQEBCwUAA4IBAQBe40t6Fb+Xe+LEEnhpITqmdzKxYk2J8TJmAOJ/1TVRAdrwIWP+
  CpsStJDJX9lt0vM52myP4FTN3P/Adq0Ej5Bw2qzy6Qy0GJFCmi+QHCJXgWjycSdX
  uzNc3pSjZkkIGOG6mNwhaVOrrha9NgvmeebLM27YZw/+1B/ahtaX+XQ267QVEPGg
  Br5Ci6TtQFgvhhJ8ZpxG1WnfqzsbtMrmpc2zOQL42W23Mar9FiSHdguneaqTbFiw
  Te+XDINGi3q5KUqOIobYGtxByAY0rxtm2c6zfijOzfA+hhooIvJnEDQEalBFiz18
  xRNj5Jg+gDpPoImQu5Kr3opohqFVDgF7WY7j
  -----END CERTIFICATE-----
metadata:
  name: ocp-wtn1-lab09
fips: true
compute:
  - name: worker
    replicas: 0
controlPlane:
  name: master
  replicas: 3
  platform:
    baremetal: {}
platform:
  baremetal:
    apiVIP: "*************"
    ingressVIP: "*************"
    externalBridge: baremetal
    provisioningNetwork: "Disabled"
    bootstrapExternalStaticIP: "*************"
    bootstrapExternalStaticGateway: "*************"
    bootstrapOSImage: "https://nexus.galaxy.lmco.com/repository/galaxy-files/rhcos-412.86.202305030814-0-qemu.x86_64.qcow2.gz?\
      sha256=2403df724fa468cd52de5d16f08dea74b53ba167b1df1bb70c28cf55b7bfb058"
    hosts:
      - name: master-00
        role: master
        bmc:
          address: "redfish-virtualmedia://***************.proxy.galaxy.lmco.com:8083/redfish/v1/Systems/1"
          username: "Administrator"
          password: "sgdsdg"
          disableCertificateVerification: true
        bootMACAddress: "b4:7a:f1:da:fe:bc"
        rootDeviceHints:
          deviceName: "/dev/nvme0n1"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: *************0, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno5
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [ocp-wtn1-lab09.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno5}
      - name: master-01
        role: master
        bmc:
          address: "redfish-virtualmedia://***************.proxy.galaxy.lmco.com:8083/redfish/v1/Systems/1"
          username: "Administrator"
          password: "sdgsdg"
          disableCertificateVerification: true
        bootMACAddress: "b4:7a:f1:da:fe:f4"
        rootDeviceHints:
          deviceName: "/dev/nvme0n1"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: *************1, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno5
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [ocp-wtn1-lab09.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno5}
      - name: master-02
        role: master
        bmc:
          address: "redfish-virtualmedia://***************.proxy.galaxy.lmco.com:8083/redfish/v1/Systems/1"
          username: "Administrator"
          password: "sfgs"
          disableCertificateVerification: true
        bootMACAddress: "b4:7a:f1:da:fb:4a"
        rootDeviceHints:
          deviceName: "/dev/nvme0n1"
        networkConfig:
          interfaces:
            - ipv4:
                address:
                  - {ip: *************2, prefix-length: 24}
                dhcp: false
                enabled: true
              name: eno5
              state: up
              type: ethernet
          dns-resolver:
            config:
              search: [ocp-wtn1-lab09.ssc.lmco.com]
              server: [*************]
          routes:
            config:
              - {destination: 0.0.0.0/0, next-hop-address: *************, next-hop-interface: eno5}
networking:
  clusterNetwork:
    - cidr: **********/14
      hostPrefix: 23
  machineNetwork:
    - cidr: *************/24
  networkType: OpenShiftSDN
  serviceNetwork:
    - **********/16
imageContentSources:
  - mirrors:
      - registry.galaxy.lmco.com/galaxy_v1.0.0/openshift/release
    source: quay.io/openshift-release-dev/ocp-v4.0-art-dev
  - mirrors:
      - registry.galaxy.lmco.com/galaxy_v1.0.0/openshift/release-images
    source: quay.io/openshift-release-dev/ocp-release
pullSecret: '{"auths": {"registry.galaxy.lmco.com": {"auth": "dfghdfghdfgh", "email": ""}}}'
sshKey: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCn4KtNuUWOWZkhCNukBBTj9dP4qrgIcfmU94MuhMTxCcS1DlhZnEG4nD7yg8fPpcIwqcwVUiOwrXxcUQ6kT0s9Ct7h7AMuXi+4XZVrTjpLYul7X\
  wQ55bhoSXPXDc5fUnId2LhcyDxNEDeZCeYkmurZ3XmUDMM8R3oEdru2GOy3lRnrXc9PrvfCNz/5XfLPZKAHKWKkGLIyXx5Io93EaxrAo3YZmBbt5qZsU8f8Focm/VRjWfcLdNMni6SkfrPux5bAkmPUk/foC\
  SHR1gb4bdcnifv1Ep3Fj0JFInBl4rqKpuoh45mupS6fkLltGp1ZNy76RY+wsypX+s6QzSHMlE16cQTaVjO399j45iDLRoJU2XGcRTsxiFbe5JfKNRCOyKZYYE9eNl8YiEDgv0QI2gZWhF2CYdCd8M+swDrqh\
  lVgnKHLBkSmWVwW97TNDRzmz5j9B2YpWh3QHAjLLDeh/lzVKvyjeSgWmTKLOJPOOYnO5O9ynQyI3IoKwfAYO8yXbq8="
