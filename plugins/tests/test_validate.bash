echo "Create certificate files"
openssl genrsa -out ca.key 2048 2>/dev/null
openssl req -x509 -new -nodes -key ca.key -sha256 -days 1024 -out ca.crt -subj "/C=US/ST=PA/L=PA/O=ExampleCA/CN=exampleCA.com" 2>/dev/null
openssl genrsa -out server.key 2048 2>/dev/null
openssl req -new -key server.key -out server.csr -subj "/C=US/ST=PA/L=PA/O=Example/CN=example.com" 2>/dev/null
openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days 180 -sha256 -extfile inputs/openssl.conf 2>/dev/null
echo -n "Test 1: Success"
echo '{"ANSIBLE_MODULE_ARGS": {"cert_data": "'"$(awk '{printf "%s\\n", $0}' server.crt)"'", "key_data": "'"$(awk '{printf "%s\\n", $0}' server.key)"'", "ca_data": "'"$(awk '{printf "%s\\n", $0}' ca.crt)"'", "domains": ["example.com", "*.apps.example.com", "*.example.com"]}}' | python3 ../modules/validate_certs.py
echo
echo -n "Test 2: Failure - No Domain in list"
echo '{"ANSIBLE_MODULE_ARGS": {"cert_data": "'"$(awk '{printf "%s\\n", $0}' server.crt)"'", "key_data": "'"$(awk '{printf "%s\\n", $0}' server.key)"'", "ca_data": "'"$(awk '{printf "%s\\n", $0}' ca.crt)"'", "domains": ["example1.com", "*.apps.example.com", "*.example.com"]}}' | python3 ../modules/validate_certs.py
echo
echo -n "Test 4: Failure - Key mismatch"
openssl genrsa -out server2.key 2048 2>/dev/null
echo '{"ANSIBLE_MODULE_ARGS": {"cert_data": "'"$(awk '{printf "%s\\n", $0}' server.crt)"'", "key_data": "'"$(awk '{printf "%s\\n", $0}' server2.key)"'", "ca_data": "'"$(awk '{printf "%s\\n", $0}' ca.crt)"'", "domains": ["example.com", "*.apps.example.com", "*.example.com"]}}' | python3 ../modules/validate_certs.py
echo
echo -n "Test 5: Failure - CA mismatch"
openssl genrsa -out ca2.key 2048 2>/dev/null
openssl req -x509 -new -nodes -key ca2.key -sha256 -days 1024 -out ca2.crt -subj "/C=US/ST=PA/L=PA/O=ExampleCA/CN=exampleCA.com" 2>/dev/null
echo '{"ANSIBLE_MODULE_ARGS": {"cert_data": "'"$(awk '{printf "%s\\n", $0}' server.crt)"'", "key_data": "'"$(awk '{printf "%s\\n", $0}' server.key)"'", "ca_data": "'"$(awk '{printf "%s\\n", $0}' ca2.crt)"'", "domains": ["example.com", "*.apps.example.com", "*.example.com"]}}' | python3 ../modules/validate_certs.py
echo
echo "Removing temp files"
rm -rf ca.crt ca.key ca.srl server.crt server.csr server.key server2.key ca2.crt ca2.key
