"""This filter gets the prefix length of an inputted CIDR"""
import ipaddress

class FilterModule():
    def filters(self):
        return {
            'cidr_prefix_length': self.cidr_prefix_length
        }
    def cidr_prefix_length(self, cidr):
        if not isinstance(cidr, str):
            raise TypeError(f"Expect cidr to be string but got: {type(cidr)}")
        try:
            network = ipaddress.IPv4Network(cidr, strict=False)
        except ValueError as e:
            raise TypeError(f"Invalid network CIDR: {str(cidr)}") from e
        return network.prefixlen
