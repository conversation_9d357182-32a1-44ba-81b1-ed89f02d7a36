"""This filter extracts the CIDR prefix from a subnet"""
import ipaddress
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    get_network_from_cidr
)

class FilterModule():
    def filters(self):
        return {
            'cidr_prefix': self.cidr_prefix
        }
    def cidr_prefix(self, cidr):
        network:ipaddress.IPv4Network = get_network_from_cidr(cidr)
        octets_to_remove = 4 - (network.prefixlen // 8)
        return str(network.network_address).rsplit('.', octets_to_remove).pop(0)
