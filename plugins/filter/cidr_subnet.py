"""This filter extracts the subnet mask from a CIDR"""
import ipaddress
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    get_network_from_cidr
)

class FilterModule():
    def filters(self):
        return {
            'cidr_subnet': self.cidr_subnet
        }
    def cidr_subnet(self, cidr):
        network:ipaddress.IPv4Network = get_network_from_cidr(cidr)
        return str(network.netmask)
