"""This filter takes a CIDR and outputs every available address in that CIDR"""
from ipaddress import IPv4Network

class FilterModule():
    def filters(self):
        return {
            'cidr_to_ips': self.cidr_to_ips
        }
    def cidr_to_ips(self, cidr):
        try:
            network = IPv4Network(cidr)
            ip_list = [f"{str(ip)}" for ip in network.hosts()]
            return ' '.join(ip_list)
        except Exception as e:
            raise ValueError(f"Invalid requested cidr: {str(cidr)}") from e
