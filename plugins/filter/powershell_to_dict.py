"""A filter that parse Powershell return values to a dictionary. This filter only handles input that 
results in 1-dimensional dictionaries.

Powershell will generally return data in the following formats:

        # DHCPEnabled      : True
        # IPAddress        :
        # DefaultIPGateway :
        # DNSDomain        :
        # ServiceName      : kdnic
        # Description      : Microsoft Kernel Debug Network Adapter
        # Index            : 0

    or sometimes with additional header information:

        Configuration for interface "Ethernet"
        DHCP enabled:                         Yes
        IP Address:                           ********
        Subnet Prefix:                        ********/24 (mask *************)
        Default Gateway:                      ********
        Gateway Metric:                       0
        InterfaceMetric:                      15"
"""


class FilterModule():
    def filters(self):
        return {
            'powershell_to_dict': self.powershell_to_dict
        }

    def powershell_to_dict(self, powershell_output):
        # The call to splitlines() should handle line ending differences between Windows/Linux
        lines = powershell_output.splitlines()
        return_dict = {}
        for line in lines:
            kvp = [line_split.strip() for line_split in line.split(':')]
            if len(kvp) > 1:
                return_dict[kvp[0]] = kvp[1]
        return return_dict
