"""A filter that merges two dicts or lists but handles lists with dicts
with name keys differently then the standard combine. Treats lists as a
less sophiscated dict"""
class FilterModule():
    def filters(self):
        return {
            'merge_nmstates': self.merge_nmstates
        }

    def merge_dicts(self, default_dict, updated_dict):
        result = dict(default_dict)
        for key, value in updated_dict.items():
            if key in result:
                if isinstance(result[key], list) and isinstance(value, list):
                    result[key] = self.merge_lists(result[key], value)
                elif isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = self.merge_dicts(result[key], value)
                else:
                    result[key] = value
            else:
                result[key] = value
        return result

    def merge_lists(self, default_list, updated_list):
        result = list(default_list)
        if self.do_all_these_lists_contain_dicts_with_name_key([default_list, updated_list]):
            default_map = {item['name']: item for item in default_list}
            updated_map = {item['name']: item for item in updated_list}
            for name, updated_item in updated_map.items():
                if name in default_map:
                    default_map[name] = self.merge_dicts(default_map[name], updated_item)
                else:
                    default_map[name] = updated_item
            return list(default_map.values())
        return result

    def do_all_these_lists_contain_dicts_with_name_key(self, list_of_list):
        return all(is_this_a_list_of_dicts_with_name_key(list_to_check)
                   for list_to_check in list_of_list)

    def merge_nmstates(self, default_state, updated_state):
        if not isinstance(updated_state, list):
            raise TypeError(f"Expect updated state to be list but got: {type(updated_state)}")
        return self.merge_lists(default_state, updated_state)

def is_this_a_list_of_dicts_with_name_key(list_to_check):
    return all(isinstance(list_item, dict) and 'name' in list_item for list_item in list_to_check)
