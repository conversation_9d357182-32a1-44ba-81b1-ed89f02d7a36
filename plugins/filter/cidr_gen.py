"""This filter combines a CIDR with an index offset to grab an available ip address"""
import ipaddress
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    get_network_from_cidr, invalid_ip_cidr
)

class FilterModule():
    def filters(self):
        return {
            'cidr_gen': self.cidr_gen
        }

    def cidr_gen(self, cidr, offset):
        network:ipaddress.IPv4Network = get_network_from_cidr(cidr)
        network_id = int(offset) + int(str(network[0]).rsplit('.', maxsplit=1)[-1])
        try:
            ip = str(ipaddress.IPv4Address(int(network.network_address) + int(offset)))
        except Exception as e:
            invalid_ip_cidr(network, network_id, e)
        return ip
