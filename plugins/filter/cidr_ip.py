"""This filter combines a CIDR with an index to create an available ip address"""
import ipaddress
from ansible_collections.lmco.openshift.plugins.module_utils.galaxy_common import (
    get_network_from_cidr, invalid_ip_cidr
)

class FilterModule():
    def filters(self):
        return {
            'cidr_ip': self.cidr_ip
        }
    def cidr_ip(self, cidr, ip):
        network:ipaddress.IPv4Network = get_network_from_cidr(cidr)
        try:
            network_id = int(ip)
        except Exception as e:
            raise ValueError(f"Invalid requested value: {str(ip)}") from e
        if network_id < int(str(network[0]).rsplit('.', maxsplit=1)[-1]):
            raise ValueError(
                f"Invalid requested ipaddress: {str(network_id)} is less than {str(network[0])}"
            )
        if network_id >= int(str(network[-1]).rsplit('.', maxsplit=1)[-1]):
            raise ValueError(
                f"Invalid requested ipaddress: {str(network_id)} is greater than {str(network[-1])}"
            )
        try:
            ip = str(ipaddress.IPv4Address(int(network.network_address) + network_id))
        except Exception as e:
            invalid_ip_cidr(network, network_id, e)
        return ip
