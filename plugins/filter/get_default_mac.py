"""This filter makes a redfish api call to grab the first mac address it finds on a server"""
import requests
from ansible.errors import AnsibleError
from requests import Response
from requests.auth import HTTPBasicAuth

class FilterModule():
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.bmc_info: dict = {}
        self.ilo_major_version = ""

    def filters(self):
        return {
            'get_default_mac': self.get_default_mac
        }

    def api_request(self, redfish_path, **kwargs) -> Response:
        url=f"https://{ self.bmc_info['ipv4_address'] }{redfish_path}"
        return requests.get(
            url=url,
            auth=HTTPBasicAuth(
                username=self.bmc_info['username'],
                password=self.bmc_info['password']
                ),
            verify=False,
            timeout=60,
            proxies={'http': '','https': ''},
            **kwargs
        )

    def get_default_mac(self, bmc_info: dict) -> str:
        self.bmc_info = bmc_info
        self.set_bmc_firmware_version()
        system_id = bmc_info.get('system_id', '1')
        network_adapter_paths = self.get_network_path(system_id)
        redfish_network_adapter_path = self.get_default_adapter_url(network_adapter_paths)
        network_adapter_response = self.api_request(redfish_network_adapter_path)
        if self.ilo_major_version == '6':
            redfish_ports_url = network_adapter_response.json()['Ports']['@odata.id']
            ports_response = self.api_request(redfish_ports_url)
            redfish_default_port_url = ports_response.json()['Members'][0]['@odata.id']
            default_port_response = self.api_request(redfish_default_port_url)
            return self.get_first_mac_address_listed_ilo6(default_port_response)
        return self.get_first_mac_address_listed(network_adapter_response)

    def get_first_mac_address_listed(self, network_adapter_response: Response) -> str:
        return network_adapter_response.json()['PhysicalPorts'][0]['MacAddress']

    def get_first_mac_address_listed_ilo6(self, default_port_response: Response) -> str:
        return default_port_response.json()['Ethernet']['LLDPTransmit']['PortId']

    def set_bmc_firmware_version(self) -> None:
        manager_id = self.bmc_info.get('manager_id', '1')
        api_response = self.api_request(f"/redfish/v1/Managers/{manager_id}/")
        self.ilo_major_version = api_response.json()['FirmwareVersion'].split(' ')[1]

    def get_network_path(self, system_id: str) -> str:
        if self.ilo_major_version == '4':
            return f'Systems/{system_id}/NetworkAdapters'
        if self.ilo_major_version == '5':
            return f'Systems/{system_id}/BaseNetworkAdapters'
        if self.ilo_major_version == '6':
            return f'Chassis/{system_id}/NetworkAdapters'
        raise AnsibleError("Unsupported BMC")

    def get_default_adapter_url(self, network_adapter_paths):
        redfish_url =  f"/redfish/v1/{network_adapter_paths}/"
        api_response = self.api_request(redfish_url)
        return api_response.json()['Members'][0]['@odata.id']
