"""A place to keep all your shared code from this collection"""
import threading
import time
import ipaddress
from abc import abstractmethod
from dataclasses import dataclass
from ansible.module_utils.basic import AnsibleModule

module: AnsibleModule = None

@dataclass
class BaseModuleData:
    def __init__(self, module_params: dict):
        self.timeout: int = module_params['timeout']
        self.start_time: float = time.time()
        self.out: dict[dict] = {}

    @property
    @abstractmethod
    def hosts(self) -> list:
        pass

def wait_for(module_data: BaseModuleData, wait_for_method) -> None:
    try:
        monitor_hosts(module_data, wait_for_method)
        determine_failure(module_data)
        module.exit_json(changed=True, msg=module_data.out)
    except Exception as e:
        module.fail_json(msg=str(e))

def is_failure(data: dict) -> bool:
    return data.get("success") is None or data.get("success") is False

def determine_failure(module_data: BaseModuleData) -> None:
    if any(is_failure(data) for data in module_data.out.values()):
        module.fail_json(msg=module_data.out)

def monitor_hosts(module_data: BaseModuleData, wait_for_method) -> None:
    threads: list[threading.Thread] = []
    for host in module_data.hosts:
        thread = threading.Thread(target=wait_for_method, args=(host, module_data))
        threads.append(thread)
        thread.start()
    for thread in threads:
        thread.join()

def create_ansible_module(module_args: dict) -> AnsibleModule:
    global module
    module = AnsibleModule(
        argument_spec=module_args,
        supports_check_mode=True
    )
    return module

def get_network_from_cidr(cidr: str) -> ipaddress.IPv4Network:
    if not isinstance(cidr, str):
        raise TypeError(f"Expect cidr to be string but got: {type(cidr)}")
    try:
        network: ipaddress.IPv4Network = ipaddress.IPv4Network(cidr)
    except ValueError as e:
        raise TypeError(f"Invalid network CIDR: {str(cidr)}") from e
    return network

def invalid_ip_cidr(network, network_id, e):
    raise ValueError(
        f"Invalid requested index: {str(network_id)}"
        f"for network range between {str(network[0])} and {str(network[-1])}"
    ) from e
