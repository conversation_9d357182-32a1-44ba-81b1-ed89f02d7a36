# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        galaxy.yml                                                        #
# Version:                                                                        #
#               2023-05-09 <PERSON><PERSON><PERSON>, <PERSON>                                      #
#               2023-05-09 <PERSON>                                       #
#               2023-05-15 espy                                                   #
#               2023-07-24 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-11-01 espy                                                   #
#               2024-02-15 galaxy-bot                                             #
#               2024-02-19 galaxy-bot                                             #
#               2024-02-20 galaxy-bot                                             #
#               2024-03-12 galaxy-bot                                             #
#               2024-03-27 galaxy-bot                                             #
#               2024-03-27 Sam<PERSON>, Landon                                           #
#               2024-03-28 galaxy-bot                                             #
#               2024-04-01 galaxy-bot                                             #
#               2024-04-01 Sam<PERSON>, Landon                                           #
#               2024-04-04 galaxy-bot                                             #
#               2024-04-09 galaxy-bot                                             #
#               2024-04-15 galaxy-bot                                             #
#               2024-04-16 galaxy-bot                                             #
#               2024-04-17 galaxy-bot                                             #
#               2024-04-23 galaxy-bot                                             #
#               2024-04-24 galaxy-bot                                             #
#               2024-04-26 galaxy-bot                                             #
#               2024-04-27 galaxy-bot                                             #
#               2024-04-30 galaxy-bot                                             #
#               2024-05-02 galaxy-bot                                             #
#               2024-05-06 galaxy-bot                                             #
#               2024-05-07 galaxy-bot                                             #
#               2024-05-08 galaxy-bot                                             #
#               2024-05-09 galaxy-bot                                             #
#               2024-05-10 galaxy-bot                                             #
#               2024-05-12 galaxy-bot                                             #
#               2024-05-13 galaxy-bot                                             #
#               2024-05-14 galaxy-bot                                             #
#               2024-05-29 galaxy-bot                                             #
#               2024-05-30 galaxy-bot                                             #
#               2024-06-02 galaxy-bot                                             #
#               2024-06-07 galaxy-bot                                             #
#               2024-06-13 galaxy-bot                                             #
#               2024-06-17 galaxy-bot                                             #
#               2024-06-18 galaxy-bot                                             #
#               2024-06-20 galaxy-bot                                             #
#               2024-06-24 galaxy-bot                                             #
#               2024-07-04 galaxy-bot                                             #
#               2024-07-08 galaxy-bot                                             #
#               2024-07-09 galaxy-bot                                             #
#               2024-07-16 galaxy-bot                                             #
#               2024-07-22 galaxy-bot                                             #
#               2024-07-24 galaxy-bot                                             #
#               2024-07-25 galaxy-bot                                             #
#               2024-07-29 galaxy-bot                                             #
#               2024-07-30 galaxy-bot                                             #
#               2024-07-31 galaxy-bot                                             #
#               2024-08-02 galaxy-bot                                             #
#               2024-08-05 galaxy-bot                                             #
#               2024-08-12 galaxy-bot                                             #
#               2024-08-15 galaxy-bot                                             #
#               2024-08-16 galaxy-bot                                             #
#               2024-08-21 galaxy-bot                                             #
#               2024-08-22 galaxy-bot                                             #
#               2024-08-27 galaxy-bot                                             #
#               2024-08-29 galaxy-bot                                             #
#               2024-09-09 galaxy-bot                                             #
#               2024-09-11 galaxy-bot                                             #
#               2024-09-12 galaxy-bot                                             #
#               2024-09-16 galaxy-bot                                             #
#               2024-09-17 galaxy-bot                                             #
#               2024-09-18 galaxy-bot                                             #
#               2024-09-19 galaxy-bot                                             #
#               2024-09-23 galaxy-bot                                             #
#               2024-09-24 galaxy-bot                                             #
#               2024-10-02 galaxy-bot                                             #
#               2024-10-07 galaxy-bot                                             #
#               2024-10-09 galaxy-bot                                             #
#               2024-10-22 galaxy-bot                                             #
#               2024-10-29 galaxy-bot                                             #
#               2024-11-04 galaxy-bot                                             #
#               2024-11-07 galaxy-bot                                             #
#               2024-11-11 galaxy-bot                                             #
#               2024-11-20 galaxy-bot                                             #
#               2024-11-21 galaxy-bot                                             #
#               2024-11-26 galaxy-bot                                             #
#               2024-11-27 galaxy-bot                                             #
#               2024-12-03 galaxy-bot                                             #
#               2024-12-05 galaxy-bot                                             #
#               2024-12-09 galaxy-bot                                             #
#               2024-12-11 galaxy-bot                                             #
#               2024-12-12 galaxy-bot                                             #
#               2024-12-16 galaxy-bot                                             #
#               2024-12-18 galaxy-bot                                             #
#               2024-12-19 galaxy-bot                                             #
#               2025-01-07 galaxy-bot                                             #
#               2025-01-13 galaxy-bot                                             #
#               2025-01-15 galaxy-bot                                             #
#               2025-01-20 galaxy-bot                                             #
#               2025-01-23 galaxy-bot                                             #
#               2025-01-24 galaxy-bot                                             #
#               2025-01-25 galaxy-bot                                             #
#               2025-01-26 galaxy-bot                                             #
#               2025-01-27 galaxy-bot                                             #
#               2025-01-28 galaxy-bot                                             #
#               2025-01-29 galaxy-bot                                             #
#               2025-01-30 espy                                                   #
#               2025-01-30 galaxy-bot                                             #
# Create Date:  2023-05-09                                                        #
# Author:       Casagrande, Wayne                                                 #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

### REQUIRED
# The namespace of the collection. This can be a company/brand/organization or product namespace under which all
# content lives. May only contain alphanumeric lowercase characters and underscores. Namespaces cannot start with
# underscores or numbers and cannot contain consecutive underscores
namespace: lmco

# The name of the collection. Has the same character restrictions as 'namespace'
name: openshift

# The version of the collection. Must be compatible with semantic versioning
version: 1.12.8

# The path to the Markdown (.md) readme file. This path is relative to the root of the collection
readme: README.md

# A list of the collection's content authors. Can be just the name or in the format 'Full Name <email> (url)
# @nicks:irc/im.site#channel'
authors:
  - Espy <EMAIL>

### OPTIONAL but strongly recommended
# A short summary description of the collection
description: This collection is used for installing openshift on any platform

# Either a single license or a list of licenses for content inside of a collection. Ansible Galaxy currently only
# accepts L(SPDX,https://spdx.org/licenses/) licenses. This key is mutually exclusive with 'license_file'
license:
  - GPL-2.0-or-later

# The path to the license file for the collection. This path is relative to the root of the collection. This key is
# mutually exclusive with 'license'
license_file: ''

# A list of tags you want to associate with the collection for indexing/searching. A tag name has the same character
# requirements as 'namespace' and 'name'
tags: ['application', 'cloud', 'infrastructure', 'linux', 'networking']

# Collections that this collection requires to be installed for it to be usable. The key of the dict is the
# collection label 'namespace.name'. The value is a version range
# L(specifiers,https://python-semanticversion.readthedocs.io/en/latest/#requirement-specification). Multiple version
# range specifiers can be set and are separated by ','
dependencies: {}

# The URL of the originating SCM repository
repository: http://example.com/repository

# The URL to any online docs
documentation: http://docs.example.com

# The URL to the homepage of the collection/project
homepage: http://example.com

# The URL to the collection issue tracker
issues: http://example.com/issue/tracker

# A list of file glob-like patterns used to filter any files or directories that should not be included in the build
# artifact. A pattern is matched from the relative path of the file or directory of the collection directory. This
# uses 'fnmatch' to match the files or directories. Some directories and files like 'galaxy.yml', '*.pyc', '*.retry',
# and '.git' are always filtered
build_ignore:
  - secrets
  - dev_files
  - '*.sh'
  - run.sh
  - ocp_install_files
  - '*.tar.gz'
  - '.gitignore'
  - '.gitlab-ci.yml'
  - '.git'
  - '*.tar'
