#!/bin/bash

# <PERSON>ript to diagnose and fix OCP undefined errors
# Run this script to check your configuration and get guidance

echo "=== OCP Undefined Error Diagnostic Tool ==="
echo

# Check if required environment variables are set
echo "1. Checking environment variables..."
required_env_vars=("GALAXY_BUILD_LMI_IP" "GALAXY_BUILD_MGMT_IP" "GALAXY_BUILD_PORT")
missing_env_vars=()

for var in "${required_env_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        missing_env_vars+=("$var")
    else
        echo "  ✓ $var is set to: ${!var}"
    fi
done

if [[ ${#missing_env_vars[@]} -gt 0 ]]; then
    echo "  ❌ Missing environment variables:"
    for var in "${missing_env_vars[@]}"; do
        echo "    - $var"
    done
    echo
    echo "  To fix, run:"
    echo "    export GALAXY_BUILD_LMI_IP=\"your-builder-ip\""
    echo "    export GALAXY_BUILD_MGMT_IP=\"your-mgmt-ip\""
    echo "    export GALAXY_BUILD_PORT=\"8080\""
    echo
fi

# Check if group_vars/all.yml exists
echo "2. Checking group_vars configuration..."
if [[ -f "group_vars/all.yml" ]]; then
    echo "  ✓ group_vars/all.yml exists"
    
    # Check if galaxy variable is defined
    if grep -q "^galaxy:" group_vars/all.yml; then
        echo "  ✓ galaxy variable is defined"
    else
        echo "  ❌ galaxy variable is not defined in group_vars/all.yml"
        echo "    Add the galaxy variable structure to group_vars/all.yml"
    fi
    
    # Check for required OCP variables
    ocp_vars=("galaxy_ocp" "lmco_ocp" "cli_ocp")
    for var in "${ocp_vars[@]}"; do
        if grep -q "^${var}:" group_vars/all.yml; then
            echo "  ✓ $var is defined"
        else
            echo "  ❌ $var is not defined in group_vars/all.yml"
        fi
    done
else
    echo "  ❌ group_vars/all.yml does not exist"
    echo "    Create group_vars/all.yml with required variables"
fi

echo

# Check if inventory file exists
echo "3. Checking inventory configuration..."
inventory_files=("inventory.yml" "inventory.yaml" "hosts" "hosts.yml" "hosts.yaml")
inventory_found=false

for inv_file in "${inventory_files[@]}"; do
    if [[ -f "$inv_file" ]]; then
        echo "  ✓ Found inventory file: $inv_file"
        inventory_found=true
        break
    fi
done

if [[ "$inventory_found" == false ]]; then
    echo "  ❌ No inventory file found"
    echo "    Create an inventory file (see inventory-example.yml)"
fi

echo

# Provide recommendations
echo "=== Recommendations ==="
echo

if [[ ${#missing_env_vars[@]} -gt 0 ]] || [[ ! -f "group_vars/all.yml" ]] || [[ "$inventory_found" == false ]]; then
    echo "To fix the OCP undefined error:"
    echo
    
    if [[ ${#missing_env_vars[@]} -gt 0 ]]; then
        echo "1. Set missing environment variables:"
        echo "   export GALAXY_BUILD_LMI_IP=\"*************\"  # Replace with your IP"
        echo "   export GALAXY_BUILD_MGMT_IP=\"*************\" # Replace with your IP"
        echo "   export GALAXY_BUILD_PORT=\"8080\""
        echo
    fi
    
    if [[ ! -f "group_vars/all.yml" ]]; then
        echo "2. Create group_vars/all.yml with required variables:"
        echo "   cp group_vars/all.yml.example group_vars/all.yml  # If example exists"
        echo "   # Or use the provided group_vars/all.yml template"
        echo
    fi
    
    if [[ "$inventory_found" == false ]]; then
        echo "3. Create an inventory file:"
        echo "   cp inventory-example.yml inventory.yml"
        echo "   # Then customize it for your environment"
        echo
    fi
    
    echo "4. Customize the configuration files with your actual values:"
    echo "   - Replace placeholder IPs, hostnames, and passwords"
    echo "   - Set correct cluster name and domain"
    echo "   - Configure BMC credentials"
    echo
else
    echo "✓ Configuration looks good!"
    echo "If you're still getting OCP undefined errors, check:"
    echo "1. Variable syntax in your YAML files"
    echo "2. Indentation and structure"
    echo "3. That all referenced variables are properly defined"
fi

echo
echo "=== End of Diagnostic ==="
