# Example inventory file for OCP deployment
# Copy this file and customize it for your environment

all:
  children:
    bastion:
      hosts:
        bastion-00:
          ansible_host: "***********0"  # Replace with your bastion IP
          lmco_host:
            abstract:
              name: "bastion-00"
              purpose: "bastion"
              description: "OpenShift bastion host"
            bmc:
              ipv4_address: "***********1"  # Replace with BMC IP
              username: "admin"  # Replace with BMC username
              password: "password"  # Replace with BMC password
              system_id: "1"
              manager_id: 1
              virtual_media_id: 2
              type: "ilo"
            network:
              interfaces:
                - name: "lmi"
                  type: "ethernet"
                  state: "up"
                  ipv4:
                    enabled: true
                    address:
                      - ip: "***********0"
                        prefix-length: 24
                    dhcp: false
                - name: "baremetal"
                  type: "ethernet"
                  state: "up"
                  ipv4:
                    enabled: true
                    address:
                      - ip: "*************"
                        prefix-length: 24
                    dhcp: false
              external_connection_name: "lmi"
              routes:
                config:
                  - destination: "0.0.0.0/0"
                    next-hop-address: "***********"
                    next-hop-interface: "lmi"
            credentials:
              root_password: "your-root-password"  # Replace with encrypted password
              ocp_user_password: "your-ocp-password"  # Replace with encrypted password
              ocp_user_authorized_public_key_file: "~/.ssh/id_rsa.pub"

  vars:
    # Galaxy configuration - CUSTOMIZE THESE VALUES
    galaxy:
      project_name: "my-ocp-cluster"  # Your cluster name
      ocp:
        hostname: "my-cluster.example.com"  # Your cluster domain
        version: "4.18.20"  # OCP version
        install_file_name: "openshift-install"
      nexus:
        username: "admin"
        password: "nexus-password"  # Replace with your password
        hostname: "nexus.example.com"  # Replace with your hostname
        repo_name: "galaxy-repo"
        storage_file_name: "nexus-storage.tar"
      registry:
        hostname: "registry.example.com"  # Replace with your hostname
        initial_url: "registry.example.com:8082"
        crt_file: "registry.crt"
      rhel:
        major_version: 9
        minor_version: 5
        patch_version: 0
        packages_file_name: "rhel-packages.tar"
      verson_vars_file_name: "version-vars.yml"

    # OCP variable overrides
    galaxy_ocp: {}
    
    lmco_ocp:
      hostname: "my-cluster.example.com"  # Override if different from galaxy.ocp.hostname
      openshift_cluster: "my-ocp-cluster"  # Override if different from galaxy.project_name
      
    cli_ocp: {}
    
    galaxy_hosts: []  # Define if using dynamic host discovery
