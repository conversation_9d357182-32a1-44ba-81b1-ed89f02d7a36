## [1.12.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.7...1.12.8) (2025-08-05)

### Bug Fixes

* missing var in spec ([78936bc](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/78936bca84d147231a61f0b7880fe772b61aa282))

## [1.12.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.6...1.12.7) (2025-08-05)

### Bug Fixes

* Major refactor of variable names ([6b4c35a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6b4c35a3b340b91cfcf3438d1014de97f0b03773))

## [1.12.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.5...1.12.6) (2025-08-05)

### Bug Fixes

* to match galaxy collection ([268f0bc](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/268f0bcd234965d47cc34aeb2ea7c87e9843b263))

## [1.12.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.4...1.12.5) (2025-08-04)

### Bug Fixes

* using 4.18 ([8a1f7a1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8a1f7a1ca8b367b86706f62b729ddcd8ad408fd7))

## [1.12.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.3...1.12.4) (2025-08-04)

### Bug Fixes

* dev connected logic ([15cc9bd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/15cc9bdf7fa17d87f766a74ec0cc72bbd7da2f3d))

## [1.12.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.2...1.12.3) (2025-08-03)

### Bug Fixes

* removed reliance on dns in haproxy ([00dfdcd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/00dfdcd3469372b637ccce11eb9d7a30c1f7b396))

## [1.12.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.1...1.12.2) (2025-07-31)

### Bug Fixes

* Partially statically setting ip addresses in haproxy ([b81f0d6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b81f0d619c13568826bcb86294c9a19aab1facbb))

## [1.12.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.12.0...1.12.1) (2025-07-31)

### Bug Fixes

* proxy issue and timeout issue ([635a0ff](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/635a0ffb19d9947b199bf87e18524214474ed291))

## [1.12.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.19...1.12.0) (2025-07-30)

### Features

* Added functionality to delete the users starts with cluster ([4531dfe](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4531dfe1b0e563423c0ad8f83d866ff92051e0c1))

### Bug Fixes

* lints fixed ([58d1b5c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/58d1b5c4eaa530906fcf19c6e71d23854af8c1d5))

## [1.11.19](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.18...1.11.19) (2025-07-30)

### Bug Fixes

* Made long running tasks asynchronous so the stigg doesnt timeout the ssh connection ([a4bbb67](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a4bbb67d8ef83c527139e6f600f4dc64afc25379))

## [1.11.18](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.17...1.11.18) (2025-07-30)

### Bug Fixes

* fix galaxy proxy haproxy routes ([324835c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/324835c6739f146327718d7329b13786a049be78))
* increase timeout time ([40257bd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/40257bd60d977cf59e32009312a2fc8d85616d99))

## [1.11.17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.16...1.11.17) (2025-07-25)

### Bug Fixes

* Added fix commit message ([21cd411](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/21cd411b3d62366772133f9052a02fd3c4fa1361))

## [1.11.16](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.15...1.11.16) (2025-07-24)

### Bug Fixes

* removed bundling ([c70ab98](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c70ab989d75b39888a83492d8539faf17cf2e4d5))
* rhel versions coming in as strings ([947745f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/947745f62a02217ae1848aa87d3f50349b1502dc))

## [1.11.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.14...1.11.15) (2025-07-21)

### Bug Fixes

* ansible errors issue ([cd12534](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/cd12534a897f70ed98c746ec2a51328a18219a2c))

## [1.11.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.13...1.11.14) (2025-07-16)

### Bug Fixes

* hardening fixes ([8f5a609](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8f5a6092b2247d1b951267a95b5e3f2239cf7d9e))

## [1.11.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.12...1.11.13) (2025-07-14)

### Bug Fixes

* fix for audit d issues ([3a2f4ef](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/3a2f4ef4f0d14a0d79037bd5fba8121508d1af65))

## [1.11.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.11...1.11.12) (2025-07-14)

### Bug Fixes

* added debug statements ([005fc1c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/005fc1c9060729fa7847e60749df241b8a7f2f2e))
* grep regex issue ([51fbfd6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/51fbfd627e5d26a70195fd9b673010c5ac5e791d))

## [1.11.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.10...1.11.11) (2025-07-13)

### Bug Fixes

* added packages for oscap ([e779062](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e77906257b18fb96ffa45c2488ee857263b1f3cc))

## [1.11.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.9...1.11.10) (2025-07-13)

### Bug Fixes

* fixing oscap stuff ([3b944dd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/3b944ddb4a57087e58d4526ce90fd20d1b2de77b))

## [1.11.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.8...1.11.9) (2025-07-11)

### Bug Fixes

* Add aid package ([9f4e1b4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9f4e1b42cc8a752fb35b4f52c056a9635d92b7e3))

## [1.11.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.7...1.11.8) (2025-07-11)

### Bug Fixes

* missing packages for rhel 9,4 ([2171aa4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2171aa4456703b4242dbd67b04012ebb1df470e5))

## [1.11.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.6...1.11.7) (2025-07-09)

### Bug Fixes

* Added dev files folder ([c9155b5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c9155b5aa20a822bbde8691a08b7b4b465d392ad))

## [1.11.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.5...1.11.6) (2025-07-08)

### Bug Fixes

* podman containers network ([c9f625a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c9f625a8ea3b9080a9ca3fb44228a24503ca7c5d))

## [1.11.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.4...1.11.5) (2025-07-02)

### Bug Fixes

* Removed dups ([d53c0d8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d53c0d894e2e9054f14fabb9dcdffb72cc4ad064))
* Removed dups ([676f79c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/676f79c1b165f0edba6facf888c20da81ab2d3f6))
* Sorted values ([2b47cac](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2b47cac12f4c9eeb16a3ce4b5ed322002604f2d3))

## [1.11.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.3...1.11.4) (2025-07-02)

### Bug Fixes

* Disabled fips by default ([a008aac](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a008aac372b58d3b63c4f958d9616122ac2cfd17))

## [1.11.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.2...1.11.3) (2025-07-01)

### Bug Fixes

* Made some fixes based on starwan deployment ([98ed218](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/98ed218bc87614562a16cdfc6e2a9c49773c94a7))
* revert back ocp version ([06bc824](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/06bc8240fc6cf2a49efd604b306ae1f929642484))
* updatedd wait fdor ssh script ([e386779](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e38677954830a1a0a65cf53db50ae7667ad4d814))

## [1.11.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.1...1.11.2) (2025-06-25)

### Bug Fixes

* Fixed luks encryption ([43e7607](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/43e7607d93be20f370f9473fbd3ed39c9b484b6a))
* reset tpm chip on cluster nodes ([02a113f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/02a113f1cbab83bfc8f14921bf9caac0db15b9d5))
* Updated haproxy and coredns images ([8f299ac](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8f299acce42b00209e187f71be6b0a8e831395be))

## [1.11.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.11.0...1.11.1) (2025-06-18)

### Bug Fixes

* [XBA_GALAXY_INF-423](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_INF-423) "/ntp" ([5ad618c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5ad618c7d529500eb2466fd4d3cd8f4abe0300c1))

## [1.11.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.19...1.11.0) (2025-06-03)

### Features

* add argument specifications for OpenShift tests role and improve debug message formatting ([3426690](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/34266902384e5ee24072e5749f275ad5dca29403))
* add DNS verification tasks for service checks ([b1b0f0f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b1b0f0f052dd05fb0b014d28424dd58ce5f199d5))
* add initial argument specifications and main metadata for OpenShift tests role ([9f74bba](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9f74bbad9c38f07e5e8725757a612312204630a8))
* add installation validation and Nexus verification tasks ([65e0a4c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/65e0a4cd86dcebe461e86f4c6d673d950f7bc14d))
* add metadata and improve task scripts for installation validation ([dedaf14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/dedaf144d893a02d45eda3625b1861c5a1e030ce))

### Bug Fixes

* added test role with verify tasks ([3ed29c4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/3ed29c45fbb90446240c6444bae66f60ba2e8d67))
* cleanup ([77050f1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/77050f13864caebab3b750832ff91bd691bf6a3d))
* correct typos in task names for clarity ([bd1a57b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/bd1a57b08eacc9bdf4aa6477b12da241bb6bef2a))
* debug ([81c686b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/81c686b394b71532a59a56baf3fc4092188e1bd2))
* debug ([d210d72](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d210d7299a5b867ac9d24e1e8b0b3816831e8dce))
* header ([4344c56](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4344c56a12e8fb5d4bd8f01cba763914b113a2a3))
* header ([f438e54](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f438e54ff10e0dd5f9af3d9ab91504a9e2f0d1b5))
* header ([1f70db2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1f70db2a220da92d3ebd23986e06d53d188a5564))
* lint ([ee6322e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ee6322e5ff830ce7dcfdbd180f0150ad6c61aa80))
* lint ([c9062ca](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c9062caf20e85e94a374728d22e6563d15f0726c))
* lint ([c1149a9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c1149a96568088c79e01ac45994896dc25b1f39c))
* Molecule test for openshift collection ([fa55b9c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/fa55b9c5cab51a9666e59f0fe66d10518f20c43b))
* more clean up ([57da465](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/57da4651bade34b3f7be5ade73b190ea78241d28))
* remove cluster verify ([880a290](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/880a290239a42de902c2960016502c96217ccd2f))
* syntax ([0bac485](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0bac48578e0b4f4d839dc1454874f5ca296cd8ec))
* syntax ([185f3dd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/185f3ddf4f3f640f8820fadc768bee5759b42467))

## [1.10.19](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.18...1.10.19) (2025-05-09)

### Bug Fixes

* add in cs option for db2/ibm catalog source products ([50330e5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/50330e51322f1b1a5b9d590721e92e65ededc473))

## [1.10.18](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.17...1.10.18) (2025-05-06)

### Bug Fixes

* podman in podman updates ([05c3f6e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/05c3f6e788064406c7703e76c4bbd66a63e53511))

## [1.10.17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.16...1.10.17) (2025-04-28)

### Reverts

* Revert "fix: Remove the requirement for the kubeconfig file to be provided so that it will work in aap" ([2091d0f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2091d0f12612146a427333b0991eb0dd2181c1d6))

## [1.10.16](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.15...1.10.16) (2025-04-28)

### Bug Fixes

* attempt for only running fetch task once ([fe31b59](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/fe31b59602dbf0ea5389d14fddafe7d3cf1e8048))

## [1.10.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.14...1.10.15) (2025-04-26)

### Bug Fixes

* Remove the requirement for the kubeconfig file to be provided so that it will work in aap ([fe977a1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/fe977a1fb9b30d6ac8f5e60f6308813f4f63682e))

## [1.10.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.13...1.10.14) (2025-04-24)

### Bug Fixes

* Added support for RHEL VMs ([b919991](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b9199916130e9c36c7c8c6d8cd0418be8c5a8550))

## [1.10.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.12...1.10.13) (2025-04-23)

### Bug Fixes

* revert back from testing ([bbec424](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/bbec42434dd289ee075d6979bd349c5b77b857c2))

## [1.10.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.11...1.10.12) (2025-04-23)

### Bug Fixes

* adding more vars to debug ([7c60226](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7c6022629cd0b9cc425f0ae698d8ddda3fca495b))

## [1.10.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.10...1.10.11) (2025-04-23)

### Bug Fixes

* for multiple bastions ([26fa512](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/26fa512fca356ba0c13398c06149ba25475bc620))
* for non vlan connection ([4a465ef](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4a465efdea696f70f149b73850ffeb8c57095d81))
* removed duplicate network data ([5d48536](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5d485361123555af8a331b2210b4cbfb6e07e726))

## [1.10.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.9...1.10.10) (2025-04-22)

### Bug Fixes

* [XBA_GALAXY_KUB-816](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-816), add AAP execution environment image to bundle. ([1663337](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/16633370670280f7a13e3bfe94f60434e7847d90))

## [1.10.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.8...1.10.9) (2025-04-18)

### Bug Fixes

* minor fixes ([422f5a9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/422f5a94591b48a317a704133c98c77610f4d085))

## [1.10.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.7...1.10.8) (2025-04-17)

### Bug Fixes

* Updated binaries path ([f8b57ca](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f8b57ca186a751105784e23c311ec9af66695a74))

## [1.10.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.6...1.10.7) (2025-04-17)

### Bug Fixes

* added default openshift public ip ([a176115](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a1761159aa53c4797c37741b239013a74c68b3d7))

## [1.10.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.5...1.10.6) (2025-04-17)

### Bug Fixes

* addresses issues with having a seperate network for bmcs ([c677e8b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c677e8b8a576fa6b4c1a2efd12724ddf31caacff))
* Fixes determined from venus ([2252748](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/22527483373034162fd2a9bdbcdd8ca7c07d6937))
* renaming network values ([1a4827b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1a4827b3bf33d51fd3c6478668f5c0e1c4a89c9c))

## [1.10.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.4...1.10.5) (2025-04-10)

### Bug Fixes

* SQUASH bugs ([d250512](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d250512d04f69badf0c37e7f95889b931c8ab360))

## [1.10.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.3...1.10.4) (2025-04-09)

### Bug Fixes

* Add support for ILOs on unroutable networks. ([20cbd0a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/20cbd0ac7dcd5e8db4659afe972e0c98a550bf27))

## [1.10.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.2...1.10.3) (2025-04-08)

### Bug Fixes

* Add more versions for ilo6 ([b21014d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b21014db235c0b7ee3e14f1cb4ec66246efe89a7))

## [1.10.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.1...1.10.2) (2025-04-08)

### Bug Fixes

* patch new ocp_common vars distro and platform for downstream ([1a5230a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1a5230a75ace41a6f0a765fe76bb27540f1edf7f))

## [1.10.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.10.0...1.10.1) (2025-04-08)

### Bug Fixes

* Fix bugs ([29fdf54](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/29fdf54eafa23ae2aa29e6ffac3da8e9be1e1f8a))

## [1.10.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.9.1...1.10.0) (2025-04-07)

### Features

* updated for aws ([89a10f5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/89a10f59db4bdcca1cd5dfc5dc071768676bd080))

### Bug Fixes

* Added code to automate setup of galaxy registry ([77fb589](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/77fb5897b8e9b458b5add728b687c7a75219bfaf))
* aws registry changes ([d497bc0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d497bc063524b564abca306050e6b88559c55f61))
* aws updates ([73c3a26](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/73c3a2672edebd888e1da70ffdb1357b4817bf8e))
* headers ([e49ac8e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e49ac8e28a4fc71935acc297ee2e4938bd8ddf49))
* jinja lint ([cd393a7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/cd393a768f7154b7fe1f2d6e840bead5ef4a21e2))
* Wip ([56027b2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/56027b2dfb25c3803aa9ea6f2ae5ed5285ed7570))

## [1.9.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.9.0...1.9.1) (2025-03-28)

### Bug Fixes

* upgrade option to pull in ocp 4.## channels for bundles ([29b4c07](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/29b4c0741f4279a369781f44f8a66eeb9663ea43))

## [1.9.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.12...1.9.0) (2025-03-17)

### Features

* Implemented ability for hosts to join domain ([31f9f62](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/31f9f62c0188a4075a28154e0373d3423c7854b0))
* Implemented ability to join Windows hosts to domain ([3cff0c4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/3cff0c464fdd96dac7ddc6995cf1cf6385f87de1))

## [1.8.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.11...1.8.12) (2025-03-17)

### Bug Fixes

* added changes to include galaxy env variables ([4810689](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/48106893a237df015007ab1e735a102d02e3eb6b))

## [1.8.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.10...1.8.11) (2025-03-06)

### Bug Fixes

* redfish nodes off logic fix ([65a1c4f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/65a1c4f09657e6ac15daf2e1d7658d077d4cca9f))

## [1.8.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.9...1.8.10) (2025-03-05)

### Bug Fixes

* Changes for NGI+iLO 6 ([73c0460](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/73c046098e1d94b727bf4fc42a34018c8a389d15))
* Rgister errors ([82ee114](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/82ee114e14588976d58f8c51761b889cd413cfc0))

## [1.8.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.8...1.8.9) (2025-03-05)

### Bug Fixes

* Added k8s.gcr.io ([12da857](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/12da8572203ea7c641ddb36c8a29d5895840a686))

## [1.8.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.7...1.8.8) (2025-03-03)

### Bug Fixes

* okd fcos location fix ([3828295](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/3828295b466a7089218d4754d4b378e36aec1eaa))

## [1.8.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.6...1.8.7) (2025-03-03)

### Bug Fixes

* hard coded ursl ([8283df5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8283df59da2ec0f556bb51e44729dbf802b0a3b8))

## [1.8.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.5...1.8.6) (2025-03-03)

### Bug Fixes

* Add support for 4.15 versions (mainly OKD) ([a6206ed](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a6206edd84658b5f628d1c1ae737103f51ddd7a8))

## [1.8.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.4...1.8.5) (2025-03-03)

### Bug Fixes

* testing oc mirror ([0ca9f41](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0ca9f41b93e286135bb60508ceb0454236dfeeaa))

## [1.8.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.3...1.8.4) (2025-02-28)

### Bug Fixes

* Added a version for OKD ([11b6e90](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/11b6e90a597ab1c0a6d9ac29bc8480049ea7ae9b))

## [1.8.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.2...1.8.3) (2025-02-25)

### Bug Fixes

* Include okd targets ([d03a1e1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d03a1e11d0eb7817213ed0ecf66c6c1052b2d569))

## [1.8.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.1...1.8.2) (2025-02-25)

### Bug Fixes

* static files fix ([eaf59ab](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/eaf59ab1ea04c316a04d0a2d05ae095e58e31191))

## [1.8.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.8.0...1.8.1) (2025-02-23)

### Bug Fixes

* linting error ([3dd6d8e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/3dd6d8e7971ea21ecbe7d09044af3ffed89e3e89))

## [1.8.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.55...1.8.0) (2025-02-23)

### Features

* added rhel versions ([d38f011](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d38f011f72f5fadf6810e3b95ac397cb5c0f2321))

## [1.7.55](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.54...1.7.55) (2025-02-21)

### Bug Fixes

* Updates required for openshift collection restructure ([603a8e6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/603a8e688e75323e73604a79fe5de7ded4f55d0f))

## [1.7.54](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.53...1.7.54) (2025-02-21)

### Bug Fixes

* added argument specs ([f8e5362](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f8e536287d2af5bf0f762fa19fa0c43bee038e63))
* fixed broken references ([6a785d2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6a785d2a78c5f439de0d037d537fa22a3c7069c2))
* fixed role names ([5834575](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5834575cf1b45b366a4d9deea438aecdc174028a))
* reverted back to original ([a06b24f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a06b24f3d89a18df3ceccbfbbec2225dfd4c1a93))

## [1.7.53](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.52...1.7.53) (2025-02-20)

### Bug Fixes

* update ocp_common ([50d7315](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/50d7315ae4b774d4e514a1e4ff0c1ff85252cd2e))

## [1.7.52](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.51...1.7.52) (2025-02-19)

### Bug Fixes

* renamed base roles ([a0adace](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a0adace1c30789b63caa81a1ac637d5c98e8afeb))

## [1.7.51](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.50...1.7.51) (2025-02-17)

### Bug Fixes

* Dam headers ([73c1641](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/73c16411726e70219c2076c6a3c24a2b241625c4))
* linting errors ([b3f337d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b3f337d5f049c766bc7a1aaedf3268c910452232))
* more linting ([6336fde](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6336fde1c74e634b1b2c8f7f609448f572b59d1e))
* Pylinting done ([d33fde8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d33fde89464b01b87a6bd6716480fd9992c592dc))

## [1.7.50](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.49...1.7.50) (2025-02-13)

### Bug Fixes

* fix typo for cp.icr.io ([c0d4de7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c0d4de74a4f7b32f4369052bb518b8b22f29e8fe))

## [1.7.49](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.48...1.7.49) (2025-02-11)

### Bug Fixes

* A better fix ([ea16f19](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ea16f19fa158a24ddf7f4204b3b78711b88a5c23))
* Switched back to hivestar ([6692051](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/669205104a8b42e7e60c651b042b42fd8e4a03c7))
* try again ([b9c2ae9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b9c2ae9a40d4fa11c689ca17ee8b8a7eca25a847))

## [1.7.48](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.47...1.7.48) (2025-02-09)

### Bug Fixes

* Switched to kraken ([b073766](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b073766d8c65ad04575be7d3e9775d3738ab07e1))

## [1.7.47](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.46...1.7.47) (2025-02-05)

### Bug Fixes

* added dev containers for consistent dev environments ([1866636](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/18666367f51507c5c73c6b5c04234bf79255582d))

## [1.7.46](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.45...1.7.46) (2025-01-30)

### Bug Fixes

* revert back to original galaxy_folder Link src dest ([254e535](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/254e5350202b0fd01302d6dfff13c670bd4ac03f))

## [1.7.45](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.44...1.7.45) (2025-01-30)

### Bug Fixes

* linting and headers ([0edfaab](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0edfaab0e7306936d2916c1ccc6020eca05ddf2f))

## [1.7.44](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.43...1.7.44) (2025-01-30)

### Bug Fixes

* fix bundling ([5d20e71](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5d20e71951cdb94a12dd7f289a3f7829fae0a077))

## [1.7.43](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.42...1.7.43) (2025-01-29)

### Bug Fixes

* fix linking on galaxy_files to packages_files folder ([e7a1233](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e7a123390bf3e446cd2fb4809401c4e14fad37db))

## [1.7.42](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.41...1.7.42) (2025-01-29)

### Bug Fixes

* Bad variable reuse ([9f526c0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9f526c075b095f18ea4cecff28641ed2d1ac4e96))

## [1.7.41](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.40...1.7.41) (2025-01-28)

### Bug Fixes

* Updated required rpm packages ([339841e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/339841e146d36e1da880839554cda4410fa0f0a4))

## [1.7.40](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.39...1.7.40) (2025-01-28)

### Bug Fixes

* Resolve XBA_GALAXY_CP4D-3 ([075a882](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/075a882e57304cbd65603f14d99e7dc6ceb62336))

## [1.7.39](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.38...1.7.39) (2025-01-27)

### Bug Fixes

* Added openssl for keepalived ([b767d50](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b767d502d29e0aeeef1496954ce571d6ac5c878d))

## [1.7.38](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.37...1.7.38) (2025-01-26)

### Bug Fixes

* Bad regex for libsss_idmap ([d680506](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d6805067383a491b7591f6cd27718e3d60e5732e))

## [1.7.37](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.36...1.7.37) (2025-01-25)

### Bug Fixes

* Added missing RPMs ([09c2778](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/09c27783779442e2cc76ff2a2e1f14a70419250d))

## [1.7.36](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.35...1.7.36) (2025-01-24)

### Bug Fixes

* Updated to allow multiple versions in nexus ([0d58229](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0d582291fa6842a8535a6f8ae06477a7d7392e52))

## [1.7.35](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.34...1.7.35) (2025-01-23)

### Bug Fixes

* Attempt to fix rhel boot issues ([41344b8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/41344b84b5c1f65379c4ac98d37f99fe4281601b))

## [1.7.34](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.33...1.7.34) (2025-01-20)

### Bug Fixes

* Resolve [XBA_GALAXY_KUB-1](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-1) "/fix login skip" ([8375b92](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8375b92e902d4cd98e1b3bfb93e43c01ac650502))

## [1.7.33](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.32...1.7.33) (2025-01-15)

### Bug Fixes

* XBA_GALAXY_CP4D-3 ([272fe2b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/272fe2be4127e63d89438c8fb2a2780791bd1c77))

## [1.7.32](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.31...1.7.32) (2025-01-13)

### Bug Fixes

* Adding --initlabel ensures the drives are re-initialized, clearing any potential remnants of old partitioning information that might conflict. ([81aa818](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/81aa818c6b70f60f3fdd7804117a613a52488eeb))

## [1.7.31](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.30...1.7.31) (2025-01-07)

### Bug Fixes

* Bad when condition ([318a267](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/318a2671146d065a5cb6c1d001c89d2bc0a8aec2))

## [1.7.30](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.29...1.7.30) (2024-12-19)

### Bug Fixes

* fixed issue of naming network names nee ([26a386e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/26a386e2817df3101ae05a3c12f6cd8ff5bc6348))

## [1.7.29](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.28...1.7.29) (2024-12-19)

### Bug Fixes

* Minor changes to handle login better ([00f794c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/00f794cef9853475bcf513c7205f6859bf6e685f))

## [1.7.28](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.27...1.7.28) (2024-12-18)

### Bug Fixes

* Added changes for okd install ([7e75523](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7e755236b533944c433e6bd13e5c2952d0de37fc))
* Added github proxy for ok installer ([c736c1c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c736c1cd41a601169e6f5e01b4e8d900e4137d91))

## [1.7.27](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.26...1.7.27) (2024-12-16)

### Bug Fixes

* bastion boot failing fix ([ac7114e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ac7114ee185a79dcfadb8842e29bb6c1a7a288a8))

## [1.7.26](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.25...1.7.26) (2024-12-12)

### Bug Fixes

* Removed health check to see if this actually fixes the issue were having with nexus hanging ([f838a5b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f838a5b0614de8220cabf2c1694febc5c6035720))

## [1.7.25](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.24...1.7.25) (2024-12-11)

### Bug Fixes

* Got rid of typing in function definitions to support older versions of python. ([b061d72](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b061d72688796258983c2bfd6b4b8208af1ef47b))

## [1.7.24](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.23...1.7.24) (2024-12-09)

### Bug Fixes

* Added proxy ([6339ec7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6339ec7b47e3db0a779498aa4bd07f39df5d6743))

## [1.7.23](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.22...1.7.23) (2024-12-05)

### Bug Fixes

* Update RPM list after adding fapolicyd to satellite server ([4d0162c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4d0162ca3f55696369aba65280e71dcaaf9620dc))

## [1.7.22](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.21...1.7.22) (2024-12-03)

### Bug Fixes

* Corrected bad argument spec ([1a26e17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1a26e172927cd8a910afbb37af372651c552ef4b))

## [1.7.21](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.20...1.7.21) (2024-11-27)

### Bug Fixes

* fixed logic for selecting pxe boot device ([459895a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/459895abd590ff81f65a374e121c5f187dd2d41f))

## [1.7.20](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.19...1.7.20) (2024-11-27)

### Bug Fixes

* added sample arg files for testing ([ce071af](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ce071aff17d6dbfe789c3d90f8070083a27aecc3))

## [1.7.19](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.18...1.7.19) (2024-11-26)

### Bug Fixes

* added ability to set boot nic order ([d120ece](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d120ece6ff287ac7fd318c61e75281115206cec0))
* Added better error checking and boot and ssh checking ([5d9c630](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5d9c63096f5373df380d2d45513f3c45e112a1ee))
* Added code to set ntp server on boot simultaneously ([948b0cf](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/948b0cf811e3adfd5077f1f18754accea9222a47))

## [1.7.18](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.17...1.7.18) (2024-11-21)

### Bug Fixes

* Changed sort method ([28839a2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/28839a2bf5859154ca87e732baeacd1ff33a0b76))
* Changes to support the Satellite server install ([f63f7d3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f63f7d38c0bf808a41bf062c79a534a806c5ab1e))

## [1.7.17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.16...1.7.17) (2024-11-20)

### Bug Fixes

* linting and fixing for more complicated network config ([0a720e3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0a720e397c6f87ccf554a6cc7255422e4376c496))
* Markdown Linting ([77b082d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/77b082d7b69bf5d2f236c8eab60a252f056ebb22))

## [1.7.16](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.15...1.7.16) (2024-11-11)

### Bug Fixes

* Resolve [XBA_GALAXY_KUB-1](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-1) "/default to 4.14" ([63d69fe](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/63d69fe0d4c3c135fb3e7585aa68fe82f288b605))

## [1.7.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.14...1.7.15) (2024-11-07)

### Bug Fixes

* increase long_task timeout for large bundles ([8344ba0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8344ba0c3871f6f999d74ed1323db854c493839c))

## [1.7.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.13...1.7.14) (2024-11-04)

### Bug Fixes

* Added a 5 minute timeout for the nexus service ([ca6fb4d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ca6fb4dd2b61ca164da20c9e590f9c16b59fbcfa))

## [1.7.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.12...1.7.13) (2024-10-29)

### Bug Fixes

* if conditional for the dynamicimageset ([962378d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/962378d18b2441c1b3c3bea960f950a01e5eb4b0))

## [1.7.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.11...1.7.12) (2024-10-29)

### Bug Fixes

* Resolve [XBA_GALAXY_KUB-482](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-482) ([5f7aa68](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5f7aa6892b53eccfe8582f5665b84ea6ad5e53fd))

## [1.7.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.10...1.7.11) (2024-10-22)

### Bug Fixes

* update harbor.us.lmco.com proxy ([7606e0f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7606e0f0e584f0920a9ec75fac399bdebaf245eb))

## [1.7.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.9...1.7.10) (2024-10-09)

### Bug Fixes

* Updated required version of ansible ([b091782](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b091782fb54fe30f55c553b3520c63e19313d3a3))

## [1.7.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.8...1.7.9) (2024-10-07)

### Bug Fixes

* Update roles/create_standard_ocp_files_package/templates/dynamic-image-set-configuration.yaml.j2 ([ecbe656](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ecbe656b5f3985f2ffed6bc3a225eee45376f5d2))

## [1.7.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.7...1.7.8) (2024-10-07)

### Bug Fixes

* Added icr.io ([8eb75b0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8eb75b07f1420184459304a4f0c99a42dd9e6e9c))

## [1.7.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.6...1.7.7) (2024-10-02)

### Bug Fixes

* update dynamic imageset to include ibm option ([790dbbd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/790dbbd0b46a3f39df0679ef58cd3732de878837))

## [1.7.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.5...1.7.6) (2024-09-24)

### Bug Fixes

* add no proxy to download assets ([0bec5c9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0bec5c98d19743a63a19ebb8da2a7f637b1e8fd8))
* testing block proxy stuff ([e5af800](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e5af8007a154196a711fbe9889545900c3b95f65))
* typo ([9c731df](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9c731df0a201ef57f7758f428c0ba8cef335268f))

## [1.7.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.4...1.7.5) (2024-09-24)

### Bug Fixes

* updated bundler to reflect changes in ee image ([0c4495a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0c4495ad2a1a0400f17aef540673672cf6f53707))

## [1.7.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.3...1.7.4) (2024-09-23)

### Bug Fixes

* reverting back pipeline ([563c286](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/563c2865f0e39e1ed2b8020691ad225f729e02a9))
* testing mirror reg to reg ([7e9f86c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7e9f86cb04728969700573a1e5dc1991c7243436))

## [1.7.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.2...1.7.3) (2024-09-19)

### Bug Fixes

* added no proxy for downloading nexus container ([15a3651](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/15a365153766cad7c2572c70fc22e4864add5e0f))
* cleanup and linting ([6a70ab3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6a70ab3189d4f6b4d3faea809845ba5bbfa61150))
* no proxy issue ([d7a4ab0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d7a4ab0aed3b45af7ba0474bd60a3821371ffe0d))

## [1.7.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.1...1.7.2) (2024-09-19)

### Bug Fixes

* Resolve [XBA_GALAXY_KUB-450](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-450) "/fix s3 upload" ([39a9fb1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/39a9fb18a68ba126a82356fbd7780b26795c60c1))

## [1.7.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.7.0...1.7.1) (2024-09-19)

### Bug Fixes

* Handle condition where redfish comes back without json data ([9259b7c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9259b7c1b3fa8b75b8730efb004e5c8e384625ab))

## [1.7.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.6.2...1.7.0) (2024-09-18)

### Features

* add spdx ([22be6cc](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/22be6ccbfb35e1a6fc06a12d196e6b3180b74ea7))

## [1.6.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.6.1...1.6.2) (2024-09-18)

### Bug Fixes

* Testing oc-mirror ([67dcda9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/67dcda9f7c080d4155914c3405c4e25e86cf5a47))

## [1.6.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.6.0...1.6.1) (2024-09-17)

### Bug Fixes

* Add oc mirror original icsp to records ([717f0bd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/717f0bda0e07490bb952bf6ddce3a82825624ed5))

## [1.6.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.15...1.6.0) (2024-09-17)

### Features

* updated to include etcd backup ([adb89f4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/adb89f445c25bc98dcf70da7c6519ac6cd404a87))

### Bug Fixes

* Starting branch ([e40d5c8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e40d5c872a29e5a899fe90dc66933f89dbd342ea))

## [1.5.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.14...1.5.15) (2024-09-16)

### Bug Fixes

* add cs to operator names ([0e87262](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0e8726244285c48f6a1b20b4b0da822b3f1dcc52))
* cs regex ([2adbf21](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2adbf21e7dbda64b371a844304fb77263e74f450))
* regex ([36decca](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/36deccaa7552ecd9dd11fc651c463a696c23c219))

## [1.5.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.13...1.5.14) (2024-09-12)

### Bug Fixes

* templating error that caused onstall to fail ([57afda9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/57afda919ce9b51057fb614a802d3fabe9222d90))

## [1.5.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.12...1.5.13) (2024-09-12)

### Bug Fixes

* Revert back to getting latest oc from container ([b89e14d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b89e14d1fb8a454a48055ac677711a2656269921))

## [1.5.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.11...1.5.12) (2024-09-11)

### Bug Fixes

* Added changes to get bundles working in containers ([34828cb](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/34828cb3e7ed1f2a01de34b8e512a43169bdb815))
* Added non working operators back in ([2699fb0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2699fb06ddb4999b32ef260faa22d42aef6a78a5))
* Added tasks to releive space as the process goes on ([302b715](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/302b7150d9b65bdf39d787ac319dfde5e2362434))
* changing back to manual deployment ([d8521b9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d8521b93ac1a387b625c14c42c3b1fc5dd70ffd4))
* Put pipeline back to normal ([f594c78](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f594c7831e1d661e90008e501e1849dc18f2688a))
* set all versions of oc binary ([0fd8b03](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0fd8b03bbb023a53e34bc6548a65428175b6ea6d))
* uid issues and testing ([46eee6a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/46eee6af3f911b67a80b7a9460ba6dab9f042457))
* Updating making progress toward containerization ([a35a3ed](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a35a3ed84017fd4a0ab82025c59dce814dbf78fe))

## [1.5.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.10...1.5.11) (2024-09-09)

### Bug Fixes

* update imageset generation for operator index version ([2f440cd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2f440cd5324d0465de5410e237be8c1ead75a471))

## [1.5.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.9...1.5.10) (2024-09-09)

### Bug Fixes

* Increase timeout ([ec20c4a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ec20c4ab95cd3d1d91e3a98018ba8f91290d9a62))

## [1.5.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.8...1.5.9) (2024-08-29)

### Bug Fixes

* downgrade nexus image version ([df07687](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/df076879fbf68cfa2b35c6516becffbee7e43d3d))

## [1.5.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.7...1.5.8) (2024-08-29)

### Bug Fixes

* Moved over assests to their respective collections ([f168224](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f168224a4e69ab9876c2494d20305baeb608e241))
* Set a fixed version for nexus ([06b2ccf](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/06b2ccf4e6ee1d725221d3bfbf50b46ae6983888))

## [1.5.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.6...1.5.7) (2024-08-27)

### Bug Fixes

* Moved order of operations ([418cd93](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/418cd93002b4253ce2f8311e8137918ad039fc7b))

## [1.5.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.5...1.5.6) (2024-08-27)

### Bug Fixes

* setup haproxy before downloading any assets. ([4a544b8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4a544b8792635f8a9e8641ea4c005064d1c6ca2b))

## [1.5.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.4...1.5.5) (2024-08-22)

### Bug Fixes

* renamed variables to hopefully work on both deployment infrastructures ([bb34aab](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/bb34aab3ae1dcdef32f514272f3e51c3a76fbdf5))

## [1.5.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.3...1.5.4) (2024-08-21)

### Bug Fixes

* Upated versions for OCP 4.14 upgrade ([dd51889](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/dd5188984b3b259f7050fd5925feec224366c162))

## [1.5.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.2...1.5.3) (2024-08-21)

### Bug Fixes

* Updates for OCP 4.14 upgrade ([e076330](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e0763302c57ad46088614bb23e81cbb1b7d48922))

## [1.5.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.1...1.5.2) (2024-08-16)

### Bug Fixes

* Fix for wait for bootstrap reboot ([d064667](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d064667694b5a1708b2f46a6c5595847946b68fb))

## [1.5.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.5.0...1.5.1) (2024-08-15)

### Bug Fixes

* hotfix for folder location ([16ad44c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/16ad44c73da3d0c4df720b6fcff41c6390a4d9c4))

## [1.5.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.17...1.5.0) (2024-08-15)

### Features

* okd now available as a distribution option ([06803ea](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/06803ea06d52418c777917108b5c6cbfe18c8f5e))

### Bug Fixes

* added another proxy registry ([bb7a341](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/bb7a341d7f5aa159bc169139189abc5c506d65ac))
* fixed time issue ([666728f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/666728f51a0b37d6f88ed7793fb01a2adc85b297))

## [1.4.17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.16...1.4.17) (2024-08-12)

### Bug Fixes

* Added hivestar credentials where needed ([8bcf5f0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8bcf5f0c94f3ab01942e5745f3e6957b26eb8f05))

## [1.4.16](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.15...1.4.16) (2024-08-12)

### Bug Fixes

* time setting issue ([ad38cc3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ad38cc362aad4211153214d14d005794fe7f7a15))

## [1.4.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.14...1.4.15) (2024-08-05)

### Bug Fixes

* Created an ansible action for long running tasks that can show progress ([c79dc62](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c79dc62efcb8f1ed132558029ed41a696b00a4fc))
* fixed for running remotely ([99d1486](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/99d14864ac700ce7554129512d5886b8b52400f2))

## [1.4.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.13...1.4.14) (2024-08-02)

### Bug Fixes

* Update download_assets ([d955fe4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d955fe46e4c78357764d20681372140415ed0970))

## [1.4.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.12...1.4.13) (2024-07-31)

### Bug Fixes

* changed default vault pass to non hidden file ([5090c8a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5090c8a534a66554bc4508a7b5d55147cf6eac92))

## [1.4.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.11...1.4.12) (2024-07-31)

### Bug Fixes

* Fixing duplicate ip check ([a7b7623](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a7b7623d7125694bba6d5c01ee03a0d03349d2a2))

## [1.4.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.10...1.4.11) (2024-07-30)

### Bug Fixes

* removed code that had a default and a required in the arg specs ([7bcbf9d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7bcbf9d9befd077474172143a0af1a9c988d6c4b))
* updates based on testing ([22cfbd4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/22cfbd4668bef8b2719f7c31bc373d91e32986f9))

## [1.4.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.9...1.4.10) (2024-07-30)

### Bug Fixes

* save run script as executable ([9c64976](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9c649763ab0abbd24b20cccf7c833b41fcd44580))

## [1.4.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.8...1.4.9) (2024-07-30)

### Bug Fixes

* corrected file paths for bundle ([1506d72](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1506d72eb2b711c1219e0f53c009fab7a5b20378))

## [1.4.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.7...1.4.8) (2024-07-29)

### Bug Fixes

* added a default name to the bundle for extensibility ([b0ae1e4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b0ae1e4279ced73480e88153cf161a7e272ed3d7))

## [1.4.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.6...1.4.7) (2024-07-29)

### Bug Fixes

* removed unused variables ([35384ca](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/35384ca777bd189bffc031ee82d14e5f9dead2d4))

## [1.4.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.5...1.4.6) (2024-07-29)

### Bug Fixes

* added CI variables to specify rhel version for static files ([e4591dd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/e4591dd4c1619480abfb6eaf635e68a1dfeadf34))

## [1.4.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.4...1.4.5) (2024-07-25)

### Bug Fixes

* remove mutually exclusive openshift_version var already defined in lmco_galaxy.yml ([ee05549](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ee05549e4f768ac1014daabaddc0281dbc3bd35f))

## [1.4.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.3...1.4.4) (2024-07-25)

### Bug Fixes

* Added fix message. Added regex for ipv4 elements. Added default values for some required elements. ([1d6520c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1d6520cf5a34c6e0579d70deb9d3f0d6a46f5994))

## [1.4.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.2...1.4.3) (2024-07-24)

### Bug Fixes

* Added fix commit message ([23f49ef](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/23f49ef65b94f6d40bf72852c385d86923561b12))

## [1.4.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.1...1.4.2) (2024-07-24)

### Bug Fixes

* Change the path of the ISOs ([cb34b2d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/cb34b2d2362ee802a6f58913959e0bd4a488a9f4))

## [1.4.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.4.0...1.4.1) (2024-07-22)

### Bug Fixes

* Added fix commit message ([43f8bb1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/43f8bb1da1dc55c072ca4460b1410cf6eba5e77c))
* Added min supported version ([c9d2685](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c9d268552d63034bd5f1b5d3766f2b29849f171f))
* missing quote ([a1a6831](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a1a68310dfe7592aecf07db60186f795d85b5fa7))
* ocp channel to be compatible with both min and max version ([a43ba7d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a43ba7dab3380c18622be0f55bd5c73b2d96aff9))
* Refactored bundling code to simplify process ([2028883](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/20288836dc2cef3d630d9ad8b0ad64227fb1b111))

## [1.4.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.3.3...1.4.0) (2024-07-16)

### Features

* Added support for 4.14.31 install ([c1456c8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c1456c85777d09c8f5a8bd52587cd64c6461072a))
* Added support for 4.14.31 install ([2d5c4c3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2d5c4c3d6331f8d84147f5aa9882405254e99160))

## [1.3.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.3.2...1.3.3) (2024-07-16)

### Bug Fixes

* validate certs ([4d92ad8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4d92ad8c9407ca4d4d15b6006eaa73fbde715350))

## [1.3.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.3.1...1.3.2) (2024-07-09)

### Bug Fixes

* revert back ([656760f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/656760f4b298738dde6904ac1fb4c748034e29ec))

## [1.3.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.3.0...1.3.1) (2024-07-09)

### Bug Fixes

* Update no_proxy list to str roles/create_standard_ocp_files_package/tasks/setup_nexus_vm.yml ([fcf0f79](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/fcf0f791d76d952193d515b02a9b4cf201379c1a))

## [1.3.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.2.0...1.3.0) (2024-07-08)


### Features

* Moved over downloads to proxy hivestar registry ([f260bcf](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f260bcf0e885bb96eb1ae2e36aa134fc2d80a83a))


### Bug Fixes

* Added fix message. Adds feature of speeding up oc mirror ([8d1a8d1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8d1a8d18102614b2643bb587d3438ae39a39c335))

## [1.2.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.17...1.2.0) (2024-07-04)


### Features

* added check ot make sure certs are valid ([d722176](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d7221761b798e99af8d4f097ee6c19ed2775a92a))
* created python module for checking certificates ([0a8e2cc](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0a8e2ccc2f545b18dfdf14d4555d687ce35c4f67))


### Bug Fixes

* Added checks for ip address space ([959a0de](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/959a0de9bf462bf1e69791ffc9394380f8717099))
* Added fix message. Adds feature of checking certs are valid ([eae011a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/eae011acbf054195ede02eb476f8da48eca479a8))

## [1.1.17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.16...1.1.17) (2024-06-24)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-1](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-1) "/add harbor us mirror" ([79f09be](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/79f09be62141e03780628834fc00e546a2e89a47))

## [1.1.16](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.15...1.1.16) (2024-06-20)


### Bug Fixes

* delete default keepalived conf when initially installed ([4ae8677](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4ae8677dc19cde5dc0767af59b3374fdbc401990))

## [1.1.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.14...1.1.15) (2024-06-20)


### Bug Fixes

* Updates for move to hivestar ([558fd08](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/558fd08e4a14a8e97c6e5970e77de6e62cf596a9))

## [1.1.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.13...1.1.14) (2024-06-18)


### Bug Fixes

* rhel version ([f7bcb3d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f7bcb3d42ea1fb1d55decd68e2cb4277bc206073))

## [1.1.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.12...1.1.13) (2024-06-18)


### Bug Fixes

* added note to say cluster is being deployed ([f77dbce](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f77dbce4bd32b9d137383bfb9df766be41aac75d))

## [1.1.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.11...1.1.12) (2024-06-18)


### Bug Fixes

* Using hivestar nexus, updates to packages, updates to policies ([ef3ac0e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ef3ac0ea9ce30379da94e4e9aef5cb85416fc9ae))

## [1.1.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.10...1.1.11) (2024-06-17)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-1](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-1) "/fix copy over records" ([f4cf9a2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f4cf9a278b8278e04b2d8bdff755f5425a3444d1))

## [1.1.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.9...1.1.10) (2024-06-13)


### Bug Fixes

* Removed references to galaxy version group in the image repository ([bbf075c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/bbf075cb3d853ed76a28827b1ab5fd433c5cf8ef))

## [1.1.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.8...1.1.9) (2024-06-13)


### Bug Fixes

* Checking actual ip in https mode for nexus ([394acb4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/394acb49ab32c84a839b5e8cfd369033b2c583c0))
* Customaize baremetal network bridge name ([03e42b8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/03e42b8550c3f5d5d371afe9abe76a9383141c85))
* Fixed if no provisioning network exists ([6f7484c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6f7484c267855cc7414fc8b963ca8ff0ed056859))
* Fixes discovered from MONET task ([0c81fe1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0c81fe165052d9bd4b4cd9bc3c3816bb60e7f45a))
* fixes from MONET install ([faf903a](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/faf903a396794a1522ff29ffdf75f1665b72223c))
* per cluster naming of containers ([c9f8084](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c9f808446b2351d129e6d78e236458929087ec3d))

## [1.1.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.7...1.1.8) (2024-06-13)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-1](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-1) "/add ext.ghcr.io harbor.global cache" ([9a935e5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9a935e53142736a45ce356e84e6fe8b01031d6fa))

## [1.1.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.6...1.1.7) (2024-06-07)


### Bug Fixes

* Restarting firewalld after setting up network settings ([427b3f9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/427b3f915afbb7a838b583b5ddeac93f340121d6))

## [1.1.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.5...1.1.6) (2024-06-02)


### Bug Fixes

* name of file ([61ae1a5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/61ae1a5f514090ef12d15b7dd63dc2f2e1e1beb8))

## [1.1.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.4...1.1.5) (2024-05-30)


### Bug Fixes

* for rhel 9 interzone communication ([b8ede2f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b8ede2fb393c4c9d47535bb594e5bf71e722c5ec))

## [1.1.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.3...1.1.4) (2024-05-30)


### Bug Fixes

* Updated bash script to work for both rhel 8 and rhel 9 ([4ec4237](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4ec42370ae9d8db4a9f31dff5b1244afd11fd762))

## [1.1.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.2...1.1.3) (2024-05-29)


### Bug Fixes

* for new nexus api change ([d300872](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d3008725acc1dcb467e2b697e24f0908da6c2adc))

## [1.1.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.1...1.1.2) (2024-05-29)


### Bug Fixes

* Fix for MONET's issue on installing openshift on larger clusters ([d9495dd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d9495dddc473001d652125872fc35ce403150f70))

## [1.1.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.1.0...1.1.1) (2024-05-29)


### Bug Fixes

* quick fixes for new ansible version and utc time ([7998a9b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7998a9b6df27226f04d87ea79d4056e1f5887e43))

## [1.1.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.50...1.1.0) (2024-05-29)


### Features

* Added podman health checks to all containers ([6932b65](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/6932b65b20ff8355059d627594670a07528537a4))


### Bug Fixes

* FIxes to health checks ([9373b44](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9373b4434ef3bc59151853ef19f00b46b8868fcd))
* Minor fixes ([4f8cbdb](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4f8cbdb0c02c3eba73a5bb2e3368caba2801678a))

## [1.0.50](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.49...1.0.50) (2024-05-14)


### Reverts

* Revert "fix: Copy file from local host if we are disconnected otherwise try to pull from URL" ([46671cb](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/46671cb864ae0ae63e67c39e0e4fdc7c542f5057))

## [1.0.49](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.48...1.0.49) (2024-05-14)


### Bug Fixes

* Copy file from local host if we are disconnected otherwise try to pull from URL ([7cccced](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7cccced9fa5186cd8f2c0474b8f8f874d581f49e))

## [1.0.48](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.47...1.0.48) (2024-05-13)


### Bug Fixes

* add temp dest ([d0b48bc](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/d0b48bc3950940546f88c351158b18f484790984))

## [1.0.47](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.46...1.0.47) (2024-05-12)


### Bug Fixes

* comment out disable outreach, breaks install ([71efd66](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/71efd6632f26bd6b1faa73ff399c254503ba3381))

## [1.0.46](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.45...1.0.46) (2024-05-10)


### Bug Fixes

* **XBA_GALAXY_KUB-1:** Fixed bad task name ([f242561](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f242561c1e5c8f5271a812928db799f080889b37))

## [1.0.45](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.44...1.0.45) (2024-05-10)


### Bug Fixes

* Only run 'become: true' on the tasks that are not local ([a4c5afd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a4c5afd31968b2620af5f811b1c07806387caf15))

## [1.0.44](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.43...1.0.44) (2024-05-09)


### Bug Fixes

* Added the release-signatures to the cluster ([2095e9e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2095e9e23d06d34ba4ad9d867bee4383930187e1))

## [1.0.43](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.42...1.0.43) (2024-05-09)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-277](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-277) "/galaxy bundle fix" ([04328cd](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/04328cd3653d721e12adbb63a3f7b964856f2e26))

## [1.0.42](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.41...1.0.42) (2024-05-08)


### Bug Fixes

* Fixed issues with icsp files ([f9b8e1d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f9b8e1d28b5aed0262c551a1183188982d48cc26))

## [1.0.41](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.40...1.0.41) (2024-05-08)


### Bug Fixes

* Update to reduce time in checking sha when downloading bundles ([886e9b5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/886e9b52414dbf7383c79d8a0ed840e2e66941d1))

## [1.0.40](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.39...1.0.40) (2024-05-08)


### Bug Fixes

* remove var upload_to_nexus_global ([f63c848](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/f63c848c0b70788de6b67e69aed4c09f31db6a1a))
* split the file only if you're uploading to nexus global ([c920d80](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c920d802983bbf53ee0bc3b463cd0471b4db9f30))

## [1.0.39](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.38...1.0.39) (2024-05-08)


### Bug Fixes

* fix download assets to download files from collections. ([5b6bf3d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5b6bf3d867a946ec1034e03bceb5438fcacf918b))

## [1.0.38](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.37...1.0.38) (2024-05-08)


### Bug Fixes

* Added default additonalimages ([5435870](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/54358700da635e927f96fe6ad182d292ecf696f6))
* yum update command ([eef8f5d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/eef8f5d61795d20f81cf0089b64392257a9419f7))

## [1.0.37](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.36...1.0.37) (2024-05-07)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-192](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-192) ([a32e5bf](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/a32e5bf8660cb3d88955e549c0198a464b7c33f8))

## [1.0.36](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.35...1.0.36) (2024-05-07)


### Bug Fixes

* **XBA_GALAXY_KUB-279:** Allow for custom partition sizes on the bastion server ([87fdba7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/87fdba747d5092084ee9953ed17e912c6db96444)), closes [XBA_GALAXY_KUB-279](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-279)

## [1.0.35](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.34...1.0.35) (2024-05-06)


### Bug Fixes

* **XBA_GALAXY_KUB-279:** Added Estecy, Syndeia, and GitLab operator images ([1294149](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1294149aad7056681aa623cdc90d83e98729c25d)), closes [XBA_GALAXY_KUB-279](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-279)

## [1.0.34](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.33...1.0.34) (2024-05-06)


### Bug Fixes

* virtual media installs ([b1ef28b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/b1ef28be98f141864b69ef68c1ff50b28b95a32b))

## [1.0.33](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.32...1.0.33) (2024-05-02)


### Bug Fixes

* **XBA_GALAXY_KUB-283:** Changed path to image so that the custom built images in harbor.global are used ([1b201a5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1b201a5c585e3f9d4ee241a63600c12d71bc6c74)), closes [XBA_GALAXY_KUB-283](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-283)

## [1.0.32](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.31...1.0.32) (2024-04-30)


### Bug Fixes

* make harbor cache optional now ([5e9caba](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5e9caba91ff4f67f91243abeddbe6dec521e7b87))

## [1.0.31](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.30...1.0.31) (2024-04-30)


### Bug Fixes

* move the dynamic_imageset outside of the registry_vm delegate tasks ([7a17edc](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/7a17edc8da498df6b1a5e7ab472f3488153b8228))

## [1.0.30](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.29...1.0.30) (2024-04-30)


### Bug Fixes

* fix dynamic and custom imageset options ([2ed1836](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2ed18360189cb43048749405cdde5d14906485c4))

## [1.0.29](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.28...1.0.29) (2024-04-30)


### Bug Fixes

* custom_imageset_file check ([8bad452](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/8bad452687e7f526e4214c7a082938a05710a51d))

## [1.0.28](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.27...1.0.28) (2024-04-30)


### Bug Fixes

* for multiple bastions use registry ip instead ([c2fa113](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c2fa113cbfff1e1ecab256aeec89851c174eedcb))

## [1.0.27](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.26...1.0.27) (2024-04-30)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-277](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-277) ([4d0c6b1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4d0c6b1530caec89932cad7bb39f0a2a31c1e239))

## [1.0.26](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.25...1.0.26) (2024-04-30)


### Bug Fixes

* timeout and search directory for catalog sources ([c48a026](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c48a026aeac4e07d15caa77ae6c4b5186605b116))

## [1.0.25](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.24...1.0.25) (2024-04-30)


### Bug Fixes

* for firewall forwards ([311ff93](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/311ff93feea5782ef990305adcdd687d06c3b40d))

## [1.0.24](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.23...1.0.24) (2024-04-27)


### Bug Fixes

* version check to include nebula ([c75d9a8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c75d9a8499d1452bd168beccdc6941742fa2b379))

## [1.0.23](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.22...1.0.23) (2024-04-27)


### Bug Fixes

* version and size ([4a2f799](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/4a2f799dc5489221809a52ea11fe6e470ac0c409))

## [1.0.22](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.21...1.0.22) (2024-04-26)


### Bug Fixes

* upload registry_storage path to s3 ([62998b2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/62998b25a93426f55f75a6ad875907d80f05b0f8))

## [1.0.21](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.20...1.0.21) (2024-04-24)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-169](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-169) "/download assets from redhat_platform ocp products" ([2d02d54](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/2d02d54503f6890a0c2939cf79bcfd4e9e472496))

## [1.0.20](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.19...1.0.20) (2024-04-23)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-267](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-267) "/dynamic imageset building" ([9d503b1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9d503b10bd549eca11748187aa977b990a10a3e7))

## [1.0.19](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.18...1.0.19) (2024-04-17)


### Bug Fixes

* **XBA_GALAXY_KUB-198:** Changed the size of /opt and removed specific versions from gitlab operator ([36d5bdb](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/36d5bdb616601acbfaa54417e87dd7825ebfd303)), closes [XBA_GALAXY_KUB-198](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-198)

## [1.0.18](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.17...1.0.18) (2024-04-16)


### Bug Fixes

* **XBA_GALAXY_KUB-198:** Changed the version of the operator ([c2dc91c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c2dc91c15b03ea80b566bbd13d978827063c3f2a)), closes [XBA_GALAXY_KUB-198](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-198)

## [1.0.17](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.16...1.0.17) (2024-04-16)


### Bug Fixes

* Resolve [XBA_GALAXY_SUST-25](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_SUST-25) "/add download for mirror registry images" ([1b0dcf8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1b0dcf802a34aa50943458ff1a016f9f311a64dd))

## [1.0.16](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.15...1.0.16) (2024-04-16)


### Bug Fixes

* Dynamic / Custom Imageset capability based on lmco_openshift_product_list ([ecf39c0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ecf39c0e88cb3f3aa14ec53b22e7728d2c0260be))

## [1.0.15](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.14...1.0.15) (2024-04-15)


### Bug Fixes

* **XBA_GALAXY_KUB-198:** Added gitlab operator to the custom image set ([471321c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/471321c3bba2c7acc2b6a49b0ff3c4444678b046)), closes [XBA_GALAXY_KUB-198](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-198)

## [1.0.14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.13...1.0.14) (2024-4-9)


### Bug Fixes

* add knative serverless images ([501a0c4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/501a0c462171edf6f7295577e5cedfea565989a6))

## [1.0.13](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.12...1.0.13) (2024-4-4)


### Bug Fixes

* Fixed the tar format ([fa70891](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/fa708919660b30446f2e22e660dede7eb8cbb5a6))

## [1.0.12](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.11...1.0.12) (2024-4-1)


### Bug Fixes

* Removing requirement for provisioning network since its not needed for virtual media install ([63205b1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/63205b184957fedcfe28ad2a1c5d8dfbb2368c6b))

## [1.0.11](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.10...1.0.11) (2024-4-1)


### Bug Fixes

* **XBA_GALAXY_KUB-47:** Update the version for Cygnus ([92fa292](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/92fa292f06f64814d5dbaa03f8993ba85fcd017d)), closes [XBA_GALAXY_KUB-47](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-47)

## [1.0.10](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.9...1.0.10) (2024-3-28)


### Bug Fixes

* Resolve [XBA_GALAXY_KUB-115](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-115) "/mbe syndeia download s3" ([bb88b14](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/bb88b14e9673e34cd93fae5a894ce393d083608a))

## [1.0.9](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.8...1.0.9) (2024-3-27)


### Bug Fixes

* **XBA_GALAXY_KUB-47:** Updated version ([361cb0f](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/361cb0fe7d9311bc140aee75aec62889d114b8ba)), closes [XBA_GALAXY_KUB-47](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-47)

## [1.0.8](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.7...1.0.8) (2024-3-12)


### Bug Fixes

* **XBA_GALAXY_KUB-32:** Changed the path for nmstate ([382d98e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/382d98e28960037a1da0fdb8056473fb7605013e)), closes [XBA_GALAXY_KUB-32](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_KUB-32)

## [1.0.7](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.6...1.0.7) (2024-2-20)


### Bug Fixes

* update imageset to use latest trident images 23.10 ([9ba5539](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9ba553946e60a62724dd398191df0ebb477b1688))

## [1.0.6](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.5...1.0.6) (2024-2-20)


### Bug Fixes

* **XBA_GALAXY_AUTO-1006:** Added a verify section to keepalived ([01d483c](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/01d483cd6188c83c98363cf43c44f4dd996ba6fb)), closes [XBA_GALAXY_AUTO-1006](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_AUTO-1006)

## [1.0.5](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.4...1.0.5) (2024-2-19)


### Bug Fixes

* add in upload registry-storage to s3 option ([fa2467d](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/fa2467dbab7b2aab54ebcfeff0630849cbfd83ca))
* Mirror from Red Hat more stable and efficient, retains workspace so if it retries, we don't re-download ~90GB+ of image blob layers ([5d2a88e](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5d2a88ef1aa02c22ae86e482705680c369749460))
* remove trident-ctl images due to manifests error, add in s3 retries ([c362dc2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c362dc2ddecc8c0892ab72d9df4dad7ef0ec0647))
* update amq-broker operator --> 7.11.6-opr-2 ([07a37c2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/07a37c2ba666da8916caaa4d0385cfcbe19cd7af))
* use new registry builder vm with 800GB of storage ([ffef571](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/ffef57100a367f0c8b81ba9e0e0c8048c743a657))
* Use the copy module instead of shell cp ([1a5561b](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/1a5561b5efd33037e4a24ff22d02d37680b54b2e))

## [1.0.4](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.3...1.0.4) (2024-2-15)


### Bug Fixes

* **XBA_GALAXY_AUTO-861:** Added NodePort range to the firewall ([c8df647](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/c8df6474c614977e963902f54ae33e9090e697f0)), closes [XBA_GALAXY_AUTO-861](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_AUTO-861)

## [1.0.3](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.2...1.0.3) (2024-2-15)


### Bug Fixes

* do not need the chartmuseum image ([9e128b0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/9e128b0c5122a59a568c732983b00aa04179d9ac))

## [1.0.2](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.1...1.0.2) (2024-2-14)


### Bug Fixes

* **XBA_GALAXY_AUTO-861:** Added nodeports to the HAProxy Configuration ([456ca85](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/456ca8518105979777efbec1a7c28f0daa8b8b32)), closes [XBA_GALAXY_AUTO-861](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_AUTO-861)

## [1.0.1](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/1.0.0...1.0.1) (2024-2-12)


### Bug Fixes

* add in rhel8 and rhel9 kvms ([0398a48](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/0398a48f0442666411b4c59d4c39969558e24054))

## [0.2.0](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/compare/0.1.31...0.2.0) (2024-2-12)


### Features

* [XBA_GALAXY_AUTO-696](https://ebstools-jira.us.lmco.com/browse/XBA_GALAXY_AUTO-696): Initial Semantic Release merge ([5398485](https://gitlab.global.lmco.com/galaxy/internal/lmco/openshift/commit/5398485dc369ada7e7fd1e1e535c935ecbee53a6))
