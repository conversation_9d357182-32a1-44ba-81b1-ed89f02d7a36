# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        filter_tester.yml                                                 #
# Version:                                                                        #
#               2023-11-01 espy                                                   #
#               2023-11-02 espy                                                   #
#               2024-01-26 espy                                                   #
#               2024-03-07 espy                                                   #
#               2024-06-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-11-01                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Test collection plugins
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Test IP generator
      ansible.builtin.debug:
        msg: "{{ '***************/26' | lmco.openshift.cidr_to_ips }}"

    - name: Test Get Defualt MAC ILO4
      ansible.builtin.debug:
        msg: "{{ bmc_info | lmco.openshift.get_default_mac }}"
      vars:
        bmc_info:
          username: Administrator
          password: password
          ipv4_address: ************

    - name: Test Get Defualt MAC ILO5
      ansible.builtin.debug:
        msg: "{{ bmc_info | lmco.openshift.get_default_mac }}"
      vars:
        bmc_info:
          username: Administrator
          password: password
          ipv4_address: ************

    - name: Test Get Defualt MAC ILO6
      ansible.builtin.debug:
        msg: "{{ bmc_info | lmco.openshift.get_default_mac }}"
      vars:
        bmc_info:
          username: Administrator
          password: password
          ipv4_address: ************

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '*************/24' | lmco.openshift.cidr_prefix }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '***********/16' | lmco.openshift.cidr_prefix }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '*************/24' | lmco.openshift.cidr_subnet }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '***********/26' | lmco.openshift.cidr_subnet }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '***********/24' | lmco.openshift.cidr_ip('125') }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '*************/25' | lmco.openshift.cidr_gen(1) }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '*************/25' | lmco.openshift.cidr_gen('2') }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '*************/25' | lmco.openshift.cidr_gen(4) }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '*************/25' | lmco.openshift.cidr_gen('5') }}"

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '***********/24' | lmco.openshift.cidr_ip('-5') }}"
      ignore_errors: true
      register: result

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '***********/24' | lmco.openshift.cidr_ip('258') }}"
      ignore_errors: true
      register: result

    - name: Test CIDR get lookup plugin
      ansible.builtin.debug:
        msg: "{{ '***************/26' | lmco.openshift.cidr_ip('125') }}"
      ignore_errors: true
      register: result
