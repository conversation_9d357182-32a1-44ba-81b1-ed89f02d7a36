# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        get_password.yml                                                  #
# Version:                                                                        #
#               2023-11-28 espy                                                   #
#               2024-01-15 espy                                                   #
#               2024-01-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-11-28                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Get lab passwords
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Get lab 00 password
      ansible.builtin.debug:
        msg: "{{ lookup('password', '/dev/null', chars=['ascii_lowercase', 'digits'], length=15, seed='ocp-wtn1-lab00') }}"

    - name: Get lab 09 password
      ansible.builtin.debug:
        msg: "{{ lookup('password', '/dev/null', chars=['ascii_lowercase', 'digits'], length=15, seed='ocp-wtn1-lab09') }}"

    - name: Get lab 10 password
      ansible.builtin.debug:
        msg: "{{ lookup('password', '/dev/null', chars=['ascii_lowercase', 'digits'], length=15, seed='ocp-wtn1-lab10') }}"
