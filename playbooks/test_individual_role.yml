# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test_individual_role.yml                                          #
# Version:                                                                        #
#               2023-06-20 espy                                                   #
#               2023-07-03 espy                                                   #
#               2023-07-05 espy                                                   #
#               2023-08-16 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-12-20 espy                                                   #
#               2024-02-11 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-06-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

---
- name: Install openshift and it's dependencies
  hosts: bastion
  tasks:
    - name: Test Role
      ansible.builtin.include_role:
        name: lmco.openshift.setup_install_config
