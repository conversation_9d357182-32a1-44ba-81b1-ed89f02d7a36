# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test_login_role.yml                                               #
# Version:                                                                        #
#               2024-02-15 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-15                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

---
- name: Test Succesful login with kubeconfig
  hosts: localhost
  tasks:
    - name: Generate KS lines
      ansible.builtin.set_fact:
        ks_lines: "{{ lookup('lmco.openshift.nmstate2kickstart', nmstate_sample) }}"
      vars:
        nmstate_sample:
          external_connection_name: lmi
          routes:
            config:
              - destination: 0.0.0.0/0
                next-hop-interface: lmi
                next-hop-address: **************
          interfaces:
            - name: ens2f0np0
              type: ethernet
              mtu: 9000
            - name: ens5f0np0
              type: ethernet
              mtu: 9000
            - name: megabond
              profile-name: megabond
              type: bond
              mtu: 9000
              link-aggregation:
                mode: 802.3ad
                options:
                  miimon: '100'
                port:
                  - ens2f0np0
                  - ens5f0np0
            - name: lmi
              profile-name: lmi
              type: vlan
              mtu: 9000
              vlan:
                base-iface: megabond
                id: 231
              ipv4:
                enabled: true
                address:
                  - ip: **************
                    prefix-length: 23
            - name: bmc
              profile-name: bmc
              type: vlan
              mtu: 9000
              vlan:
                base-iface: megabond
                id: 2398
              ipv4:
                enabled: true
                address:
                  - ip: ************
                    prefix-length: 22
            - name: baremetal
              type: vlan
              mtu: 9000
              vlan:
                base-iface: megabond
                id: 2425
            - name: provisioning
              type: vlan
              mtu: 9000
              vlan:
                base-iface: megabond
                id: 2426

    - name: Display lines
      ansible.builtin.debug:
        var: ks_lines
