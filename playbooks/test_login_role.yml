# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test_login_role.yml                                               #
# Version:                                                                        #
#               2024-02-15 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-15                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

---
- name: Test Succesful login with kubeconfig
  hosts: localhost
  tasks:
    - name: Remove .kube
      ansible.builtin.file:
        path: ~/.kube
        state: absent

    - name: Test Succesful login with kubeconfig
      ansible.builtin.include_role:
        name: lmco.openshift.ocp_login
      vars:
        galaxy_ocp:
          openshift_cluster: ocp-wtn1-lab09
          kubeconfig_file: /openshift_build/secrets/lab9kubeconfig

- name: Test Succesful login with crednetials
  hosts: localhost
  tasks:
    - name: Remove .kube
      ansible.builtin.file:
        path: ~/.kube
        state: absent

    - name: Test Succesful login with crednetials
      ansible.builtin.include_role:
        name: lmco.openshift.ocp_login
      vars:
        galaxy_ocp:
          openshift_cluster: ocp-wtn1-lab09
          cluster:
            username: kubeadmin
            password: 8iVFq-okgeC-pWjki-tV7jN

- name: Test successful login with api_key
  hosts: localhost
  tasks:
    - name: Remove .kube
      ansible.builtin.file:
        path: ~/.kube
        state: absent

    - name: Test successful login with api_key
      ansible.builtin.include_role:
        name: lmco.openshift.ocp_login
      vars:
        galaxy_ocp:
          openshift_cluster: ocp-wtn1-lab09
          cluster:
            api_key: sha256~HQaDqUf0nxGUWQQPrUc5hXo5VSmAwuPa5NgkYniMBPs

- name: Test Failed login 1
  hosts: localhost
  tasks:
    - name: Remove .kube
      ansible.builtin.file:
        path: ~/.kube
        state: absent

    - name: Test Failed login
      ansible.builtin.include_role:
        name: lmco.openshift.ocp_login
      vars:
        galaxy_ocp:
          openshift_cluster: ocp-wtn1-lab09
