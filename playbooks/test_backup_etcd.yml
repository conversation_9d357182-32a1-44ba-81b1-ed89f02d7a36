# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        test_backup_etcd.yml                                              #
# Version:                                                                        #
#               2024-09-11 espy                                                   #
#               2024-09-16 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-09-11                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Test etcd backup
  hosts: localhost
  tasks:
    - name: Test removal of installation of etcd backup
      ansible.builtin.include_role:
        name: lmco.openshift.ocp_etcd_backup
        tasks_from: remove
      vars:
        lmco_ocp:
          openshift_cluster: ocp-wtn1-lab09

    - name: Test install of installation of etcd backup
      ansible.builtin.include_role:
        name: lmco.openshift.ocp_etcd_backup
      vars:
        lmco_ocp:
          openshift_cluster: ocp-wtn1-lab09
          etcd_backup_settings:
            schedule: "*/5 * * * *"
            nexus:
              username: "{{ lookup('env', 'NEXUS_USER') }}"
              token: "{{ lookup('env', 'NEXUS_PASSWORD') }}"
              repository: https://{{ lookup('env', 'NEXUS_URL') }}/repository/space-station
