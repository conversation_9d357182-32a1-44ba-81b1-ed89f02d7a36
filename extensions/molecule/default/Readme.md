# Molecule Test Setup and Execution

This documentation provides instructions for setting up a local environment to run Molecule tests for this repository.

## Driver-Agnostic Installation Guide

This guide provides instructions for setting up the environment and running Molecule tests for Docker driver.

### Requirements

General Molecule dependencies:

- Refer to the official documentation: [Molecule Installation Guide](https://ansible.readthedocs.io/projects/molecule/installation/)

Additional dependencies (based on the driver you choose):

- **Docker Driver**: Docker Engine, `docker` Python library

## Prerequisites

Ensure the following requirements are met before proceeding:

1. **Python**: Install Python 3.11 or higher.

2. **Dependencies**: The following Python packages are required:

   - `ansible==9.9.0`
   - `ansible-compat==24.10.0`
   - `ansible-lint==24.10.0`
   - `ansible-builder==3.1.0`
   - `ansible-runner==2.4.1`
   - `molecule==25.4.0`
   - `pywinrm[credssp]`
   - `yamllint`

## Setup Instructions

Follow these steps to set up the environment and run Molecule tests:

1. **Clone the Repository**:

   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. **Create a Python Virtual Environment**:

   ```bash
   python -m venv env
   ```

3. **Activate the Virtual Environment**:

   - On Windows:

     ```bash
     .\env\Scripts\activate
     ```

   - On macOS/Linux:

     ```bash
     source env/bin/activate
     ```

4. **Navigate to the Molecule Directory**:

   Change to the `extension` directory in the repository:

   ```bash
   cd extension
   ```

5. **Install Dependencies**:

   Install the required Python packages listed in `molecule/default/requirements.txt`:

   ```bash
   pip install -r requirements.txt
   ```

6. **Set Environment Variables and Login to the Cluster**:

   Before running the Molecule tests, export the required environment variables and log in to the cluster to retrieve necessary information:

   ```bash
   export CICD_GALAXY_DEPLOYER_DOMAIN="<domain>"
   export CICD_GALAXY_DEPLOYER_HOST="<host>"
   export CICD_GALAXY_DEPLOYER_TOKEN="<token>"

7. **Run Molecule Tests**:

   Specify the driver and execute the Molecule tests:

   ```bash
   molecule test
   ```

## Notes

- Ensure the required tools (e.g., Docker, Podman, AWS CLI, Vagrant) are installed and configured for the driver you intend to use (Docker for this repo).

- If you encounter any issues, verify that all dependencies are installed correctly and that the virtual environment is activated.

## Troubleshooting

If you see the message `It looks like you may not have tests set up in this repository yet`, ensure that:

- The `molecule.yml` file is present in the `extension` directory.
- The `molecule` command is installed and accessible in your virtual environment.

For further assistance, refer to the [Molecule documentation](https://molecule.readthedocs.io/).

For other installation steps, refer to this link: [Dockerfile-ae](https://gitlab.global.lmco.com/galaxy/internal/lmco/galaxy-utils/-/blob/main/Dockerfile-ae#L60)
