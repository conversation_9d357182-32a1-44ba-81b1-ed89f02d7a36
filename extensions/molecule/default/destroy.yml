---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        destroy.yml                                                       #
# Version:                                                                        #
#               2024-08-08 Initial                                                #
# Create Date:  2025-04-11                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)                  #
# Description:                                                                    #
#                Molecule Destroy template                                        #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Cleanup
  hosts: localhost
  connection: local
  ignore_errors: true
  gather_facts: false
  tasks:
    - name: Create | Molecule Version Check (Just for pipeline purposes)
      ansible.builtin.command: molecule --version
      changed_when: true

    # - name: Destroy | Include application deletion tasks
    #   ansible.builtin.include_role:
    #     name:  lmco.<role name>   # TO DO add the actual destroy role
