---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        converge.yml                                                      #
# Version:                                                                        #
#               2024-08-08 Initial                                                #
# Create Date:  2025-04-11                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> N<PERSON> (<EMAIL>)                  #
# Description:                                                                    #
#               Molecule Converge template                                        #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: Converge
  hosts: localhost
  tasks:
    - name: Create | Molecule Version Check (Just for pipeline purposes)
      ansible.builtin.command: molecule --version
      changed_when: true

    # - name: Converge | include installation tasks
    #   ansible.builtin.include_role:
    #     name:  lmco.<role name>   # TO DO add the actual install role
    #     apply:
    #       delegate_to: localhost
