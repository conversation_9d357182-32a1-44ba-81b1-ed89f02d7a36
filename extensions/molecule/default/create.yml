---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create.yml                                                        #
# Version:                                                                        #
#               2025-05-21 Initial                                                #
# Create Date:  2025-05-21                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)               #
# Description:                                                                    #
#               Initial commit for create.yml                                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# TO DO add single node cluster  collection/role
- name: Create | Molecule instance
  hosts: localhost
  tasks:
    - name: Create | Molecule Version Check (Just for pipeline purposes)
      ansible.builtin.command: molecule --version
      changed_when: true
