---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        prepare.yml                                                       #
# Version:                                                                        #
#               2025-05-22 Initial                                                #
# Create Date:  2025-05-22                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)               #
# Description:                                                                    #
#               Initial commit for prepare.yml                                    #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Prepare
  hosts: all
  tasks:
  # Future plans: add prepare role if required by the collection
    - name: Prepare | Molecule Version Check (Just for pipeline purposes)
      ansible.builtin.command: molecule --version
      changed_when: true
