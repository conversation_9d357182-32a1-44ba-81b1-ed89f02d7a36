---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        molecule.yml                                                      #
# Version:                                                                        #
#               2024-05-22 Initial                                                #
# Create Date:  2025-05-22                                                        #
# Author:        Ch<PERSON><PERSON><PERSON> Ndeh (<EMAIL>)                 #
# Description:                                                                    #
#                Molecule definition template                                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# TO DO this needs to be refactored to suit this collection
dependency:
  name: galaxy
  options:
    requirements-file: requirements.yml

driver:
  name: default
  options:
    managed: false
    ansible_connection_options:
      ansible_connection: local

lint: |
  yamllint -c gitlab/.ansible-lint .
  ansible-lint

platforms:
  - name: instance

provisioner:
  name: ansible
  config_options:
    defaults:
      vault_password_file: "/home/<USER>/.vpw"
  options:
    vvv: true
  inventory:
    group_vars:
      all:
        lmco_ocp:
          hostname: "{{ lookup('env', 'CICD_GALAXY_DEPLOYER_DOMAIN') }}"
          openshift_cluster: "{{ lookup('env', 'CICD_GALAXY_DEPLOYER_CLUSTER') }}"
          cluster:
            api_key: "{{ lookup('env', 'CICD_GALAXY_DEPLOYER_TOKEN') }}"
            host: "{{ lookup('env', 'CICD_GALAXY_DEPLOYER_HOST') }}"
        ansible_extra_vars: "{{ lookup('env', 'COLLECTION_EXTRA_VARS') | default('{}', true) | from_json }}"
        lmco_cp4d:
          namespace: cpd-operators
          STG_CLASS_BLOCK: perf-no-snap
          STG_CLASS_FILE: perf-no-snap

  playbooks:
    prepare: prepare.yaml
    Create: create.yml
    converge: converge.yml
    verify: verify.yml
    destroy: destroy.yml

verifier:
  name: ansible

scenario:
  name: default
  test_sequence:
    - dependency
    - cleanup
    - destroy
    - create
    - prepare
    - syntax
    - converge
    - verify
    - cleanup
    - destroy
