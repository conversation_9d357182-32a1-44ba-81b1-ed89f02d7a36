# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        verify.yml                                                        #
# Version:                                                                        #
#               2024-05-22 Initial                                                #
# Create Date:  2025-05-22                                                        #
# Author:       Ch<PERSON><PERSON><PERSON> (<EMAIL>)                  #
# Description:                                                                    #
#                Molecule verify template                                         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
---
- name: Verify installation
  hosts: localhost
  ignore_errors: true
  tasks:
    - name: Create | Molecule Version Check (Just for pipeline purposes)
      ansible.builtin.command: molecule --version
      changed_when: true

    # - name: Verify | Include verification tasks
    #   ansible.builtin.include_role:
    #      name:  lmco.<role name>   # TO DO add the actual verify role
    #       delegate_to: localhost
