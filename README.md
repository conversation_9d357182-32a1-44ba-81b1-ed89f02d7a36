# Ansible Collection - lmco.openshift

This collection is for installing openshift on several different media.

IN order to run this collection first fill out the inventory file with the approperiate variables for your install.

Provide a node_info file that has all information on nodes that will be used for the openshift cluster.

A sample of these files is provided in the sample folder.

## Local Install Setup

```bash
rm lmco-openshift-1.0.0.tar.gz
rm -rf ~/.ansible/collections/ansible_collections/lmco/openshift
ansible-galaxy collection build
ansible-galaxy collection install lmco-openshift-1.0.0.tar.gz
ansible-galaxy install -r requirements.yml
ansible-playbook ~/.ansible/collections/ansible_collections/lmco/openshift/playbooks/setup-ipi-install.yml -i ./cmm-inventory.yaml
```

Then finally run the collection by executing the following command.

`ansible-playbook lmco.openshift.setup-ipi-install.yml -i ./inventory.yaml`

## Setup Ansible Environment (AE)

```bash
alias ae='pr harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest'
pr ()
{
    read uid name <<< "$(podman run --rm $1 id  | sed 's?uid=\([0-9]\+\)(\([a-z]\+\)) .*?\1 \2?g')";
    workdir="/home/<USER>/$(basename $(pwd))";
    container_id=$(podman run -itd --rm --uidmap=0:1:${uid} --uidmap=${uid}:0:1 -v $(pwd):${workdir}:Z -w ${workdir} -p 8080:80 $1 bash)
    podman cp ~/.ssh ${container_id}:/home/<USER>/
    podman exec ${container_id} bash -c "ansible-galaxy install -r requirements.yml"
    podman exec -u 0 ${container_id} bash -c "chmod 777 /var/www/html; chmod 755 /home/<USER>/usr/sbin/httpd"
    podman exec -it ${container_id} bash
    echo "Exiting container"
    podman rm -f ${container_id}
}
```

## Debugging Guide

Make sure boot strap node spins up

```bash
#Run on bastion as kni user
sudo virsh list --all
sudo virsh console <name-of-vm>
```

Check if bootstrap node is able to pull images

```bash
#SShed into bootstrap node as core user
curl https://registry.<ocp.hostname>:5000/v2/_catalog
curl https://registry.<ocp.hostname>:5000/v2/openshift/release/tags/list
curl https://registry.<ocp.hostname>:5000/health/instance
```

Check if podman pull works on image repo

```bash
#SSHed into bootstrap node as root user
podman pull quay.io/openshift-release-dev/ocp-v4.0-art-dev@sha256:3d1da2d63a1ec480a11746543f796a9a84c28f65cf833cd67e69833e2c7074ab
```

Check if boot strap node can talk to master node BMCs

```bash
#SSHed into bootstrap node as core user
curl -k -u <baremetal_list[0].bmc.username>:<baremetal_list[0].bmc.password> https://<baremetal_list[0].bmc.ipv4_address>/redfish/v1/Systems/1
```
