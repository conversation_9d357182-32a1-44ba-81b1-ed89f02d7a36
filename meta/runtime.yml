# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        runtime.yml                                                       #
# Version:                                                                        #
#               2023-05-15 espy                                                   #
#               2023-10-11 Sam<PERSON>, Landon                                           #
#               2023-10-12 Sam<PERSON>, Landon                                           #
#               2024-10-08 Sam<PERSON>, <PERSON>                                           #
# Create Date:  2023-05-15                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

---
# ------------------------------------------------------------------------------- #
# Collections must specify a minimum required ansible version to upload           #
# to galaxy                                                                       #
# ------------------------------------------------------------------------------- #
requires_ansible: '>=2.16.11'

# ------------------------------------------------------------------------------- #
# Content that Ansible needs to load from another location or that has            #
# been deprecated/removed                                                         #
# ------------------------------------------------------------------------------- #
# plugin_routing:
#   action:

# ------------------------------------------------------------------------------- #
# Python import statements that Ansible needs to load from another location       #
# import_redirection:                                                             #
# ------------------------------------------------------------------------------- #
#   ansible_collections.ns.col.plugins.module_utils.old_location:
#     redirect: ansible_collections.ns.col.plugins.module_utils.new_location

# https://docs.ansible.com/ansible/latest/dev_guide/developing_collections_structure.html
