# Required variables for OCP deployment
# Define the galaxy variable structure
galaxy:
  project_name: "your-cluster-name"  # Replace with your actual cluster name
  ocp:
    hostname: "your-cluster.domain.com"  # Replace with your actual domain
    version: "4.18.20"  # Replace with your desired OCP version
    install_file_name: "openshift-install"
  nexus:
    username: "admin"  # Replace with your nexus username
    password: "your-nexus-password"  # Replace with your nexus password
    hostname: "nexus.your-domain.com"  # Replace with your nexus hostname
    repo_name: "galaxy-repo"  # Replace with your repo name
    storage_file_name: "nexus-storage.tar"
  registry:
    hostname: "registry.your-domain.com"  # Replace with your registry hostname
    initial_url: "registry.your-domain.com:8082"
    crt_file: "registry.crt"
  rhel:
    major_version: 9
    minor_version: 5
    patch_version: 0
    packages_file_name: "rhel-packages.tar"
  verson_vars_file_name: "version-vars.yml"

# Define the required OCP override variables
galaxy_ocp: {}  # Override specific galaxy OCP settings here

lmco_ocp:
  # Add any LMCO-specific overrides here
  # Example:
  # hostname: "custom-hostname.domain.com"
  # openshift_cluster: "custom-cluster-name"

cli_ocp: {}  # CLI-specific overrides

# Define galaxy_hosts if using dynamic inventory
galaxy_hosts: []

# Environment variables (set these in your environment or here)
# GALAXY_BUILD_LMI_IP: "*************"
# GALAXY_BUILD_MGMT_IP: "*************" 
# GALAXY_BUILD_PORT: "8080"
