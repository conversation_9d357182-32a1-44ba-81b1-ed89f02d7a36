{"version": "2.0.0", "tasks": [{"label": "setup", "type": "shell", "command": "bash", "args": ["${workspaceFolder}/dev_files/install-collection.sh"]}, {"label": "Run j2lint", "type": "shell", "command": "j2lint ${file}", "problemMatcher": {"owner": "jinja", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": [{"regexp": "^(.*):(\\d+):(\\d+): (.*)$", "file": 1, "line": 2, "column": 3, "message": 4}]}}]}