{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug: Long Running Task",
            "type": "debugpy",
            "request": "launch",
            "program": "~/.local/bin/ansible-playbook",
            "args": [
                "lmco.openshift.test_long_running_task.yml"
            ],
            "console": "integratedTerminal",
            "justMyCode": false,
            "preLaunchTask": "setup"
        },
        {
            "name": "Debug: Nmstate to Ks",
            "type": "debugpy",
            "request": "launch",
            "program": "~/.local/bin/ansible-playbook",
            "args": [
                "lmco.openshift.test_nmstate_to_ks.yml"
            ],
            "console": "integratedTerminal",
            "justMyCode": false,
            "preLaunchTask": "setup"
        },
        {
            "name": "Debug: Ansible Nic Boot Order",
            "type": "debugpy",
            "request": "launch",
            "module": "plugins.modules.set_nic_boot_order",
            "console": "internalConsole",
            "args": [
                "${workspaceFolder}/plugins/tests/set_nic_args.json"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}:/home/<USER>/.ansible/collections"
            },
            "justMyCode": false
        },
        {
            "name": "Debug: Captive Portal",
            "type": "debugpy",
            "request": "launch",
            "module": "plugins.modules.captive_portal",
            "console": "internalConsole",
            "args": [
                "${workspaceFolder}/plugins/tests/captive_portal_args.json"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "Debug: Ansible Wait Boot",
            "type": "debugpy",
            "request": "launch",
            "module": "plugins.modules.async_wait_for_boot",
            "console": "internalConsole",
            "args": [
                "${workspaceFolder}/plugins/tests/wait_boot_args.json"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "Debug: Ansible Wait SSH",
            "type": "debugpy",
            "request": "launch",
            "module": "plugins.modules.async_wait_for_ssh",
            "console": "internalConsole",
            "args": [
                "${workspaceFolder}/plugins/tests/wait_ssh_args.json"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "Debug: Ansible Playbook filter",
            "type": "debugpy",
            "request": "launch",
            "module": "ansible.cli.playbook", // This tells VS Code to run the ansible-playbook module
            "args": [
                "playbooks/filter_tester.yml"
            ],
            "console": "internalConsole",
            "justMyCode": false
        }
    ]
}
