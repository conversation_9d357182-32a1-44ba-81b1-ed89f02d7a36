#!/bin/bash
cp_url='cpauth.us.lmco.com'
DSSIGNIN='url_default'
username=$1
PIN=$2
echo "${cp_url}	FALSE	/	TRUE	0	DSSigninNotif	1" > cookies.txt
initial_response=$(curl -s --location -X POST "https://${cp_url}/dana-na/auth/${DSSIGNIN}/welcome.cgi" --cookie cookies.txt --cookie-jar cookies.txt)
xs_token=$(echo "$initial_response" | sed -n 's/.*<input id="xsauth_token"[^>]*value="\([^"]*\)".*/\1/p')
echo "XS TOKEN: $xs_token"
data_raw="tz_offset=-300&clientMAC=&xsauth_token=${xs_token}&username=${username}&password=${PIN}&realm=LMDC_CP_Realm&btnSubmit=Sign+In"
echo "DATA RAW: $data_raw"
login_response1=$(curl -s --location -c cookies.txt -b cookies.txt -X POST --data-raw ${data_raw} "https://${cp_url}/dana-na/auth/${DSSIGNIN}/login.cgi" )
heartbeat_msg=$(echo $login_response1 | tr -d '\n' | sed 's?.*"\([a-f0-9]\{32\}\)".*?\1?')
heart_beat_data="heartbeat=1&clientlessEnabled=1&sessionExtension=1&notification_originalmsg=&instruction_originalmsg=${heartbeat_msg}"
echo "HEARTBEAT DATA: ${heart_beat_data}"
ITERATIONS=$((12 * 60))
for ((i=1; i<=ITERATIONS; i++))
do
    curl -s -b cookies.txt --data-raw ${heart_beat_data} "https://${cp_url}/dana/home/<USER>"
    echo -e "--- Waiting 60 seconds ---\n"
    sleep 60
done