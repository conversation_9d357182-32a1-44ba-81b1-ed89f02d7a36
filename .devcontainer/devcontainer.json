// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "Galaxy",
	"image": "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest",
	"workspaceFolder": "/home/<USER>/.ansible/collections/ansible_collections/lmco/openshift",
	"workspaceMount": "source=${localWorkspaceFolder},target=/home/<USER>/.ansible/collections/ansible_collections/lmco/openshift,type=bind,consistency=cached",
	"customizations": {
		"vscode": {
			"extensions": [
				"redhat.ansible",
				"ms-python.pylint"
			],
			"settings": {
				"pylint.severity": {
					"convention": "Information",
					"error": "Error",
					"fatal": "Error",
					"refactor": "Information",
					"warning": "Warning",
					"info": "Information"
				},
				"python.analysis.extraPaths": [
					"/home/<USER>/.ansible/collections"
				],
				"files.associations": {
					"*.yml": "ansible"
				},
				"ansible.python.interpreterPath": "/usr/bin/python3.12"
			}
		}
	},
	"initializeCommand": "mkdir -p ${HOME}/.aws",
	"runArgs": [
		"--userns=keep-id:uid=1000,gid=1000",
		"--security-opt=label=disable",
		"--device=/dev/fuse",
		"--device=/dev/net/tun",
		"-e AWS*",
		"-e GALAXY*",
		"-e NEXUS*",
		"--name=vscode-openshift",
		"--network=slirp4netns"
	   ],
	"containerUser": "ansible",
	"updateRemoteUserUID": false,
	"mounts": [
		"source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind",
		"source=${localEnv:HOME}/.aws,target=/home/<USER>/.aws,type=bind"
	]
}
