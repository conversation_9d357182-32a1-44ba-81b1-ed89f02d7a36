# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-07-26 espy                                                   #
#               2023-07-27 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-08-06 espy                                                   #
#               2023-08-07 espy                                                   #
#               2023-08-22 espy                                                   #
#               2023-08-28 espy                                                   #
#               2023-09-15 espy                                                   #
#               2023-10-12 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-10-26 espy                                                   #
#               2023-11-02 Sam<PERSON>, Landon                                           #
#               2023-11-05 espy                                                   #
#               2023-11-06 espy                                                   #
#               2023-11-06 Sam<PERSON>, Landon                                           #
#               2023-11-08 espy                                                   #
#               2023-11-28 espy                                                   #
#               2023-11-30 espy                                                   #
#               2024-01-30 espy                                                   #
#               2024-01-31 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-02-12 espy                                                   #
#               2024-02-22 <PERSON>                                       #
#               2024-05-22 espy                                                   #
#               2024-05-29 espy                                                   #
#               2024-08-21 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-07-26                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: install_os_on_bastion | Reset TPM2 Module
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Systems/{{ ocp.bastion.bmc.system_id }}/bios/settings/"
    method: "PATCH"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    body:
      Attributes:
        Tpm2Operation: Clear
    body_format: json
    validate_certs: false
    return_content: true
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'SystemResetRequired' not in result.content"
  changed_when: "'SystemResetRequired' in result.content"
  when: ocp.tpm_encryption

- name: install_os_on_bastion | Turn off bastion
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Systems/{{ ocp.bastion.bmc.system_id }}/Actions/ComputerSystem.Reset"
    method: "POST"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    body:
      Action: Reset
      ResetType: ForceOff
    body_format: json
    validate_certs: false
    return_content: true
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'Power is off' not in result.content and 'Success' not in result.content"
  changed_when: "'Success' in result.content"

- name: install_os_on_bastion | Wait for bastion to turn off
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Systems/{{ ocp.bastion.bmc.system_id }}/"
    method: GET
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    validate_certs: false
    return_content: true
    follow_redirects: all
  register: redfish_result
  until: redfish_result.json.PowerState == "Off"
  retries: 60
  delay: 1
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"

- name: install_os_on_bastion | Enable cd boot on next restart
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Systems/{{ ocp.bastion.bmc.system_id }}/"
    method: "PATCH"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    body:
      Boot:
        BootSourceOverrideTarget: Cd
        BootSourceOverrideEnabled: Once
    body_format: json
    validate_certs: false
    return_content: true
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'Success' not in result.content"
  changed_when: "'Success' in result.content"
  retries: 3 # Not sure why we have to do this but needed to do this for ILO6 for starwan.
  delay: 5
  until: result.status == 200

- name: install_os_on_bastion | Eject any previous media from virtual drive ILO5 or ILO6
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Managers/{{ ocp.bastion.bmc.manager_id }}/VirtualMedia/{{ ocp.bastion.bmc.virtual_media_id }}/Actions/VirtualMedia.EjectMedia"
    method: "POST"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    validate_certs: false
    return_content: true
    status_code:
      - 200
      - 400
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'Success' not in result.content and 'NoVirtualMediaConnectionAvailable' not in result.content"
  changed_when: "'Success' in result.content"
  when: ilo_version == "5" or ilo_version == "6"

- name: install_os_on_bastion | Check if there's any virtual media inserted on ILO4
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Managers/{{ ocp.bastion.bmc.manager_id }}/VirtualMedia/{{ ocp.bastion.bmc.virtual_media_id }}/"
    method: "GET"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    validate_certs: false
    return_content: true
    status_code:
      - 200
    follow_redirects: all
  register: ilo4_virtual_media
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  when: ilo_version == "4"

- name: install_os_on_bastion | Eject any previous media from virtual drive ILO4
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Managers/{{ ocp.bastion.bmc.manager_id }}/VirtualMedia/{{ ocp.bastion.bmc.virtual_media_id }}/{{ virtual_media_eject }}"
    method: "POST"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    body:
      Action: "EjectVirtualMedia"
    body_format: json
    validate_certs: false
    return_content: true
    status_code:
      - 200
      - 400
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'Success' not in result.content and 'NoVirtualMediaConnectionAvailable' not in result.content"
  changed_when: "'Success' in result.content"
  vars:
    virtual_media_eject: Actions/Oem/Hp/HpiLOVirtualMedia.EjectVirtualMedia/
  when: ilo_version == "4" and ilo4_virtual_media.json.Inserted

- name: install_os_on_bastion | Load media into virtual drive
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Managers/{{ ocp.bastion.bmc.manager_id }}/VirtualMedia/{{ ocp.bastion.bmc.virtual_media_id }}/"
    method: "PATCH"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    body:
      Image: "http://{{ ocp_base.galaxy_builder_mgmt_ip }}:{{ ocp_base.galaxy_builder_port }}/{{ ocp.cluster_specific_rhel_iso }}"
    body_format: json
    validate_certs: false
    return_content: true
    status_code:
      - 200
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'Success' not in result.content"
  changed_when: true

- name: install_os_on_bastion | Restart bastion
  ansible.builtin.uri:
    url: "{{ redfish_api }}/Systems/{{ ocp.bastion.bmc.system_id }}/Actions/ComputerSystem.Reset"
    method: "POST"
    user: "{{ ocp.bastion.bmc.username }}"
    password: "{{ ocp.bastion.bmc.password }}"
    force_basic_auth: true
    body:
      Action: Reset
      ResetType: 'On'
    body_format: json
    validate_certs: false
    return_content: true
    follow_redirects: all
  register: result
  environment:
    no_proxy: "{{ ocp.bastion.bmc.ipv4_address }}"
  failed_when: "'Success' not in result.content"
  changed_when: "'Success' in result.content"

- name: install_os_on_bastion | Waiting for SSH to open when installation starts
  ansible.builtin.wait_for:
    host: "{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}"
    port: 22
    delay: 120
    state: started
    timeout: 3600
    sleep: 10
    msg: |
      Timeout waiting for SSH port (22) to become available. This could be caused by the bastion network configuration
      not being correct or the ISO is unreachable by the BMC.

- name: install_os_on_bastion | Pause for 10 seconds so that root becomes available
  ansible.builtin.pause:
    seconds: 10

- name: install_os_on_bastion | Validate if Bastion booted to install live-ISO
  ansible.builtin.command:
    cmd: |
      ssh
      -o StrictHostKeyChecking=no
      -o ConnectTimeout=5
      -o UserKnownHostsFile=/dev/null
      -o BatchMode=yes
      root@{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}
      "cat /proc/cmdline"
  register: kernel_cmdline
  changed_when: false
  ignore_errors: true

- name: install_os_on_bastion | Fail when unable to SSH into install environment
  ansible.builtin.fail:
    msg: |
      Failed to login to install environment.
      Machine booted and port 22 opened on bastion.
      Machine most likely booted from disk instead of "live install" OS.
      This can be caused due to the BMC being unable to reach the "live install" ISO from the cluster builder.
  vars:
    kernel_boot_string: inst.ks=hd:LABEL=RHEL-{{ ocp.rhel_major_version }}-{{ ocp.rhel_minor_version }}-{{ ocp.rhel_patch_version }}-BaseOS-x86_64:/ks.cfg
  when: kernel_boot_string not in kernel_cmdline.stdout

- name: install_os_on_bastion | Waiting for SSH to Close after installation is complete
  ansible.builtin.wait_for:
    host: "{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}"
    port: 22
    delay: 60
    state: stopped
    timeout: 3600
    sleep: 10

- name: install_os_on_bastion | Waiting for SSH to open after reboot
  ansible.builtin.wait_for:
    host: "{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}"
    port: 22
    delay: 60
    state: started
    timeout: 3600
    sleep: 10
