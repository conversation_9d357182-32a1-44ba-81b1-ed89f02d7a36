# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: aws | Variable check
  ansible.builtin.include_tasks: ../../ocp_common/tasks/variable_check.yml

- name: aws | Create Bastion
  lmco.openshift.ec2_create:
    ami_id: "{{ ocp.bastion.aws.ami_id }}"
    subnet_id: "{{ ocp.aws.baremetal_subnet_id }}"
    ipv4_address: "{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}"
    name: "{{ ocp.openshift_cluster }}-bastion"
    username: "{{ ocp.ocp_user }}"
    password: "{{ ocp.bastion.credentials.ocp_user_password }}"
    public_ssh_key: "{{ ocp.bastion.credentials.ocp_user_authorized_public_key }}"
    force_create: "{{ ocp.force_redeployment }}"
  when: redploy_os
  vars:
    redploy_os: "{{ ocp.force_redeployment }}"

- name: aws | Waiting for SSH to open on boot
  ansible.builtin.wait_for:
    host: "{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}"
    port: 22
    delay: 0
    state: started
    timeout: 600
    sleep: 10
