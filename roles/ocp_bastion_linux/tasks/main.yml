# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-19 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-11-05 espy                                                   #
#               2023-11-07 espy                                                   #
#               2023-11-08 espy                                                   #
#               2023-12-07 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-05-16 espy                                                   #
#               2024-05-22 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-19                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Variable check
  ansible.builtin.include_tasks: ../../ocp_common/tasks/variable_check.yml

- name: Prompt user to confirm blowing away any existing deployment
  ansible.builtin.pause:
    prompt: >-
      The `force_redeployment` variable has been set to true.
      This will blow away existing bastion as well as any running cluster.
      Are you sure you want to continue?
  when: ocp.force_redeployment

- name: Check if bastion is running with correct OS
  ansible.builtin.command:
    cmd: |
      {{ ssh_command }}
      -o StrictHostKeyChecking=no
      -o ConnectTimeout=5
      -o UserKnownHostsFile=/dev/null
      -o BatchMode=yes
        {{ ocp.ocp_user }}@{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }}
      "cat /etc/os-release | grep ^CPE_NAME="
  register: bastion_config
  vars:
    ssh_password_command: sshpass -p {{ ocp.bastion_ssh_password }}
    ssh_key_command: ssh -i {{ ocp.bastion_host_key_file }}
    ssh_command: "{{ ssh_key_command if ocp.bastion_host_key_file else ssh_password_command }}"
    os_text: "cpe:/o:redhat:enterprise_linux:{{ ocp.rhel_major_version }}::baseos"
  changed_when: os_text not in bastion_config.stdout
  failed_when: false

- name: Output of Bastion check
  ansible.builtin.debug:
    var: bastion_config.stdout

- name: Install os if needed
  when: redploy_os
  vars:
    redploy_os: "{{ ocp.force_redeployment or bastion_config.changed }}"
  block:
    - name: Create custom rhel iso
      ansible.builtin.include_tasks:
        file: create_custom_rhel_iso.yml
      vars:
        rhel_users:
          - name: "{{ ocp.ocp_user }}"
            password: "{{ ocp.bastion.credentials.ocp_user_password }}"
            wheel: true
            authorized_public_key: "{{ ocp.bastion.credentials.ocp_user_authorized_public_key }}"
      when: ocp.bootstrap_os

    - name: Install os on bastion
      ansible.builtin.include_tasks:
        file: install_os_on_bastion.yml
      when: ocp.bootstrap_os
