# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-07-26 espy                                                   #
#               2023-07-27 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-08-06 espy                                                   #
#               2023-08-22 espy                                                   #
#               2023-08-23 espy                                                   #
#               2023-08-28 espy                                                   #
#               2023-09-20 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-10-26 espy                                                   #
#               2023-11-01 Sam<PERSON>, Landon                                           #
#               2023-11-05 espy                                                   #
#               2023-11-06 espy                                                   #
#               2023-11-08 espy                                                   #
#               2023-11-28 espy                                                   #
#               2023-12-11 espy                                                   #
#               2023-12-12 Sam<PERSON>, Landon                                           #
#               2024-05-22 espy                                                   #
#               2024-08-21 espy                                                   #
#               2024-11-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-07-26                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_custom_rhel_iso | Delete previous modified iso folder
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    state: absent

- name: create_custom_rhel_iso | Create new modified iso folder
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    state: directory
    mode: "0755"

- name: create_custom_rhel_iso | Unarchive iso files to modified iso directory
  ansible.builtin.command:
    cmd: bsdtar -xf {{ ocp_rhel_iso_location }} -C {{ modified_iso_location }}/
  changed_when: true

- name: create_custom_rhel_iso | Add write access for owner of all files in modified iso
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    mode: u+w
    recurse: true

- name: create_custom_rhel_iso | Create kickstart file
  ansible.builtin.template:
    src: templates/ks.cfg.j2
    dest: "{{ modified_iso_location }}/ks.cfg"
    mode: '0644'
    lstrip_blocks: true

- name: create_custom_rhel_iso | Modify Grub file
  ansible.builtin.blockinfile:
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR GRUB MODS"
    block: |
      menuentry 'Install Red Hat Enterprise Linux {{ version }} with kickstart file' --class fedora --class gnu-linux --class gnu --class os {
          linuxefi /images/pxeboot/vmlinuz inst.stage2=hd:LABEL={{ label }} quiet inst.ks=hd:LABEL={{ label }}:/ks.cfg inst.sshd {{ fips }}
          initrdefi /images/pxeboot/initrd.img
      }
    path: "{{ modified_iso_location }}/EFI/BOOT/grub.cfg"
    insertafter: "# BEGIN /etc/grub.d/10_linux ###"
    mode: '0644'
  vars:
    label: RHEL-{{ ocp.rhel_major_version }}-{{ ocp.rhel_minor_version }}-{{ ocp.rhel_patch_version }}-BaseOS-x86_64
    version: "{{ ocp.rhel_major_version }}.{{ ocp.rhel_minor_version }}"
    fips: "{{ 'fips=1' if ocp.fips_enabled == true else '' }}"

- name: create_custom_rhel_iso | Modify Timeout
  ansible.builtin.lineinfile:
    path: "{{ modified_iso_location }}/EFI/BOOT/grub.cfg"
    regexp: '^set timeout=.*$'
    line: "set timeout=5"
    backrefs: true

- name: create_custom_rhel_iso | Modify Default
  ansible.builtin.lineinfile:
    path: "{{ modified_iso_location }}/EFI/BOOT/grub.cfg"
    regexp: '^set default=.*$'
    line: 'set default="0"'
    backrefs: true

- name: create_custom_rhel_iso | Make iso
  ansible.builtin.shell:
    cmd: |
      mkisofs \
        -o {{ output_iso }} \
        -b isolinux/isolinux.bin \
        -J -R -l \
        -c isolinux/boot.cat -no-emul-boot \
        -boot-load-size 4 \
        -boot-info-table \
        -eltorito-alt-boot \
        -e images/efiboot.img -no-emul-boot \
        -graft-points \
        -joliet-long -V "RHEL-{{ ocp.rhel_major_version }}-{{ ocp.rhel_minor_version }}-{{ ocp.rhel_patch_version }}-BaseOS-x86_64" \
        {{ modified_iso_location }}/
      isohybrid --uefi {{ output_iso }}
      implantisomd5 {{ output_iso }}
      chmod 644 {{ output_iso }}
  vars:
    output_iso: "{{ ocp.ocp_install_files }}/{{ ocp.cluster_specific_rhel_iso }}"
  changed_when: true
