# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-11-05 espy                                                   #
#               2023-11-07 espy                                                   #
#               2023-11-08 espy                                                   #
#               2024-02-08 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-11-05                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

role_required_vars:
  - ocp.bastion_ssh_password or ocp.bastion_host_key_file
  - ocp.bastion.bmc.ipv4_address
  - ocp.bastion.bmc.manager_id
  - ocp.bastion.bmc.password
  - ocp.bastion.bmc.system_id
  - ocp.bastion.bmc.username
  - ocp.bastion.bmc.virtual_media_id
  - ocp.bastion.credentials.ocp_user_authorized_public_key
  - ocp.bastion.credentials.ocp_user_password
  - ocp.bastion.credentials.root_password
  - ocp.galaxy_builder_lmi_ip
  - ocp.galaxy_builder_port
  - ocp.cluster_specific_rhel_iso
  - ocp.dns_servers
  - ocp.ocp_install_files
  - ocp.ocp_rhel_iso_name
  - ocp.ocp_user
  - ocp.openshift_cluster
  - ocp.openshift_public_ip
modified_iso_location: "/tmp/modified_iso-{{ inventory_hostname }}"
ocp_rhel_iso_location: "{{ ocp.ocp_install_files }}/{{ ocp.ocp_rhel_iso_name }}"
http_dir: /var/www/html
redfish_api: "https://{{ ocp.bastion.bmc.ipv4_address }}/redfish/v1"
ilo_version_url: "{{ redfish_api }}/Managers/{{ ocp.bastion.bmc.manager_id }}/"
ilo_data: >-
  {{
    lookup('url', ilo_version_url,
      username=ocp.bastion.bmc.username,
      password=ocp.bastion.bmc.password,
      force_basic_auth=True,
      validate_certs=False,
      use_proxy=False)
  }}
ilo_version: "{{ (ilo_data.FirmwareVersion | split(' '))[1] }}"
