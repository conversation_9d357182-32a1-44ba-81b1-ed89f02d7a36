{# j2lint: disable=single-statement-per-line #}
text
{% if ocp.developer_run %}
url --url {{ ocp.ocp_install_files_url }}/BaseOS/
repo --name="AppStream" --baseurl={{ ocp.ocp_install_files_url }}/AppStream/
{% else %}
repo --name="AppStream" --baseurl=file:///run/install/sources/mount-0000-cdrom/AppStream
cdrom
{% endif %}
%pre --logfile /tmp/pre-install.log
# Hopefully fixes an issue where there's a race condition of deleting a mirror before it acts upon that mirror.
mdadm --stop --scan
mdadm --zero-superblock --force /dev/{{ ocp.bastion.storage_drives | join(' /dev/') }}
wipefs -fa /dev/{{ ocp.bastion.storage_drives | join(' /dev/') }}
%end

%packages
@core
python39
{% for package in ocp.basic_packages %}
{{ package }}
{% endfor %}
{% if ocp.tpm_encryption %}
clevis
clevis-dracut
clevis-luks
{% endif %}
%end

{{ lookup('lmco.openshift.nmstate2kickstart', ocp.bastion.network) }}

keyboard --xlayouts="us"
lang en_US.UTF-8
timezone UTC --isUtc
skipx

clearpart --initlabel --drives={{ ocp.bastion.storage_drives | join(',') }} --all
part /boot/efi --fstype="efi" --size=600 --fsoptions="umask=0077,shortname=winnt" --ondrive={{ ocp.bastion.storage_drives | first }}
part /boot --fstype="xfs" --size=1024 --ondrive={{ ocp.bastion.storage_drives | first }}
{% if ocp.bastion.storage_drives | length == 1 %}
part pv.main --size 1 --grow --fstype=xfs --ondrive={{ ocp.bastion.storage_drives | first }} {% if ocp.tpm_encryption %}--encrypted --luks-version=luks2 --passphrase="temppass" {% endif %}
{% else %}
{%     for drive in ocp.bastion.storage_drives %}
part raid.{{ drive }} --fstype="mdmember" --ondisk={{ drive }} --size=1 --grow
{%     endfor %}
raid pv.main --device=pv00 --fstype="lvmpv" --level=RAID{% if ocp.bastion.storage_drives | length > 2 %}5{% else %}1{% endif %} {% if ocp.tpm_encryption %}--encrypted --luks-version=luks2 --passphrase="temppass" {% endif %}raid.{{ ocp.bastion.storage_drives | join(' raid.') }}
{% endif %}

volgroup rhel --pesize=4096 pv.main
logvol /              --fstype="xfs"  --vgname=rhel --name=root --size={{ ocp.bastion.disk_part.root }}
logvol /home          --fstype="xfs"  --vgname=rhel --name=home --size={{ ocp.bastion.disk_part.home }}
logvol /opt           --fstype="xfs"  --vgname=rhel --name=opt --size={{ ocp.bastion.disk_part.opt }}
logvol /tmp           --fstype="xfs"  --vgname=rhel --name=tmp --size={{ ocp.bastion.disk_part.tmp }}
logvol /var           --fstype="xfs"  --vgname=rhel --name=var --size={{ ocp.bastion.disk_part.var }}
logvol /var/log       --fstype="xfs"  --vgname=rhel --name=var_log --size={{ ocp.bastion.disk_part.var_log }}
logvol /var/log/audit --fstype="xfs"  --vgname=rhel --name=var_log_audit --size={{ ocp.bastion.disk_part.var_log_audit }}
logvol /var/tmp       --fstype="xfs"  --vgname=rhel --name=var_tmp --size={{ ocp.bastion.disk_part.var_tmp }}
logvol swap           --fstype="swap" --vgname=rhel --name=swap --size={{ ocp.bastion.disk_part.swap }}

rootpw --iscrypted {{ ocp.bastion.credentials.root_password | password_hash('sha512') }}
{% for user in rhel_users %}
user {% if user.wheel %}--groups=wheel {% endif %}--name={{ user.name }} --iscrypted --password={{ user.password | password_hash('sha512') }}
sshpw --username={{ user.name }} {{ user.password | password_hash('sha512') }} --iscrypted
{% endfor %}

reboot --eject
# halt

%addon com_redhat_oscap
content-type = scap-security-guide
profile = xccdf_org.ssgproject.content_profile_stig
%end

%addon com_redhat_kdump --disable --reserve-mb="auto"
%end

%post --nochroot --log=/mnt/sysimage/root/kickstart_post_nochroot.log
echo "Make kickstart_logs dir"
/usr/bin/mkdir /mnt/sysimage/root/kickstart_logs
echo "Copying Over Kickstart logs"
/usr/bin/cp -r /tmp/* /mnt/sysimage/root/kickstart_logs
%end

%post --log=/root/kickstart_logs/kickstart_post.log
{% if ocp.tpm_encryption %}
LUKSDEV=$( blkid | awk -F: '/crypto_LUKS/{print $1}' )
clevis luks bind -y -d ${LUKSDEV} tpm2 '{"pcr_bank":"sha256","pcr_ids":"7"}' -k - <<< "temppass"
cryptsetup luksRemoveKey ${LUKSDEV} <<< "temppass"
{% endif %}
echo "Regenerating dracut"
dracut -fv --regenerate-all
{% for user in rhel_users %}
echo "Adding user {{ user.name }}"
mkdir /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chown {{ user.name }} /home/<USER>/.ssh
{%     if user.authorized_public_key %}
echo "{{ user.authorized_public_key }}" >> /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown {{ user.name }} /home/<USER>/.ssh/authorized_keys
{%     endif %}
{%     if user.wheel %}
echo "{{ user.name }} ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers.d/{{ user.name }}
chmod 440 /etc/sudoers.d/{{ user.name }}
{%     endif %}
chage -M 60 -m 0 -W 7 -d $(date +%Y-%m-%d) {{ user.name }}
echo "allow perm=any all : dir=/home/<USER>" >> /etc/fapolicyd/rules.d/50-ansible.rules
echo "-a never,exclude -F dir=/home/<USER>" >>/etc/audit/rules.d/audit.rules
chown root:fapolicyd /etc/fapolicyd/rules.d/50-ansible.rules
chmod 644 /etc/fapolicyd/rules.d/50-ansible.rules
{% endfor %}
{% if ocp.openshift_install_path %}
echo "-a never,exclude -F dir={{ ocp.openshift_install_path }} -k exclude_dir" >>/etc/audit/rules.d/audit.rules
{% endif %}
%end
