# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-07-03 espy                                                   #
#               2024-06-19 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-07-03                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

baremetal_network_prefix: "{{ ocp.baremetal_network_cidr | lmco.openshift.cidr_prefix }}"
bmc_proxy_container_name: "{{ ocp.hostname }}-galaxy-proxy"
bmc_proxy_image_name: "{{ ocp.registry_hostname }}/lmc.space.galaxy/galaxy-bmc-proxy:latest"
coredns_config_dir: /etc/coredns
coredns_corefile: "{{ coredns_directory }}/Corefile"
coredns_directory: "{{ ocp.openshift_install_path }}/coredns"
dns_container_name: "{{ ocp.hostname }}-coredns"
dns_forwarders: "{{  ocp.dns_servers | join(' ') }}"
dns_image_name: ext.hub.docker.com/coredns/coredns:1.11.3
forward_filename: "{{ ocp.hostname }}.zone"
forward_filepath: "{{ coredns_config_dir }}/{{ forward_filename }}"
galaxy_domain: galaxy.lmco.com
galaxy_filepath: "{{ coredns_config_dir }}/{{ galaxy_domain }}.zone"
galaxy_nexus_api: "{{ galaxy_nexus_url }}/service/rest/v1"
galaxy_nexus_url: https://{{ ocp.galaxy_nexus_hostname }}
galaxy_proxy_certs_directory: "{{ galaxy_proxy_directory }}/certs"
galaxy_proxy_directory: "{{ ocp.openshift_install_path }}/galaxy_proxy"
haproxy_certs_directory: "{{ haproxy_directory }}/certs"
haproxy_config_file: "{{ haproxy_directory }}/haproxy.cfg"
haproxy_directory: "{{ ocp.openshift_install_path }}/haproxy"
haproxy_tls_pem_file: /usr/local/etc/haproxy/tls.pem
keepalived_test_timeout: 30
lb_container_name: "{{ ocp.hostname }}-haproxy"
lb_image_name: ext.hub.docker.com/library/haproxy:3.0.3
net_api_vip_reverse: "{{ ocp.baremetal_network_openshift_api_vip.split('.') | reverse | join('.') }}"
net_bootstrap_reverse: "{{ ocp.baremetal_network_bootstrap_ip.split('.') | reverse | join('.') }}"
net_prefix_reverse: "{{ baremetal_network_prefix.split('.') | reverse | join('.') }}"
net_reverse_prefix: "{{ baremetal_network_prefix.split('.') | reverse | join('.') }}"
nexus_container_filename: nexus.tar
nexus_container_name: "{{ ocp.hostname }}-nexus"
nexus_data_dir: "{{ ocp.openshift_install_path }}/nexus-data"
nexus_image_name: harbor.global.lmco.com/ext.hub.docker.com/sonatype/nexus3:latest
ntp_dig_command: python3 -c "import socket; print(socket.gethostbyname('{{ ocp.ntp_servers | first }}'))"
ntp_server_ip: "{{ lookup('pipe', ntp_dig_command) }}"
openshift_public_network_prefix_length: "{{ ocp.bastion_public_network_interface.ipv4.address[0]['prefix-length'] }}"
proxy_zones: "{{ ocp.baremetal_list | map(attribute='bmc.ipv4_address') | map('regex_replace', '$', '.' + ocp.galaxy_proxy) | list }}"
reverse_filename: "{{ net_reverse_prefix }}.in-addr.arpa.zone"
reverse_filepath: "{{ coredns_config_dir }}/{{ reverse_filename }}"
sso_dig_command: python3 -c "import socket; print(socket.gethostbyname('{{ ocp.sso_issuer.split('/') | last }}'))"
sso_issuer_ip: "{{ lookup('pipe', sso_dig_command) }}"
role_required_vars:
  - ocp.baremetal_list
  - ocp.baremetal_network_bastion_ip
  - ocp.baremetal_network_bootstrap_ip
  - ocp.baremetal_network_cidr
  - ocp.baremetal_network_dns_ip
  - ocp.baremetal_network_openshift_api_vip
  - ocp.baremetal_network_openshift_ingress_vip
  - ocp.galaxy_builder_lmi_ip
  - ocp.galaxy_builder_port
  - ocp.galaxy_nexus_password
  - ocp.galaxy_nexus_repo
  - ocp.galaxy_proxy
  - ocp.galaxy_proxy_port
  - ocp.master_nodes_starting_ip
  - ocp.nexus_storage
  - ocp.ntp_servers
  - ocp.ocp_user
  - ocp.openshift_install_path
  - ocp.hostname
  - ocp.rhel_version
  - ocp.worker_nodes_starting_ip
