[galaxy-rhel-{{ ocp.rhel_major_version }}-AppStream]
name=AppStream
baseurl={{ ocp.galaxy_nexus_repo }}/RHEL{{ ocp.rhel_major_version }}/AppStream/
enabled = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
gpgcheck = 1
[galaxy-rhel-{{ ocp.rhel_major_version }}-BaseOS]
name=BaseOS
baseurl={{ ocp.galaxy_nexus_repo }}/RHEL{{ ocp.rhel_major_version }}/BaseOS/
enabled = 1
gpgkey = file:///etc/pki/rpm-gpg/RPM-GPG-KEY-redhat-release
gpgcheck = 1