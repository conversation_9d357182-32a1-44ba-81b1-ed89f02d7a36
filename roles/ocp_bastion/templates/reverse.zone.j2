#jinja2: trim_blocks:False
{# j2lint: disable=jinja-statements-delimiter #}
$TTL 1W
@   IN  SOA ns1.{{ ocp.hostname }}.  root (
            2022011131  ; serial
            3H          ; refresh (3 hours)
            30M         ; retry (30 minutes)
            2W          ; expiry (2 weeks)
            1W )        ; minimum (1 week)
    IN  NS  ns1.{{ ocp.hostname }}.
;
; Services
{{ net_api_vip_reverse }}.in-addr.arpa.    IN  PTR     api.{{ ocp.hostname }}.
;
; Bootstrap
{{ net_bootstrap_reverse }}.in-addr.arpa.    IN  PTR     bootstrap.{{ ocp.hostname }}.
;
; Control Plane
{%- set master_index = [0] %}
{%- for node in ocp.baremetal_list %}
{%-     if node.abstract.purpose == 'master' %}
{{ master_index[-1] + ocp.master_nodes_starting_ip }}.{{ net_prefix_reverse }}.in-addr.arpa.   IN  PTR     {{ node.abstract.name }}.{{ ocp.hostname }}.
{%-         set _ = master_index.append(master_index[-1] + 1) %}
{%-     endif %}
{%- endfor %}
;
; Compute / Data Plane
{%- set worker_index = [0] %}
{%- for node in ocp.baremetal_list %}
{%-     if node.abstract.purpose == 'worker' %}
{{ worker_index[-1] + ocp.worker_nodes_starting_ip }}.{{ net_prefix_reverse }}.in-addr.arpa.   IN  PTR     {{ node.abstract.name }}.{{ ocp.hostname }}.
{%-         set _ = worker_index.append(worker_index[-1] + 1) %}
{%-     endif %}
{%- endfor %}
;
;EOF
