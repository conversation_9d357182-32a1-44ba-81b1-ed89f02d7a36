<direct>
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="0">-m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT</rule>
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="1">-d {{ ocp.galaxy_builder_lmi_ip }} -j ACCEPT</rule>
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="1">-d {{ ocp.galaxy_builder_mgmt_ip }} -j ACCEPT</rule>
{% for node in ocp.baremetal_list %}
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="2">-d {{ node.bmc.ipv4_address }} -j ACCEPT</rule>
{% endfor %}
{% if ocp.sso_issuer %}
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="3">-d {{ sso_issuer_ip }} -j ACCEPT</rule>
{% endif %}
{% if ocp.ntp_servers %}
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="3">-d {{ ntp_server_ip }} -j ACCEPT</rule>
{% endif %}
{% for dns_server in ocp.dns_servers %}
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="4">-d {{ dns_server }} -j ACCEPT</rule>
{% endfor %}
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="5">-o {{ ocp.bastion.network.external_connection_name }} -j LOG --log-prefix 'ESPY-IPV4-OUTPUT-chain-drop '</rule>
  <rule ipv="ipv4" table="filter" chain="OUTPUT" priority="6">-o {{ ocp.bastion.network.external_connection_name }} -j REJECT --reject-with icmp-admin-prohibited</rule>
  <rule ipv="ipv6" table="filter" chain="OUTPUT" priority="0">-o {{ ocp.bastion.network.external_connection_name }} -j LOG --log-prefix 'ESPY-IPV6-OUTPUT-chain-drop '</rule>
  <rule ipv="ipv6" table="filter" chain="OUTPUT" priority="1">-o {{ ocp.bastion.network.external_connection_name }} -j REJECT --reject-with icmp6-adm-prohibited</rule>
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="0">-m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT</rule>
{% for node in ocp.baremetal_list %}
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="1">-d {{ node.bmc.ipv4_address }} -j ACCEPT</rule>
{% endfor %}
{% if ocp.sso_issuer %}
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="2">-d {{ sso_issuer_ip }} -j ACCEPT</rule>
{% endif %}
{% if ocp.ntp_servers %}
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="2">-d {{ ntp_server_ip }} -j ACCEPT</rule>
{% endif %}
{% for dns_server in ocp.dns_servers %}
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="3">-d {{ dns_server }} -j ACCEPT</rule>
{% endfor %}
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="4">-o {{ ocp.bastion.network.external_connection_name }} -j LOG --log-prefix 'ESPY-IPV4-FORWARD-chain-drop '</rule>
  <rule ipv="ipv4" table="filter" chain="FORWARD" priority="5">-o {{ ocp.bastion.network.external_connection_name }} -j REJECT --reject-with icmp-admin-prohibited</rule>
  <rule ipv="ipv6" table="filter" chain="FORWARD" priority="0">-o {{ ocp.bastion.network.external_connection_name }} -j LOG --log-prefix 'ESPY-IPV6-FORWARD-chain-drop '</rule>
  <rule ipv="ipv6" table="filter" chain="FORWARD" priority="1">-o {{ ocp.bastion.network.external_connection_name }} -j REJECT --reject-with icmp6-adm-prohibited</rule>
</direct>