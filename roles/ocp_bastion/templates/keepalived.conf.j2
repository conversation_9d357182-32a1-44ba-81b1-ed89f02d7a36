vrrp_instance openshift_baremetal-{{ ocp.hostname }} {
    state {{ ocp.bastion.keepalived.role }}
    interface {{ baremetal_interface_name.stdout }}
    virtual_router_id {{ ocp.bastion.keepalived.baremetal_vrid }}
    advert_int {{ ocp.bastion.keepalived.advertising_interval }}
    priority {{ 100 if ocp.bastion.keepalived.role == 'BACKUP' else 200 }}
    authentication {
        auth_type PASS
        auth_pass {{ ocp.bastion.keepalived.password }}
    }
    virtual_ipaddress {
        {{ ocp.baremetal_network_dns_ip }}/{{ ocp.baremetal_network_prefix_length }}
    }
}
vrrp_instance openshift_public-{{ ocp.hostname }} {
    state {{ ocp.bastion.keepalived.role }}
    interface {{ public_interface_name.stdout }}
    virtual_router_id {{ ocp.bastion.keepalived.public_vrid }}
    advert_int {{ ocp.bastion.keepalived.advertising_interval }}
    priority {{ 100 if ocp.bastion.keepalived.role == 'BACKUP' else 200 }}
    authentication {
        auth_type PASS
        auth_pass {{ ocp.bastion.keepalived.password }}
    }
    virtual_ipaddress {
        {{ ocp.openshift_public_ip }}/{{ openshift_public_network_prefix_length }}
    }
}