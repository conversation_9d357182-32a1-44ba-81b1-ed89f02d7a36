#############################################################################################
# Global Section                                                                            #
#############################################################################################
global
  daemon
  maxconn                   10000

#############################################################################################
# Defaults Section                                                                          #
#############################################################################################
defaults
  mode                      tcp
  option                    http-server-close
  option                    redispatch
  retries                   3
  timeout http-request      10s
  timeout queue             1m
  timeout connect           10s
  timeout client            10m
  timeout server            10m
  timeout http-keep-alive   10s
  timeout check             10s
  maxconn                   10000
  balance                   source

#############################################################################################
# Statistics Section                                                                        #
#############################################################################################
frontend stats
  bind      *:{{ ocp.haproxy_stats_port }}
  mode      http
  maxconn   10
  stats     enable
  stats     hide-version
  stats     refresh 30s
  stats     show-node
  stats     show-desc OpenShift Cluster
  stats     auth admin:password
  stats     uri /stats

frontend health_check
  bind *:1025
  mode http
  http-request return status 200 content-type text/plain string "OK"

{% if ocp.bmc_api == 'redfish-virtualmedia' %}
frontend bootstrap_http_proxy-frontend
  bind              *:{{ ocp.virtual_media_bastion_http_listen_port }}
  default_backend   bootstrap_http_proxy-backend

backend bootstrap_http_proxy-backend
{%     for forward_port in ocp.virtual_media_http_dest_ports %}
  server    bootstrap-{{ forward_port }} {{ ocp.baremetal_network_bootstrap_ip }}:{{ forward_port }} check backup
{%         set master_index = [0] %}
{%         for node in ocp.baremetal_list %}
{%             if node.abstract.purpose == 'master' %}
  server {{ node.abstract.name }}-{{ forward_port }} {{ node.abstract.name }}.{{ ocp.hostname }}:{{ forward_port }} check init-addr {{ baremetal_network_prefix }}.{{ master_index[-1] + ocp.master_nodes_starting_ip }}
{%                 set _ = master_index.append(master_index[-1] + 1) %}
{%             endif %}
{%         endfor %}
{%         set worker_index = [0] %}
{%         for node in ocp.baremetal_list %}
{%             if node.abstract.purpose == 'worker' %}
  server {{ node.abstract.name }}-{{ forward_port }} {{ node.abstract.name }}.{{ ocp.hostname }}:{{ forward_port }} check init-addr {{ baremetal_network_prefix }}.{{ worker_index[-1] + ocp.worker_nodes_starting_ip }}
{%                 set _ = worker_index.append(worker_index[-1] + 1) %}
{%             endif %}
{%         endfor %}
{%     endfor %}

frontend bootstrap_https_proxy-frontend
  bind              *:{{ ocp.virtual_media_bastion_https_listen_port }}
  mode              tcp
  default_backend   bootstrap_https_proxy-backend

backend bootstrap_https_proxy-backend
{%     for forward_port in ocp.virtual_media_https_dest_ports %}
  server    bootstrap-{{ forward_port }} {{ ocp.baremetal_network_bootstrap_ip }}:{{ forward_port }} check backup
{%         set master_index = [0] %}
{%         for node in ocp.baremetal_list %}
{%             if node.abstract.purpose == 'master' %}
  server {{ node.abstract.name }}-{{ forward_port }} {{ node.abstract.name }}.{{ ocp.hostname }}:{{ forward_port }} check init-addr {{ baremetal_network_prefix }}.{{ master_index[-1] + ocp.master_nodes_starting_ip }}
{%                 set _ = master_index.append(master_index[-1] + 1) %}
{%             endif %}
{%         endfor %}
{%         set worker_index = [0] %}
{%         for node in ocp.baremetal_list %}
{%             if node.abstract.purpose == 'worker' %}
  server {{ node.abstract.name }}-{{ forward_port }} {{ node.abstract.name }}.{{ ocp.hostname }}:{{ forward_port }} check init-addr {{ baremetal_network_prefix }}.{{ worker_index[-1] + ocp.worker_nodes_starting_ip }}
{%                 set _ = worker_index.append(worker_index[-1] + 1) %}
{%             endif %}
{%         endfor %}
{%     endfor %}
{% endif %}

frontend https-frontend
  bind              *:{{ ocp.https_non_root_port }}
  tcp-request inspect-delay 5s
  tcp-request content accept if { req_ssl_hello_type 1 }
  acl is_registry req.ssl_sni -i {{ ocp.registry_hostname }}
  acl is_nexus req.ssl_sni -i {{ ocp.galaxy_nexus_hostname }}
  use_backend forward_{{ ocp.registry_hostname }} if is_registry
  use_backend forward_{{ ocp.galaxy_nexus_hostname }} if is_nexus
{% if ocp.cluster_lb_enabled %}
  default_backend   https-backend
{% endif %}

frontend {{ ocp.registry_hostname }}_frontend
  bind             *:{{ ocp.registry_forward_port }} ssl crt {{ haproxy_tls_pem_file }}
  mode http
  default_backend   {{ ocp.registry_hostname }}_backend

frontend {{ ocp.galaxy_nexus_hostname }}_frontend
  bind             *:{{ ocp.nexus_forward_port }} ssl crt {{ haproxy_tls_pem_file }}
  mode http
  default_backend   {{ ocp.galaxy_nexus_hostname }}_backend

backend forward_{{ ocp.registry_hostname }}
  server forward_registry 127.0.0.1:{{ ocp.registry_forward_port }}

backend forward_{{ ocp.galaxy_nexus_hostname }}
  server forward_nexus 127.0.0.1:{{ ocp.nexus_forward_port }}

backend {{ ocp.galaxy_nexus_hostname }}_backend
  mode http
  server nexus 127.0.0.1:{{ ocp.nexus_http_port }}

backend {{ ocp.registry_hostname }}_backend
  mode http
  server registry 127.0.0.1:{{ ocp.registry_port }}

{% if ocp.cluster_lb_enabled %}
frontend http-frontend
  bind              *:{{ ocp.http_non_root_port }}
  default_backend   http-backend

backend https-backend
  server    ingress any.apps.{{ ocp.hostname }}:443 init-addr {{ ocp.baremetal_network_openshift_ingress_vip }}

backend http-backend
  server    ingress any.apps.{{ ocp.hostname }}:80 init-addr {{ ocp.baremetal_network_openshift_ingress_vip }}

frontend kubernetes-api-frontend
  bind              *:{{ ocp.kube_api_port }}
  default_backend   kubernetes-api-backend

backend kubernetes-api-backend
  server    api api.{{ ocp.hostname }}:6443 init-addr {{ ocp.baremetal_network_openshift_api_vip }}
{% endif %}

{% if ocp.nodeport_range and ocp.expose_nodeports and ocp.cluster_lb_enabled %}
frontend node-ports-frontend
  bind              *:{{ ocp.nodeport_range }}
  default_backend   default
  timeout client    1d

backend default
{%     set master_index = [0] %}
{%     for node in ocp.baremetal_list %}
{%         if node.abstract.purpose == 'master' %}
  server {{ node.abstract.name }} {{ node.abstract.name }}.{{ ocp.hostname }} check init-addr {{ baremetal_network_prefix }}.{{ master_index[-1] + ocp.master_nodes_starting_ip }}
{%             set _ = master_index.append(master_index[-1] + 1) %}
{%         endif %}
{%     endfor %}
{%     set worker_index = [0] %}
{%     for node in ocp.baremetal_list %}
{%         if node.abstract.purpose == 'worker' %}
  server {{ node.abstract.name }} {{ node.abstract.name }}.{{ ocp.hostname }} check init-addr {{ baremetal_network_prefix }}.{{ worker_index[-1] + ocp.worker_nodes_starting_ip }}
{%             set _ = worker_index.append(worker_index[-1] + 1) %}
{%         endif %}
{%     endfor %}
  timeout server    1d
{% endif %}
