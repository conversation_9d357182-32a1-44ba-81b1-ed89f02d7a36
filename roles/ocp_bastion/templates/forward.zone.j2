#jinja2: trim_blocks:False
{# j2lint: disable=jinja-statements-delimiter #}
$ORIGIN {{ ocp.hostname }}.
$TTL 1W
@   IN      SOA     ns1.{{ ocp.hostname }}. root (
                        2022011131          ; serial
                        3H                  ; refresh (3 hours)
                        30M                 ; retry (30 minutes)
                        2W                  ; expiry (2 weeks)
                        1W )            ; minimum (1 week)
    IN      NS      ns1.{{ ocp.hostname }}.
    IN      MX 10   smtp.{{ ocp.hostname }}.
;
; Bastion Services
ns1.{{ ocp.hostname }}.          IN      A   {{ ocp.baremetal_network_dns_ip }}
ntp.{{ ocp.hostname }}.          IN      A   {{ ocp.baremetal_network_ntp_ip }}
;
; OpenShift API amd Ingress
api.{{ ocp.hostname }}.          IN      A   {{ ocp.baremetal_network_openshift_api_vip }}
*.apps.{{ ocp.hostname }}.       IN      A   {{ ocp.baremetal_network_openshift_ingress_vip }}
;
; Bootstrap
bootstrap.{{ ocp.hostname }}.    IN      A   {{ ocp.baremetal_network_bootstrap_ip }}
; Control Plane
{%- set master_index = [0] %}
{%- for node in ocp.baremetal_list %}
{%-     if node.abstract.purpose == 'master' %}
{{ node.abstract.name }}.{{ ocp.hostname }}.    IN      A   {{ baremetal_network_prefix }}.{{ master_index[-1] + ocp.master_nodes_starting_ip }}
{%-         set _ = master_index.append(master_index[-1] + 1) %}
{%-     endif %}
{%- endfor %}
;
; Compute / Data Plane - Expand as needed
{%- set worker_index = [0] %}
{%- for node in ocp.baremetal_list %}
{%-     if node.abstract.purpose == 'worker' %}
{{ node.abstract.name }}.{{ ocp.hostname }}.    IN      A   {{ baremetal_network_prefix }}.{{ worker_index[-1] + ocp.worker_nodes_starting_ip }}
{%-         set _ = worker_index.append(worker_index[-1] + 1) %}
{%-     endif %}
{%- endfor %}
;
;EOF
