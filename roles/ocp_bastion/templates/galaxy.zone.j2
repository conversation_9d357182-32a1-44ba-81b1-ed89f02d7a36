$ORIGIN {{ galaxy_domain }}.
$TTL 1W
@   IN      SOA     ns1.{{ galaxy_domain }}. root (
                        2022011131          ; serial
                        3H                  ; refresh (3 hours)
                        30M                 ; retry (30 minutes)
                        2W                  ; expiry (2 weeks)
                        1W )            ; minimum (1 week)
    IN      NS      ns1.{{ galaxy_domain }}.
;
; Bastion Services
ns1.{{ galaxy_domain }}.          IN      A   {{ ocp.baremetal_network_dns_ip }}
{{ ocp.galaxy_registry_url }}.     IN      A   {{ ocp.baremetal_network_registry_ip }}
{{ ocp.galaxy_nexus_url }}.     IN      A   {{ ocp.baremetal_network_registry_ip }}
*.nexus-inimcmprdp.galaxy.lmco.com.     IN   A   ************
{% if ocp.bmc_api == 'redfish-virtualmedia' %}
{%     for proxy_zone in proxy_zones %}
{{ proxy_zone }}.     IN      A   {{ ocp.baremetal_network_registry_ip }}
{%     endfor %}
{% endif %}
;EOF