# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-02-21 espy                                                   #
# Create Date:  2025-02-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name:  settings | Change hostname
  ansible.builtin.hostname:
    name: "{{ ocp.openshift_cluster }}"
  become: true
  become_user: root

- name:  settings | Change prompt color
  ansible.builtin.blockinfile:
    path: /etc/bashrc
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR PS1 UPDATE"
    block: |
      if [ -n "$PS1" ]; then
        export PS1="\[\033[{{ ocp.bastion.options.shell_color }}m\]\u@\h\[\033[00m\]:\w\$ "
      fi
  become: true
  become_user: root

- name:  settings | Set authentication profile to minimal
  ansible.builtin.command:
    cmd: authselect select minimal --force
  changed_when: true
  become: true
  become_user: root

- name:  settings | Setup firewall to be simulated disconnected
  ansible.builtin.include_tasks:
    file: simulated_disconnected.yml
  when: ocp.simulated_disconnected_env

- name:  settings | Setup LMCO Proxy
  ansible.builtin.include_role:
    name: lmco.linux.linux_proxy
    public: true
  vars:
    galaxy_linux:
      http_proxy: "{{ ocp.http_proxy }}"
      https_proxy: "{{ ocp.https_proxy }}"
      no_proxy: >-
        {{
          ocp.standard_no_proxy_addresses +
          [ocp.baremetal_network_cidr] +
          [ocp.provisioning_network_cidr] +
          [ocp.galaxy_builder_lmi_ip] +
          [ocp.galaxy_builder_mgmt_ip] +
          ocp.additional_no_proxies
        }}
  when: ocp.setup_proxy

- name:  settings | Install LMCO Certificate Authority
  ansible.builtin.include_role:
    name: lmco.linux.linux_cert_auth
    public: true
  vars:
    galaxy_linux:
      ca_cert: "{{ ocp.ca_cert }}"
  when: ocp.install_lmco_ca

- name:  settings | Setup Redhat Subscription Manager
  ansible.builtin.include_role:
    name: lmco.linux.linux_rhsm
    public: true
  vars:
    galaxy_linux:
      org_id: "{{ ocp.rhsm_org_id }}"
      activationkey: "{{ ocp.rhsm_key }}"
      server_hostname: "{{ ocp.rhsm_host }}"
  when: ocp.setup_rhsm

- name:  settings | Configure Gateway
  ansible.builtin.include_role:
    name: lmco.linux.linux_gateway
    public: true
  vars:
    galaxy_linux:
      gateway_zone: "public"
  when: ocp.bastion_is_gateway

- name:  settings | Setup openshift user
  ansible.builtin.include_role:
    name: lmco.linux.linux_user
    public: true
  vars:
    galaxy_linux:
      user:
        username: "{{ ocp.ocp_user }}"
        authorized_key: "{{ ocp.bastion.credentials.ocp_user_authorized_public_key }}"
        home_dir: "{{ ocp.ocp_user_home if ocp.ocp_user_home is defined else none }}"
        sudoer: true
        groups:
          - wheel
        private_key: "{{ lookup('file', '~/.ssh/id_rsa') }}"
  when: ocp.setup_ocp_user

- name:  settings | Add ssh shortcuts to ssh config file for master nodes
  ansible.builtin.blockinfile:
    create: true
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR SSH CONFIG FOR {{ node.abstract.name }}"
    path: "~/.ssh/config"
    mode: "0600"
    block: |
      Host m{{ index }}
        User core
        Hostname {{ node.abstract.name }}.{{ ocp.hostname }}
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
  loop: "{{ ocp.baremetal_list | selectattr('abstract.purpose', 'equalto', 'master') | list }}"
  loop_control:
    loop_var: node
    label: "{{ node.abstract.name }}"
    index_var: index
  when: ocp.platform == 'baremetal'

- name:  settings | Add ssh shortcuts to ssh config file for worker nodes
  ansible.builtin.blockinfile:
    create: true
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR SSH CONFIG FOR {{ node.abstract.name }}"
    path: "~/.ssh/config"
    mode: "0600"
    block: |
      Host w{{ index }}
        User core
        Hostname {{ node.abstract.name }}.{{ ocp.hostname }}
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
  loop: "{{ ocp.baremetal_list | selectattr('abstract.purpose', 'equalto', 'worker') | list }}"
  loop_control:
    loop_var: node
    label: "{{ node.abstract.name }}"
    index_var: index
  when: ocp.platform == 'baremetal'

- name:  settings | Add ssh shortcuts to ssh config file
  ansible.builtin.blockinfile:
    create: true
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR SSH CONFIG FOR BOOTSTRAP NODE"
    path: "~/.ssh/config"
    mode: "0600"
    block: |
      Host bootstrap
        User core
        Hostname bootstrap.{{ ocp.hostname }}
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
  when: ocp.platform == 'baremetal'

- name:  settings | Get control machine time
  ansible.builtin.command:
    cmd: date -u "+%Y-%m-%d %H:%M:%S"
  register: host_time
  delegate_to: localhost
  changed_when: false
  when: ocp.platform == 'baremetal'

- name:  settings | Set time on target machine to UTC '{{ host_time.stdout }}'
  ansible.builtin.command:
    cmd: date -u -s '{{ host_time.stdout }}'
  become: true
  become_user: root
  changed_when: true
  when: ocp.platform == 'baremetal'

- name: settings | Allow priviliged ports
  ansible.posix.sysctl:
    name: net.ipv4.ip_unprivileged_port_start
    value: 23
    state: present
    reload: true
  become: true
  become_user: root
  when: ocp.platform == 'aws'
