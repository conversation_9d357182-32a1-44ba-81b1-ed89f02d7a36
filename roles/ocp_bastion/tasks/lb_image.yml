# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        get_image.yml                                                     #
# Version:                                                                        #
#               2024-04-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-04-18                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: lb_image | Pull image from nexus
  ansible.builtin.command:
    cmd: podman pull --tls-verify=false {{ ocp.registry_hostname }}:{{ ocp.registry_port }}/{{ lb_image_name }}
  changed_when: true

- name: lb_image | Retag the image
  containers.podman.podman_tag:
    image: "{{ ocp.registry_hostname }}:{{ ocp.registry_port }}/{{ lb_image_name }}"
    target_names:
      - "{{ ocp.registry_hostname }}/{{ lb_image_name }}"
