# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_haproxy_config.yml                                         #
# Version:                                                                        #
#               2024-02-21 espy                                                   #
#               2024-02-24 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2024-09-11 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: lb_config_files | Create necessary directories for HAProxy
  ansible.builtin.file:
    path: "{{ directories }}"
    mode: "0755"
    state: directory
  loop:
    - "{{ haproxy_directory }}"
    - "{{ haproxy_certs_directory }}"
  loop_control:
    loop_var: directories

- name: lb_config_files | Create haproxy.cfg file
  ansible.builtin.template:
    src: haproxy.cfg.j2
    dest: "{{ haproxy_config_file }}"
    mode: '0644'
    lstrip_blocks: true

- name: lb_config_files | Create nexus certs
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_common
    tasks_from: create_certs
  vars:
    cert_directory: "{{ haproxy_certs_directory }}"
    dns_name: galaxy.lmco.com
    alt_names:
      - "{{ ocp.galaxy_nexus_hostname }}"
      - "{{ ocp.registry_hostname }}"

- name: lb_config_files | Fetch created cert
  ansible.builtin.fetch:
    src: "{{ ocp.openshift_install_path }}/haproxy/certs/tls.crt"
    dest: "{{ ocp.registry_crt_file }}"
    flat: true
  run_once: true
