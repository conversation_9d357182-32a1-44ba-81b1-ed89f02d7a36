# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        setup_firewall.yml                                                #
# Version:                                                                        #
#               2024-02-20 espy                                                   #
#               2024-02-21 espy                                                   #
#               2024-02-24 espy                                                   #
#               2024-03-04 Sam<PERSON>, Landon                                           #
#               2024-06-05 espy                                                   #
#               2024-06-06 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: dns_firewall | Open firewall ports
  ansible.builtin.include_role:
    name: lmco.linux.linux_firewall_ports
    public: true
  vars:
    firewall_services:
      - dns
    firewall_rich_rules:
      - rule protocol value="icmp" accept
      - rule family="ipv4" destination address={{ ocp.baremetal_network_dns_ip }} forward-port port=53 protocol=udp to-port={{ ocp.dns_port }}
    firewall_zone: internal

- name: dns_firewall | Setup DNS Firewall forwards
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_common
    tasks_from: forward_ports
  vars:
    port_to_forward:
      port: 53
      dest_ips:
        - 127.0.0.1
        - "{{ ocp.baremetal_network_dns_ip }}"
      toport: "{{ ocp.dns_port }}"
      proto: udp
      zones: []
