# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_container.yml                                              #
# Version:                                                                        #
#               2024-02-20 espy                                                   #
#               2024-02-22 espy                                                   #
#               2024-02-24 espy                                                   #
#               2024-02-26 espy                                                   #
#               2024-03-06 espy                                                   #
#               2024-03-07 espy                                                   #
#               2024-03-11 <PERSON><PERSON><PERSON><PERSON>, <PERSON>                                    #
#               2024-05-06 espy                                                   #
#               2024-05-08 espy                                                   #
#               2024-05-23 espy                                                   #
#               2024-05-29 espy                                                   #
#               2024-06-02 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: bmc_proxy_container | Start galaxy container
  containers.podman.podman_container:
    name: "{{ bmc_proxy_container_name }}"
    image: "{{ bmc_proxy_image_name }}"
    uidmap:
      - "0:1:1001"
      - "1001:0:1"
    state: created
    network: slirp4netns
    healthcheck: python -c 'import httpx; httpx.get("https://localhost:8443/galaxy_health", verify=False)'
    healthcheck_interval: 30s
    healthcheck_retries: 5
    healthcheck_start_period: 10s
    healthcheck_timeout: 10s
    healthcheck_failure_action: "{{ 'restart' if ocp.rhel_major_version | int == 9 else omit }}"
    publish:
      - "{{ ocp.galaxy_proxy_port }}:8443"
    env:
      PROXY_DOMAIN: "{{ ocp.galaxy_proxy }}"
      PROXY_PORT: "{{ ocp.galaxy_proxy_port }}"
      BASTION_PUBLIC_IP: "{{ ocp.bastion_bmc_ip }}"
      DEST_HTTP_PORTS: "{{ ocp.virtual_media_http_dest_ports | join(',') }}"
      DEST_HTTPS_PORTS: "{{ ocp.virtual_media_https_dest_ports | join(',') }}"
      GALAXY_PROXY_HTTP_PORT: "{{ ocp.virtual_media_bastion_http_listen_port }}"
      GALAXY_PROXY_HTTPS_PORT: "{{ ocp.virtual_media_bastion_https_listen_port }}"
    volumes:
      - "{{ galaxy_proxy_certs_directory }}:/home/<USER>/app/certs:Z"
    generate_systemd:
      container_prefix: ""
      separator: ""
      new: true
      path: ~/.config/systemd/user

- name: bmc_proxy_container | Start and enabled galaxy container
  ansible.builtin.systemd:
    name: "{{ bmc_proxy_container_name }}"
    state: restarted
    enabled: true
    daemon_reload: true
    scope: user

- name: bmc_proxy_container | Ensure lingering enabled for podman user
  ansible.builtin.command:
    cmd: loginctl enable-linger
    creates: /var/lib/systemd/linger/{{ ocp.ocp_user }}
