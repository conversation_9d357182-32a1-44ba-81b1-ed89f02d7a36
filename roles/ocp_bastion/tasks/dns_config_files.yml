# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_dns_files.yml                                              #
# Version:                                                                        #
#               2024-02-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: dns_config_files | Create necessary directories for Coredns
  ansible.builtin.file:
    path: "{{ coredns_directory }}{{ coredns_config_dir }}"
    mode: "0755"
    state: directory
    recurse: true

- name: dns_config_files | Create Corefile for coredns
  ansible.builtin.template:
    src: corefile.j2
    dest: "{{ coredns_corefile }}"
    mode: "0644"

- name: dns_config_files | Update DNS Entries for forward zone file
  ansible.builtin.template:
    src: forward.zone.j2
    dest: "{{ coredns_directory }}/{{ forward_filepath }}"
    mode: "0644"

- name: dns_config_files | Update DNS Entries for reverze zone file
  ansible.builtin.template:
    src: reverse.zone.j2
    dest: "{{ coredns_directory }}/{{ reverse_filepath }}"
    mode: "0644"

- name: dns_config_files | Update DNS Entries for galaxy zone
  ansible.builtin.template:
    src: galaxy.zone.j2
    dest: "{{ coredns_directory }}/{{ galaxy_filepath }}"
    mode: "0644"
