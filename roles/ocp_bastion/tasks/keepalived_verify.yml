# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        keepalived_verify_node.yml                                        #
# Version:                                                                        #
#               2023-08-23 LDS. Initial                                           #
# Create Date:  2023-08-23                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                      #
# Description:                                                                    #
#               Tasks to verify keepalived functionality                          #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: keepalived_verify | Create /dev/shm/restartnetwork.sh file for keepaived testing
  ansible.builtin.template:
    src: restartnetwork.sh.j2
    dest: /dev/shm/restartnetwork.sh
    mode: "0655"

- name: keepalived_verify | Execute verification tests on each node
  ansible.builtin.include_tasks:
    file: keepalived_verify_node.yml
    apply:
      when: node == inventory_hostname
  loop: "{{ groups.bastion }}"
  loop_control:
    loop_var: node

- name: keepalived_verify | Remove /dev/shm/restartnetwork.sh
  ansible.builtin.file:
    path: /dev/shm/restartnetwork.sh
    state: absent
