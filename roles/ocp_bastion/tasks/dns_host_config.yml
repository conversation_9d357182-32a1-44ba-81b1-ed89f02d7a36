# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        ntp_host_config.yml                                               #
# Version:                                                                        #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: dns_host_config | Set local dns as first on dns list
  ansible.builtin.include_role:
    name: lmco.linux.linux_dns_servers
    public: true
  vars:
    galaxy_linux:
      connection_ip: "{{  ocp.bastion_public_network_interface.ipv4.address[0].ip }}"
      dns_list: "{{ ['127.0.0.1'] + ocp.dns_servers }}"
