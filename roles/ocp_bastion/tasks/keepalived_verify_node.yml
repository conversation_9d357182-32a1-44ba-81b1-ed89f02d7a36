# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        keepalived_verify_node.yml                                        #
# Version:                                                                        #
#               2023-08-23 LDS. Initial                                           #
# Create Date:  2023-08-23                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                      #
# Description:                                                                    #
#               Loop each node and verify that when the network is shutdown       #
#               the VIP is still available                                        #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: keepalived_verify_node | Shutdown the network
  ansible.builtin.command: /dev/shm/restartnetwork.sh
  async: "{{ keepalived_test_timeout + 5 }}"
  poll: 0
  register: restart_results
  changed_when: true
  become: true

- name: keepalived_verify_node | Check if server is unavailalbe
  ansible.builtin.wait_for:
    host: "{{ ansible_host }}"
    port: 22
    delay: 5
    timeout: 10
  register: results
  delegate_to: localhost
  failed_when: '"Timeout" not in results.msg'

- name: keepalived_verify_node | Check if VIP is available
  ansible.builtin.wait_for:
    host: "{{ ocp.openshift_public_ip }}"
    port: 22
    delay: 20
    timeout: 40
  delegate_to: localhost

- name: keepalived_verify_node | Wait for server to become available again
  ansible.builtin.wait_for:
    port: 22
    timeout: "{{ keepalived_test_timeout }}"
