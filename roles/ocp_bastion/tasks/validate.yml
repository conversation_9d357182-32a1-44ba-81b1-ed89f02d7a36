# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-11-14 espy                                                   #
#               2023-11-15 espy                                                   #
#               2023-11-15 Sam<PERSON>, Landon                                           #
#               2024-02-19 espy                                                   #
#               2024-02-22 <PERSON>                                       #
#               2024-06-06 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-11-14                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: validate | Waiting for HTTPd port to be available
  ansible.builtin.wait_for:
    host: "{{ ocp.galaxy_builder_lmi_ip }}"
    port: "{{ ocp.galaxy_builder_port }}"
    delay: 0
    state: started
    timeout: 5
    sleep: 1
    msg: "Timeout waiting for HTTPS port {{ ocp.galaxy_builder_port }} to become available"

- name: validate | Validate ocp install folder
  lmco.openshift.validate_server:
    path: /opt
    min_space_gb: 90

- name: validate | Validate home folder
  lmco.openshift.validate_server:
    path: "~/"
    min_space_gb: 25
