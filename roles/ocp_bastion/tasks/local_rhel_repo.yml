# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        local_rhel_repo.yml                                               #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: local_rhel_repo | Create repo directory
  ansible.builtin.file:
    state: directory
    mode: "0755"
    path: /repo
  become: true
  become_user: root

- name: local_rhel_repo | Unarchive packages
  ansible.builtin.unarchive:
    src: "{{ ocp.ocp_install_files }}/{{ ocp.bastion_rhel_packages_tarball }}"
    dest: /repo/
    mode: "0755"
    creates: /repo/AppStream
  become: true
  become_user: root

- name: local_rhel_repo | Find all files repo directory
  ansible.builtin.find:
    paths: /etc/yum.repos.d
    file_type: file
  register: files_found

- name: local_rhel_repo | Remove the found files
  ansible.builtin.file:
    path: "{{ file.path }}"
    state: absent
  loop: "{{ files_found.files }}"
  loop_control:
    loop_var: file
  become: true
  become_user: root

- name: local_rhel_repo | Copy over repos
  ansible.builtin.copy:
    src: local.repo
    dest: /etc/yum.repos.d/rhel.repo
    mode: "0644"
  become: true
  become_user: root

- name: local_rhel_repo | Install basic packages
  ansible.builtin.yum:
    name: "{{ ocp.basic_packages }}"
    state: present
  become: true
  become_user: root


- name: local_rhel_repo | Install basic packages
  ansible.builtin.yum:
    name: firewalld
    state: absent
  become: true
  become_user: root
  when: ocp.platform == 'aws'
