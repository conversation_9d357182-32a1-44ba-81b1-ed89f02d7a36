# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_config_files.yml                                           #
# Version:                                                                        #
#               2024-04-16 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2024-05-05 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-04-16                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: nexus_config_files | Run root required commands
  become: true
  become_user: root
  block:
    - name: nexus_config_files | Create openshift directoru
      ansible.builtin.file:
        path: "{{ ocp.openshift_install_path }}"
        state: directory
        mode: '0755'
        owner: "{{ ocp.ocp_user }}"
        group: "{{ ocp.ocp_user }}"

    - name: nexus_config_files | Update auditd to ignore files in {{ ocp.openshift_install_path }}
      ansible.builtin.lineinfile:
        path: /etc/audit/rules.d/audit.rules
        regexp: '^{{ auditd_line }}$'
        line: "{{ auditd_line }}"
      vars:
        auditd_line: -a never,exclude -F dir={{ ocp.openshift_install_path }} -k exclude_dir

    - name: nexus_config_files | Add nexus urls to etc hosts
      ansible.builtin.lineinfile:
        path: /etc/hosts
        regexp: '^127\.0\.0\.1'
        line: "127.0.0.1 localhost localhost.localdomain localhost4 localhost4.localdomain4 {{ ocp.registry_hostname }} {{ ocp.galaxy_nexus_hostname }}"
      when: ocp.platform == 'baremetal'

    - name: nexus_config_files | Make sure max username spaces is not 0
      ansible.builtin.lineinfile:
        path: /etc/sysctl.d/99-sysctl.conf
        search_string: user.max_user_namespaces
        line: user.max_user_namespaces=1027341

    - name: nexus_config_files | Make sure max username spaces is not 0
      ansible.posix.sysctl:
        name: user.max_user_namespaces
        value: '1027341'
        sysctl_set: true
        state: present
        reload: true

- name: nexus_config_files | Add umask 0002
  ansible.builtin.lineinfile:
    path: ~/.bashrc
    regexp: '^umask 0002'
    line: umask 0002

- name: nexus_config_files | Create docker config directoru
  ansible.builtin.file:
    path: ~/.docker
    state: directory
    mode: '0755'
    owner: "{{ ocp.ocp_user }}"
    group: "{{ ocp.ocp_user }}"

- name: nexus_config_files | Create config.json for logging in
  ansible.builtin.template:
    src: config.json.j2
    dest: ~/.docker/config.json
    mode: "0600"
