# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_config_files.yml                                           #
# Version:                                                                        #
#               2024-03-06 espy                                                   #
#               2024-03-07 espy                                                   #
#               2024-04-16 espy                                                   #
#               2024-04-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-03-06                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: bmc_proxy_config_files | Create necessary directories for galaxy proxy
  ansible.builtin.file:
    path: "{{ galaxy_proxy_certs_directory }}"
    mode: "0755"
    state: directory

- name: bmc_proxy_config_files | Create galaxy proxy certs
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_common
    tasks_from: create_certs
  vars:
    cert_directory: "{{ galaxy_proxy_certs_directory }}"
    dns_name: "{{ ocp.galaxy_proxy }}"
    alt_names: "{{ ocp.baremetal_list | map(attribute='bmc.ipv4_address') | map('regex_replace', '$', '.' + ocp.galaxy_proxy) | list }}"
