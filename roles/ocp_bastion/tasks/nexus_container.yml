# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_container.yml                                              #
# Version:                                                                        #
#               2024-04-16 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2024-04-28 espy                                                   #
#               2024-04-30 <PERSON><PERSON><PERSON><PERSON>, <PERSON>                                    #
#               2024-05-06 espy                                                   #
#               2024-05-14 <PERSON><PERSON>, <PERSON>                                           #
#               2024-05-23 espy                                                   #
#               2024-05-29 espy                                                   #
#               2024-06-02 espy                                                   #
#               2024-06-11 espy                                                   #
#               2024-11-04 <PERSON><PERSON>, Landon                                           #
#               2024-11-20 espy                                                   #
#               2024-12-11 espy                                                   #
#               2024-12-12 <PERSON><PERSON><PERSON><PERSON>, <PERSON>                                    #
#               2025-01-30 espy                                                   #
# Create Date:  2024-04-16                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: nexus_container | Stop and remove any old nexus containers
  containers.podman.podman_container:
    name: "{{ nexus_container_name }}"
    state: absent

- name: nexus_container | Remove nexus directory
  ansible.builtin.file:
    path: "{{ nexus_data_dir }}"
    state: absent

- name: nexus_container | Unarchive registry storage
  ansible.builtin.shell:
    cmd: |
      set -e
      set -o pipefail && curl -s {{ ocp.ocp_install_files_url }}/{{ ocp.nexus_storage }} | tar -x
      chown {{ ocp.ocp_user }}:{{ ocp.ocp_user }} {{ ocp.openshift_install_path }}/nexus-data
    creates: "{{ ocp.openshift_install_path }}/nexus-data"
    chdir: "{{ ocp.openshift_install_path }}"
  become: true
  become_user: root
  when: ocp.file_transfer_method == 'http'
  async: 3600
  poll: 30

- name: nexus_container | Copy over nexus image tarball
  ansible.builtin.shell:
    cmd: |
      set -o pipefail && cat {{ ocp.ocp_install_files }}/{{ ocp.nexus_storage }} | \
      ssh {{ ocp.ocp_user }}@{{ ocp.bastion_public_network_interface.ipv4.address[0].ip }} \
      "tar -xf - -C {{ ocp.openshift_install_path }}"
  delegate_to: localhost
  changed_when: true
  when: ocp.file_transfer_method == 'ssh'

- name: nexus_container | Start new nexus container
  containers.podman.podman_container:
    name: "{{ nexus_container_name }}"
    image: "{{ nexus_image_name }}"
    uidmap:
      - "0:1:200"
      - "200:0:1"
      - "201:201:10000"
    state: created
    network: slirp4netns
    publish:
      - "{{ ocp.nexus_http_port }}:8081"
      - "{{ ocp.registry_port }}:8082"
    volumes:
      - "{{ nexus_data_dir }}:/nexus-data:Z"
    env:
      _JAVA_OPTIONS: -Dcom.redhat.fips=false
    healthcheck: curl localhost:8081/service/rest/v1/status -f
    healthcheck_interval: 30s
    healthcheck_retries: 5
    healthcheck_start_period: 5m
    healthcheck_timeout: 10s
    healthcheck_failure_action: "{{ 'restart' if ocp.rhel_major_version | int == 9 else omit }}"
    generate_systemd:
      container_prefix: ""
      separator: ""
      start_timeout: 600
      new: true
      path: ~/.config/systemd/user

- name: nexus_container | Ensure lingering enabled for podman user
  ansible.builtin.command:
    cmd: loginctl enable-linger
    creates: /var/lib/systemd/linger/{{ ocp.ocp_user }}

- name: nexus_container | Start and enabled galaxy container
  ansible.builtin.systemd:
    name: "{{ nexus_container_name }}"
    state: restarted
    enabled: true
    daemon_reload: true
    scope: user

- name: nexus_container | Wait for nexus to become active
  ansible.builtin.uri:
    url: http://{{ ocp.galaxy_nexus_hostname }}:{{ ocp.nexus_http_port }}/service/rest/v1/status
    method: "GET"
    return_content: true
  register: nexus_response
  retries: 20
  delay: 30
  until: nexus_response.status == 200
