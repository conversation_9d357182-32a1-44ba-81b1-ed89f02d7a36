# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        install_image.yml                                                 #
# Version:                                                                        #
#               2024-02-24 espy                                                   #
#               2024-04-16 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-24                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: nexus_image | Copy over nexus image tarball
  ansible.builtin.copy:
    src: "{{ ocp.ocp_install_files }}/{{ nexus_container_filename }}"
    dest: "/tmp/{{ nexus_container_filename }}"
    mode: '0644'

- name: nexus_image | Load tarball into local podman image registry
  containers.podman.podman_load:
    input: "/tmp/{{ nexus_container_filename }}"
  register: image_info

- name: nexus_image | Tag loaded image
  ansible.builtin.command:
    cmd: podman tag {{ image_info.image.Id }} {{ nexus_image_name }}
  changed_when: true
