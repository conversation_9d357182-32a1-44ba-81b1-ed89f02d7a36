# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2024-04-16 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-04-16                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: nexus | Import variables file with nexus sha value
  ansible.builtin.include_vars:
    file: "{{ ocp.ocp_install_files }}/{{ ocp.verson_vars_file }}"

- name: nexus | Check if nexus is up and running with correct SHA
  ansible.builtin.uri:
    url: "http://{{ ocp.galaxy_nexus_hostname }}:{{ ocp.nexus_http_port }}/repository/{{ ocp.galaxy_nexus_repo_name }}/nexus-sha.txt"
    method: "GET"
    return_content: true
  ignore_errors: true
  register: nexus_response

- name: nexus | Block for creating nexus
  when: nexus_response.failed or nexus_response.status != 200 or nexus_response.content != nexus_sha
  block:
    - name: nexus | Create nexus config files
      ansible.builtin.include_tasks: nexus_config_files.yml

    - name: nexus | Install nexus image
      ansible.builtin.include_tasks: nexus_image.yml

    - name: nexus | Create nexus container
      ansible.builtin.include_tasks: nexus_container.yml
