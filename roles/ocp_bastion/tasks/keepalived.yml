# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        keepalived_verify_node.yml                                        #
# Version:                                                                        #
#               2023-08-23 LDS. Initial                                           #
# Create Date:  2023-08-23                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                      #
# Description:                                                                    #
#               Install task file for keepalived                                  #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: keepalived | Root commands
  become: true
  become_user: root
  block:
    - name: keepalived | Install keepalived
      ansible.builtin.yum:
        name: keepalived
        state: present
      notify: Remove_default_keepalived_conf_file

    - name: keepalived | Flush handlers
      ansible.builtin.meta: flush_handlers

    - name: keepalived | Open up firewall rules for keepalived
      ansible.posix.firewalld:
        rich_rule: rule protocol value="vrrp" accept
        permanent: true
        state: enabled
        immediate: true
        zone: "{{ zone }}"
      loop:
        - public
        - internal
      loop_control:
        loop_var: zone

    - name: keepalived | Get public connection name
      ansible.builtin.shell:
        cmd: set -o pipefail && echo -n $(ip -o -4 a | grep 'inet {{ ansible_host }}/' | awk '{print $2}')
      changed_when: false
      register: public_interface_name

    - name: keepalived | Get public connection name
      ansible.builtin.shell:
        cmd: set -o pipefail && echo -n $(ip -o -4 a | grep 'inet {{ ocp.baremetal_network_bastion_ip }}/' | awk '{print $2}')
      changed_when: false
      register: baremetal_interface_name

    - name: keepalived | Ansible replace block example
      ansible.builtin.blockinfile:
        path: /etc/keepalived/keepalived.conf
        marker: "# {mark} ANSIBLE MANAGED BLOCK FOR KEEPALIVED CONF FOR {{ ocp.hostname }}"
        block: "{{ lookup('template', 'keepalived.conf.j2') }}"
        create: true
        state: present
        mode: '0644'

    - name: keepalived | Start keepalived
      ansible.builtin.systemd:
        name: keepalived
        enabled: true
        state: restarted
