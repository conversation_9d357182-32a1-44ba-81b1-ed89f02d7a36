# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_container.yml                                              #
# Version:                                                                        #
#               2024-02-20 espy                                                   #
#               2024-02-22 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-05-23 espy                                                   #
#               2024-05-29 espy                                                   #
#               2024-06-02 espy                                                   #
#               2024-06-05 espy                                                   #
#               2024-08-12 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: dns_container | Run coredns pod
  containers.podman.podman_container:
    name: "{{ dns_container_name }}"
    image: "{{ ocp.registry_hostname }}/{{ dns_image_name }}"
    state: created
    network: slirp4netns
    publish:
      - "{{ ocp.dns_port }}:53/udp"
    volumes:
      - "{{ coredns_corefile }}:/home/<USER>/Corefile:z"
      - "{{ coredns_directory }}{{ coredns_config_dir }}:{{ coredns_config_dir }}:z"
    generate_systemd:
      container_prefix: ""
      separator: ""
      new: true
      path: ~/.config/systemd/user

- name: dns_container | Start and enabled new podman registry container
  ansible.builtin.systemd:
    name: "{{ dns_container_name }}"
    state: restarted
    enabled: true
    daemon_reload: true
    scope: user

- name: dns_container | Ensure lingering enabled for podman user
  ansible.builtin.command:
    cmd: loginctl enable-linger
    creates: /var/lib/systemd/linger/{{ ocp.ocp_user }}
