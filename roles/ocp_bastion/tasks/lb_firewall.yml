# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        update_firewall.yml                                               #
# Version:                                                                        #
#               2024-02-21 espy                                                   #
#               2024-02-23 espy                                                   #
#               2024-03-04 <PERSON><PERSON>, Landon                                           #
#               2024-04-01 espy                                                   #
#               2024-04-27 espy                                                   #
#               2024-04-30 <PERSON><PERSON><PERSON><PERSON>, <PERSON> M                                    #
#               2024-04-30 espy                                                   #
#               2024-05-06 espy                                                   #
#               2024-05-30 espy                                                   #
#               2024-06-06 espy                                                   #
#               2024-06-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: lb_firewall | Open firewall ports for NodePorts
  ansible.builtin.include_role:
    name: lmco.linux.linux_firewall_ports
    public: true
  vars:
    firewall_ports:
      - "{{ ocp.nodeport_range }}/tcp"
    firewall_zone: public
  when: ocp.expose_nodeports and ocp.nodeport_range

- name: lb_firewall | Open firewall ports on public
  ansible.builtin.include_role:
    name: lmco.linux.linux_firewall_ports
    public: true
  vars:
    firewall_services:
      - kube-apiserver
      - http
      - https
    firewall_ports:
      - "{{ ocp.virtual_media_bastion_http_listen_port }}/tcp"
      - "{{ ocp.virtual_media_bastion_https_listen_port }}/tcp"
    firewall_rich_rules:
      - rule family=ipv4 destination address={{ ocp.openshift_public_ip }} forward-port port=443 protocol=tcp to-port={{ ocp.https_non_root_port }}
      - rule family=ipv4 destination address={{ ocp.openshift_public_ip }} forward-port port=80 protocol=tcp to-port={{ ocp.http_non_root_port }}
      - rule family=ipv4 destination address={{ ocp.openshift_public_ip }} forward-port port=6443 protocol=tcp to-port={{ ocp.kube_api_port }}
    firewall_zone: public

- name: lb_firewall | Open firewall ports on internal
  ansible.builtin.include_role:
    name: lmco.linux.linux_firewall_ports
    public: true
  vars:
    firewall_services:
      - http
      - https
    firewall_rich_rules:
      - rule family=ipv4 destination address={{ ocp.baremetal_network_registry_ip }} forward-port port=443 protocol=tcp to-port={{ ocp.https_non_root_port }}
      - rule family=ipv4 destination address={{ ocp.baremetal_network_registry_ip }} forward-port port=80 protocol=tcp to-port={{ ocp.http_non_root_port }}
    firewall_zone: internal

- name: lb_firewall | Setup HAProxy Firewall forwards
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_common
    tasks_from: forward_ports
  loop_control:
    loop_var: port_to_forward
    label: "{{ port_to_forward.port }}"
  loop:
    - port: 443
      dest_ips:
        - 127.0.0.1
        - "{{ ocp.baremetal_network_registry_ip }}"
      toport: "{{ ocp.https_non_root_port }}"
      proto: tcp
      zones: []
    - port: 80
      dest_ips:
        - 127.0.0.1
        - "{{ ocp.baremetal_network_registry_ip }}"
      toport: "{{ ocp.http_non_root_port }}"
      proto: tcp
      zones: []
    - port: 6443
      dest_ips:
        - 127.0.0.1
        - "{{ ocp.baremetal_network_registry_ip }}"
      toport: "{{ ocp.kube_api_port }}"
      proto: tcp
      zones: []

- name: lb_firewall | Setup gateway policy
  become: true
  become_user: root
  when: ocp.rhel_major_version | int == 9
  vars:
    policy_name: gateway_policy
  block:
    - name: lb_firewall | Delete and create policy
      ansible.builtin.shell:
        cmd: |
          firewall-cmd --permanent --delete-policy gateway_policy
          firewall-cmd --permanent --new-policy gateway_policy
      changed_when: true

    - name: lb_firewall | Add policy rules
      ansible.builtin.shell:
        cmd: |
          set -e
          firewall-cmd --permanent --policy gateway_policy --add-ingress-zone internal
          firewall-cmd --permanent --policy gateway_policy --add-egress-zone public
          firewall-cmd --permanent --policy gateway_policy --set-target ACCEPT
      changed_when: true
