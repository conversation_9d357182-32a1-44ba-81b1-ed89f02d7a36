# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        setup_fapolicy.yml                                                #
# Version:                                                                        #
#               2024-05-22 espy                                                   #
#               2024-06-05 espy                                                   #
#               2024-06-11 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-05-22                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: setup_fapolicy | Install and configure fapolicyd
  become: true
  become_user: root
  block:
    - name: setup_fapolicy | Install fapolicy
      ansible.builtin.yum:
        name: fapolicyd
        state: present

    - name: setup_fapolicy | Create fapolicy policy file for home
      ansible.builtin.copy:
        dest: '/etc/fapolicyd/rules.d/50-ansible.rules'
        owner: root
        group: fapolicyd
        mode: "0644"
        content: |
          # Allow home directory
          allow perm=any all : dir=/home/<USER>

    - name: setup_fapolicy | Create fapolicy policy file so the OpenShift binaries work
      ansible.builtin.copy:
        dest: '/etc/fapolicyd/rules.d/50-{{ ocp.hostname }}-openshift.rules'
        owner: root
        group: fapolicyd
        mode: "0644"
        content: |
          # Allow terraform and etcd from install_path
          allow perm=execute all : dir={{ ocp.openshift_install_path }}/cluster-configs : all trust=0
          allow perm=execute all : dir=/usr/local/bin : all trust=0

    - name: setup_fapolicy | Restart policyd
      ansible.builtin.systemd:
        name: fapolicyd
        state: restarted
