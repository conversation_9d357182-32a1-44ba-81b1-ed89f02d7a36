# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_container.yml                                              #
# Version:                                                                        #
#               2024-02-20 espy                                                   #
#               2024-02-21 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2024-05-23 espy                                                   #
#               2024-05-29 espy                                                   #
#               2024-06-02 espy                                                   #
#               2024-08-07 espy                                                   #
#               2024-08-12 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: lb_container | Run haproxy container
  containers.podman.podman_container:
    name: "{{ lb_container_name }}"
    image: "{{ ocp.registry_hostname }}/{{ lb_image_name }}"
    state: created
    network: host
    volumes:
      - "{{ haproxy_config_file }}:{{ haproxy_config_filepath }}:z"
      - "{{ haproxy_certs_directory }}/tls.pem:{{ haproxy_tls_pem_file }}:z"
    generate_systemd:
      container_prefix: ""
      separator: ""
      new: true
      path: ~/.config/systemd/user
  vars:
    haproxy_config_filepath: /usr/local/etc/haproxy/haproxy.cfg

- name: lb_container | Ensure lingering enabled for podman user
  ansible.builtin.command:
    cmd: loginctl enable-linger
    creates: /var/lib/systemd/linger/{{ ocp.ocp_user }}

- name: lb_container | Start and enabled new podman registry container
  ansible.builtin.systemd:
    name: "{{ lb_container_name }}"
    state: restarted
    enabled: true
    daemon_reload: true
    scope: user
