# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        remove_temporary_settings.yml                                     #
# Version:                                                                        #
#               2024-06-06 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-06-06                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: remove_temporary_settings | Remove temporary port forwards
  ansible.builtin.command:
    cmd: |
      firewall-cmd
      --permanent
      --direct
      --remove-rule ipv4 nat OUTPUT 0 -o lo -d {{ port_to_forward.dest_ip }} -p {{ port_to_forward.proto }}
      --dport {{ port_to_forward.port }} -j REDIRECT
      --to-ports {{ port_to_forward.toport }}
  register: firewalld_output
  changed_when: already_enabled not in firewalld_output.stderr
  vars:
    already_enabled: NOT_ENABLED
  become: true
  become_user: root
  loop:
    - port: 80
      dest_ip: 127.0.0.1
      proto: tcp
      toport: "{{ ocp.http_non_root_port }}"
    - port: 443
      dest_ip: 127.0.0.1
      proto: tcp
      toport: "{{ ocp.https_non_root_port }}"
    - port: 6443
      dest_ip: 127.0.0.1
      proto: tcp
      toport: "{{ ocp.kube_api_port }}"
  loop_control:
    loop_var: port_to_forward

- name: remove_temporary_settings | Restart firewalld
  ansible.builtin.systemd:
    name: firewalld
    state: restarted
  become: true
  become_user: root

- name: remove_temporary_settings | Remove Tempoary nexus urls from etc hosts
  ansible.builtin.lineinfile:
    path: /etc/hosts
    regexp: '^127\.0\.0\.1'
    line: "127.0.0.1 localhost localhost.localdomain localhost4 localhost4.localdomain4"
  become: true
  become_user: root
