# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        local_rhel_repo.yml                                               #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: galaxy_rhel_repo | Find all files repo directory
  ansible.builtin.find:
    paths: /etc/yum.repos.d
    file_type: file
  register: files_found

- name: galaxy_rhel_repo | Remove the found files
  ansible.builtin.file:
    path: "{{ file.path }}"
    state: absent
  loop: "{{ files_found.files }}"
  loop_control:
    loop_var: file
  become: true
  become_user: root

- name: galaxy_rhel_repo | Copy over repos
  ansible.builtin.template:
    src: galaxy.repo.j2
    dest: /etc/yum.repos.d/galaxy.repo
    mode: "0644"
  become: true
  become_user: root

- name: galaxy_rhel_repo | Install basic packages
  ansible.builtin.yum:
    name: "{{ ocp.basic_packages }}"
    state: present
  become: true
  become_user: root
