# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-07-26 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-08-23 espy                                                   #
#               2023-08-28 espy                                                   #
#               2023-10-09 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-10-26 espy                                                   #
#               2024-04-28 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-07-26                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: rhel_dnf_repo | Find all files repo directory
  ansible.builtin.find:
    paths: /etc/yum.repos.d
    file_type: file
  register: files_found

- name: rhel_dnf_repo | Remove the found files
  ansible.builtin.file:
    path: "{{ file.path }}"
    state: absent
  loop: "{{ files_found.files }}"
  loop_control:
    loop_var: file
  become: true
  become_user: root

- name: rhel_dnf_repo | Create rhel repo file
  ansible.builtin.template:
    src: rhel.repo.j2
    dest: /etc/yum.repos.d/nexus.repo
    mode: '0644'
  become: true
  become_user: root
