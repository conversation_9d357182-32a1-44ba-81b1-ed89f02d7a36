# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-07-17 Sam<PERSON>, <PERSON> D                                         #
#               2023-07-24 <PERSON><PERSON>, <PERSON> D                                         #
#               2023-10-12 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-01 <PERSON><PERSON>, <PERSON>                                           #
#               2023-12-20 espy                                                   #
#               2024-01-04 espy                                                   #
#               2024-01-08 espy                                                   #
#               2024-01-09 espy                                                   #
#               2024-01-11 Sam<PERSON>, Landon                                           #
#               2024-01-16 espy                                                   #
#               2024-01-17 espy                                                   #
#               2024-01-22 <PERSON><PERSON>, <PERSON>                                           #
#               2024-01-23 espy                                                   #
#               2024-01-23 <PERSON><PERSON>, <PERSON>                                           #
#               2024-01-30 <PERSON><PERSON>, <PERSON>                                           #
#               2024-02-06 <PERSON><PERSON>, <PERSON>                                           #
#               2024-02-08 espy                                                   #
#               2024-02-14 espy                                                   #
#               2024-03-12 Sam<PERSON>, <PERSON>                                           #
#               2024-05-09 Sam<PERSON>, Landon                                           #
#               2024-05-10 <PERSON><PERSON>, <PERSON>                                           #
#               2025-01-30 espy                                                   #
# Create Date:  2023-07-17                                                        #
# Author:       Sam<PERSON>, <PERSON> D                                                    #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Add to api to etc hosts
  ansible.builtin.shell:
    cmd: |
      echo "$(sed '/{{ ocp.openshift_public_ip }}/c\{{ ocp.openshift_public_ip }} api.{{ ocp.hostname }}' /etc/hosts)" > /etc/hosts
      grep '^{{ ocp.openshift_public_ip }}' /etc/hosts || echo '{{ ocp.openshift_public_ip }} api.{{ ocp.hostname }}' >> /etc/hosts
  changed_when: true
  vars:
    openshift_public_ip: "{{ ocp.openshift_public_ip if ocp.platform == 'baremetal' else lookup('lmco.openshift.api_ip', ocp.openshift_cluster) }}"
  become: true
  become_user: root

- name: Login to OpenShift
  ansible.builtin.import_role:
    name: lmco.openshift.ocp_login

- name: Find release-signature files
  ansible.builtin.find:
    paths: "{{ ocp.openshift_install_path }}/nexus-data/oc-mirror-workspace/"
    patterns: signature-sha256-.*json
    recurse: true
    use_regex: true
  register: release_signature_files
  delegate_to: '{{ groups["bastion"][0] }}'

- name: Fetch files from the bastion server
  ansible.builtin.fetch:
    dest: "/tmp/{{ item.path | basename }}"
    flat: true
    src: "{{ item.path }}"
  loop: "{{ release_signature_files.files }}"
  loop_control:
    label: "{{ item.path }}"
  when: release_signature_files.files | length > 0
  delegate_to: '{{ groups["bastion"][0] }}'

- name: Apply release-signature resources to the cluster.
  kubernetes.core.k8s:
    src: "/tmp/{{ item.path | basename }}"
    state: present
  loop: "{{ release_signature_files.files }}"
  loop_control:
    label: "{{ item.path }}"
  when: release_signature_files.files | length > 0

- name: Wait for openshift cluster to finish install
  lmco.openshift.wait_for_cluster:
    kubeconfig_file: "{{ ocp.kubeconfig_file }}"

- name: Install NMState operator
  ansible.builtin.include_role:
    name: lmco.nmstate.nmstate_openshift
    public: true
    tasks_from: install
  when:
    - ocp.nmstate is defined
    - ocp.nmstate | length > 0

- name: Create NMState configurations
  kubernetes.core.k8s:
    state: present
    definition:
      apiVersion: nmstate.io/v1
      kind: NodeNetworkConfigurationPolicy
      metadata:
        name: "{{ nmconfig.name }}"
      spec: "{{ nmconfig.spec }}"
  loop: "{{ ocp.nmstate }}"
  loop_control:
    loop_var: nmconfig
    label: "{{ nmconfig.name | default(nmconfig) }}"
  when:
    - ocp.nmstate is defined
    - ocp.nmstate | length > 0

- name: Wait for NMState configurations to become available
  kubernetes.core.k8s_info:
    api_version: nmstate.io/v1
    kind: NodeNetworkConfigurationPolicy
    name: "{{ nmconfig.name }}"
  loop: "{{ ocp.nmstate }}"
  register: nmstate_status
  retries: 24
  delay: 5
  until:
    - nmstate_status.resources is defined
    - nmstate_status.resources | length > 0
    - nmstate_status.resources.0.status.conditions is defined
    - nmstate_status.resources.0.status.conditions | length == 3
    - ( nmstate_status.resources.0.status.conditions.0.type == "Available" and
      nmstate_status.resources.0.status.conditions.0.status == "True" ) or
      ( nmstate_status.resources.0.status.conditions.1.type == "Available" and
      nmstate_status.resources.0.status.conditions.1.status == "True" ) or
      ( nmstate_status.resources.0.status.conditions.2.type == "Available" and
      nmstate_status.resources.0.status.conditions.2.status == "True" )
  loop_control:
    loop_var: nmconfig
    label: "{{ nmconfig.name | default(nmconfig) }}"
  when:
    - ocp.nmstate is defined
    - ocp.nmstate | length > 0

- name: Pause machine config operator
  kubernetes.core.k8s:
    state: present
    definition:
      apiVersion: machineconfiguration.openshift.io/v1
      kind: MachineConfigPool
      metadata:
        name: "{{ role }}"
      spec:
        paused: true
  loop:
    - master
    - worker
  loop_control:
    loop_var: role

- name: "Create configmap"
  kubernetes.core.k8s:
    state: present
    definition:
      apiVersion: v1
      kind: ConfigMap
      metadata:
        name: "{{ ca_name }}"
        namespace: "{{ config_namespace }}"
      data:
        ca-bundle.crt: |
          {{ lookup('file', ocp.ca_cert) }}
          {{ lookup('file', ocp.registry_crt_file) }}

- name: "Apply custom CA to the cluster wide proxy"
  kubernetes.core.k8s:
    state: present
    merge_type:
      - merge
    definition:
      apiVersion: v1
      kind: Proxy
      metadata:
        name: cluster
      spec:
        trustedCA:
          name: "{{ ca_name }}"

- name: "Create wildcard cert secret"
  kubernetes.core.k8s:
    state: present
    definition:
      apiVersion: v1
      kind: Secret
      metadata:
        name: "{{ wildcard_cert_name }}"
        namespace: openshift-ingress
      data:
        tls.crt: "{{ lookup('file', ocp.cluster_cert, rstrip=false) | b64encode }}"
        tls.key: "{{ lookup('file', ocp.cluster_key, rstrip=false) | b64encode }}"

- name: "Apply wildcard cert secret to the cluster wide ingress controller"
  kubernetes.core.k8s:
    state: present
    merge_type:
      - merge
    definition:
      apiVersion: operator.openshift.io/v1
      kind: IngressController
      metadata:
        name: default
        namespace: openshift-ingress-operator
      spec:
        defaultCertificate:
          name: "{{ wildcard_cert_name }}"

- name: "Create api cert secret"
  kubernetes.core.k8s:
    state: present
    definition:
      apiVersion: v1
      kind: Secret
      metadata:
        name: "{{ wildcard_cert_name }}"
        namespace: "{{ config_namespace }}"
      data:
        tls.crt: "{{ lookup('file', ocp.cluster_cert, rstrip=false) | b64encode }}"
        tls.key: "{{ lookup('file', ocp.cluster_key, rstrip=false) | b64encode }}"

- name: "Apply API cert secret to the cluster wide api server"
  kubernetes.core.k8s:
    state: present
    merge_type:
      - merge
    definition:
      apiVersion: config.openshift.io/v1
      kind: APIServer
      metadata:
        name: cluster
      spec:
        servingCerts:
          namedCertificates:
            - names:
                - api.{{ ocp.hostname }}
              servingCertificate:
                name: "{{ wildcard_cert_name }}"

- name: Get names of all manifests files
  ansible.builtin.find:
    paths: "{{ ocp.manifest_files_folder }}/"
  register: manifest_files

- name: Apply all manifests files to cluster
  kubernetes.core.k8s:
    src: "{{ manifest_file.path }}"
    state: present
  loop: "{{ manifest_files.files }}"
  loop_control:
    loop_var: manifest_file
    label: "{{ manifest_file.path | basename }}"

- name: Create login banner secrets
  kubernetes.core.k8s:
    api_version: v1
    definition:
      data:
        login.html: "{{ lookup('template', 'login.html.j2') | b64encode }}"
    kind: Secret
    name: login-template
    namespace: openshift-config
    state: present
  when: ( ocp.banner_login_text is defined and ocp.banner_login_text | length > 0 ) or
        ( ocp.banner_top_text is defined and ocp.banner_top_text | length > 0 ) or
        ( ocp.banner_bottom_text is defined and ocp.banner_bottom_text | length > 0 )

- name: Create providers banner secrets
  kubernetes.core.k8s:
    api_version: v1
    definition:
      data:
        providers.html: "{{ lookup('template', 'providers.html.j2') | b64encode }}"
    kind: Secret
    name: providers-template
    namespace: openshift-config
    state: present
  when: ( ocp.banner_login_text is defined and ocp.banner_login_text | length > 0 ) or
        ( ocp.banner_top_text is defined and ocp.banner_top_text | length > 0 ) or
        ( ocp.banner_bottom_text is defined and ocp.banner_bottom_text | length > 0 )

- name: Create errors banner secrets
  kubernetes.core.k8s:
    api_version: v1
    definition:
      data:
        errors.html: "{{ lookup('template', 'errors.html.j2') | b64encode }}"
    kind: Secret
    name: errors-template
    namespace: openshift-config
    state: present
  when: ( ocp.banner_login_text is defined and ocp.banner_login_text | length > 0 ) or
        ( ocp.banner_top_text is defined and ocp.banner_top_text | length > 0 ) or
        ( ocp.banner_bottom_text is defined and ocp.banner_bottom_text | length > 0 )

- name: Gather current information aobut OAuth
  kubernetes.core.k8s_info:
    api_version: v1
    kind: OAuth
    name: cluster
  register: oauth_config

- name: Remove banner configuration from OAuth
  kubernetes.core.k8s_json_patch:
    api_version: v1
    kind: OAuth
    name: cluster
    patch:
      - op: remove
        path: /spec/templates
  when:
    - oauth_config.resources | length == 1 and oauth_config.resources.0.spec.templates is defined
    - ocp.banner_login_text is not defined or ocp.banner_login_text | length < 1
    - ocp.banner_top_text is not defined or ocp.banner_top_text | length < 1
    - ocp.banner_bottom_text is not defined or ocp.banner_bottom_text | length < 1

- name: Add banner configuration to OAuth
  kubernetes.core.k8s:
    api_version: operator.openshift.io/v1
    definition:
      apiVersion: config.openshift.io/v1
      kind: OAuth
      metadata:
        name: cluster
      spec:
        templates:
          error:
            name: errors-template
          login:
            name: login-template
          providerSelection:
            name: providers-template
    merge_type:
      - merge
    state: present
  when: ( ocp.banner_login_text is defined and ocp.banner_login_text | length > 0 ) or
        ( ocp.banner_top_text is defined and ocp.banner_top_text | length > 0 ) or
        ( ocp.banner_bottom_text is defined and ocp.banner_bottom_text | length > 0 )

- name: Remove Identity Proviers
  kubernetes.core.k8s_json_patch:
    api_version: v1
    kind: OAuth
    name: cluster
    patch:
      - op: remove
        path: /spec/identityProviders
  when:
    - oauth_config.resources | length == 1 and oauth_config.resources.0.spec.identityProviders is defined
    - ( ocp.sso_client_id is not defined or ocp.sso_client_id | length < 1 ) or
      ( ocp.sso_issuer is not defined or ocp.sso_issuer | length < 1 ) or
      ( ocp.sso_name is not defined or ocp.sso_name | length < 1 ) or
      ( ocp.sso_secret is not defined or ocp.sso_secret | length < 1 ) or
      ( ocp.sso_type is not defined or ocp.sso_type | length < 1 )

- name: Add OpenID Secret
  kubernetes.core.k8s:
    api_version: v1
    definition:
      data:
        clientSecret: "{{ ocp.sso_secret | b64encode }}"
    kind: Secret
    name: "{{ ocp.sso_name | lower }}"
    namespace: openshift-config
    state: present
  when:
    - ocp.sso_secret is defined and ocp.sso_secret | length > 0
    - ocp.sso_name is defined and ocp.sso_name | length > 0

- name: Add SSO configuration to OAuth
  kubernetes.core.k8s:
    api_version: operator.openshift.io/v1
    definition:
      apiVersion: config.openshift.io/v1
      kind: OAuth
      metadata:
        name: cluster
      spec:
        identityProviders:
          - mappingMethod: claim
            name: "{{ ocp.sso_name }}"
            openID:
              claims:
                email:
                  - email
                name:
                  - name
                preferredUsername:
                  - preferred_username
              clientID: "{{ ocp.sso_client_id }}"
              clientSecret:
                name: "{{ ocp.sso_name | lower }}"
              extraScopes:
                - email
                - profile
                - openid
                - mfa_required
              issuer: "{{ ocp.sso_issuer }}"
            type: OpenID
    merge_type:
      - merge
    state: present
  when:
    - ocp.sso_type is defined and ocp.sso_type == 'OpenID'
    - ocp.sso_client_id is defined and ocp.sso_client_id | length > 0
    - ocp.sso_issuer is defined and ocp.sso_issuer | length > 0
    - ocp.sso_name is defined and ocp.sso_name | length > 0

- name: Add cluster-admins group
  kubernetes.core.k8s:
    api_version: operator.openshift.io/v1
    definition:
      kind: Group
      apiVersion: user.openshift.io/v1
      metadata:
        name: cluster-admins
      users: "{{ ocp.cluster_admins }}"
    state: present
  when:
    - ocp.cluster_admins is defined
    - ocp.cluster_admins | length > 0

- name: Add cluster-admins-group ClusterRoleBinding
  kubernetes.core.k8s:
    api_version: operator.openshift.io/v1
    definition:
      kind: ClusterRoleBinding
      apiVersion: rbac.authorization.k8s.io/v1
      metadata:
        name: cluster-admin-group
      subjects:
        - kind: Group
          apiGroup: rbac.authorization.k8s.io
          name: cluster-admins
      roleRef:
        apiGroup: rbac.authorization.k8s.io
        kind: ClusterRole
        name: cluster-admin
    state: present
  when:
    - ocp.cluster_admins is defined
    - ocp.cluster_admins | length > 0

- name: Remove header
  kubernetes.core.k8s:
    api_version: console.openshift.io/v1
    kind: ConsoleNotification
    name: custom-header
    state: absent
  when: ocp.banner_top_text is not defined or ocp.banner_top_text | length < 1

- name: Create header
  kubernetes.core.k8s:
    api_version: console.openshift.io/v1
    definition:
      spec:
        backgroundColor: "{{ ocp.banner_top_color | default('#06C') }}"
        location: BannerTop
        text: "{{ ocp.banner_top_text }}"
    kind: ConsoleNotification
    name: custom-header
    state: present
  when:
    - ocp.banner_top_text is defined
    - ocp.banner_top_text | length > 0

- name: Remove footer
  kubernetes.core.k8s:
    api_version: console.openshift.io/v1
    kind: ConsoleNotification
    name: custom-footer
    state: absent
  when: ocp.banner_bottom_text is not defined or ocp.banner_bottom_text | length < 1

- name: Create footer
  kubernetes.core.k8s:
    api_version: console.openshift.io/v1
    definition:
      spec:
        backgroundColor: "{{ ocp.banner_bottom_color | default('#06C') }}"
        location: BannerBottom
        text: "{{ ocp.banner_bottom_text }}"
    kind: ConsoleNotification
    name: custom-footer
    state: present
  when:
    - ocp.banner_bottom_text is defined
    - ocp.banner_bottom_text | length > 0

- name: Adjust pull secret for external sources
  kubernetes.core.k8s:
    state: present
    definition:
      kind: Secret
      apiVersion: v1
      metadata:
        name: pull-secret
        namespace: openshift-config
      data:
        .dockerconfigjson: "{{ ocp.redhat_registry_pull_secret | to_nice_json | b64encode }}"
      type: kubernetes.io/dockerconfigjson
  when: ocp.redhat_registry_pull_secret and ocp.dev_connected

- name: UnPause machine config operator
  kubernetes.core.k8s:
    state: present
    definition:
      apiVersion: machineconfiguration.openshift.io/v1
      kind: MachineConfigPool
      metadata:
        name: "{{ role }}"
      spec:
        paused: false
  loop:
    - master
    - worker
  loop_control:
    loop_var: role

- name: Wait for cluster to finish all machine config affecting configurations
  lmco.openshift.wait_for_cluster:
    kubeconfig_file: "{{ ocp.kubeconfig_file }}"
    initial_delay: 20

- name: Remove old certificate authority from kubeconfig
  ansible.builtin.lineinfile:
    path: "{{ ocp.kubeconfig_file }}"
    regexp: "^    certificate-authority-data:"
    state: absent

- name: Add certificate authority to kubeconfig
  ansible.builtin.lineinfile:
    path: "{{ ocp.kubeconfig_file }}"
    insertbefore: '^    server:'
    line: "    certificate-authority-data: {{ lookup('file', ocp.ca_cert) | b64encode }}"

- name: Verify new certificates
  lmco.openshift.wait_for_cluster:
    kubeconfig_file: "{{ ocp.kubeconfig_file }}"
