{% if ocp.registry.1.unqualified_search is defined and ocp.registry.1.unqualified_search | length > 0 %}
unqualified-search-registries = ["{{ ocp.registry.1.unqualified_search }}"]
{% endif %}
[[registry]]
blocked = {{ ocp.registry.1.blocked | default('false') }}
insecure = {{ ocp.registry.1.insecure | default('false') }}
location = "{{ ocp.registry.1.location | default('') }}"
prefix = "{{ ocp.registry.1.prefix | default('') }}"
{% if ocp.registry.1.mirrors is defined and ocp.registry.1.mirrors | length > 0 %}
{%     for mirror in ocp.registry.1.mirrors %}
  [[registry.mirror]]
  location = "{{ mirror.location | default('') }}"
  insecure = {{ mirror.insecure | default('false') }}
{%     endfor %}
{% endif %}
