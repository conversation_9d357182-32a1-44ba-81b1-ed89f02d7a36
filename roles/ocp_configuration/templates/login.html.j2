{# j2lint: disable=jinja-variable-lower-case #}
{% raw %}
<!DOCTYPE html>
<html lang="en-us" data-test-id="login">
  <head>
    <title>

        {{ .Locale.LogIn }}

       · Red Hat OpenShift
    </title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="data:image/x-icon;base64,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">

    <style>
      @font-face { font-family: 'RedHatText'; src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff"); font-weight: 400; font-style: normal; }
@font-face { font-family: 'RedHatText'; src: url(data:application/x-font-woff;charset=utf-8;base64,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) format("woff"); font-weight: 700; font-style: normal; }
.pf-c-form-control { --pf-global--Color--100: var(--pf-global--Color--dark-100); --pf-global--Color--200: var(--pf-global--Color--dark-200); --pf-global--BorderColor--100: var(--pf-global--BorderColor--dark-100); --pf-global--primary-color--100: var(--pf-global--primary-color--dark-100); --pf-global--link--Color: var(--pf-global--link--Color--dark); --pf-global--link--Color--hover: var(--pf-global--link--Color--dark--hover); --pf-global--BackgroundColor--100: var(--pf-global--BackgroundColor--light-100); }

.pf-c-login__header, .pf-c-login__footer { --pf-global--Color--100: var(--pf-global--Color--light-100); --pf-global--Color--200: var(--pf-global--Color--light-200); --pf-global--BorderColor--100: var(--pf-global--BorderColor--light-100); --pf-global--primary-color--100: var(--pf-global--primary-color--light-100); --pf-global--link--Color: var(--pf-global--link--Color--light); --pf-global--link--Color--hover: var(--pf-global--link--Color--light); --pf-global--BackgroundColor--100: var(--pf-global--BackgroundColor--dark-100); }
.pf-c-login__header .pf-c-card, .pf-c-login__footer .pf-c-card { --pf-c-card--BackgroundColor: var(--pf-global--BackgroundColor--dark-transparent-200); }
.pf-c-login__header .pf-c-button, .pf-c-login__footer .pf-c-button { --pf-c-button--m-primary--Color: var(--pf-global--primary-color--dark-100); --pf-c-button--m-primary--hover--Color: var(--pf-global--primary-color--dark-100); --pf-c-button--m-primary--focus--Color: var(--pf-global--primary-color--dark-100); --pf-c-button--m-primary--active--Color: var(--pf-global--primary-color--dark-100); --pf-c-button--m-primary--BackgroundColor: var(--pf-global--BackgroundColor--light-100); --pf-c-button--m-primary--hover--BackgroundColor: var(--pf-global--BackgroundColor--light-300); --pf-c-button--m-primary--focus--BackgroundColor: var(--pf-global--BackgroundColor--light-300); --pf-c-button--m-primary--active--BackgroundColor: var(--pf-global--BackgroundColor--light-300); --pf-c-button--m-secondary--Color: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--hover--Color: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--focus--Color: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--active--Color: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--BorderColor: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--hover--BorderColor: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--focus--BorderColor: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--active--BorderColor: var(--pf-global--Color--light-100); }

:root { --pf-global--palette--black-100: #fafafa; --pf-global--palette--black-150: #f5f5f5; --pf-global--palette--black-200: #ededed; --pf-global--palette--black-300: #d2d2d2; --pf-global--palette--black-400: #b8bbbe; --pf-global--palette--black-500: #8a8d90; --pf-global--palette--black-600: #737679; --pf-global--palette--black-700: #4f5255; --pf-global--palette--black-800: #3c3f42; --pf-global--palette--black-850: #212427; --pf-global--palette--black-900: #151515; --pf-global--palette--black-1000: #030303; --pf-global--palette--blue-50: #def3ff; --pf-global--palette--blue-100: #bee1f4; --pf-global--palette--blue-200: #73bcf7; --pf-global--palette--blue-300: #2b9af3; --pf-global--palette--blue-400: #06c; --pf-global--palette--blue-500: #004080; --pf-global--palette--blue-600: #004368; --pf-global--palette--blue-700: #002235; --pf-global--palette--cyan-100: #a2d9d9; --pf-global--palette--cyan-200: #73c5c5; --pf-global--palette--cyan-300: #009596; --pf-global--palette--cyan-400: #005f60; --pf-global--palette--cyan-500: #003737; --pf-global--palette--cyan-600: #003d44; --pf-global--palette--cyan-700: #001f22; --pf-global--palette--gold-100: #f9e0a2; --pf-global--palette--gold-200: #f6d173; --pf-global--palette--gold-300: #f4c145; --pf-global--palette--gold-400: #f0ab00; --pf-global--palette--gold-500: #c58c00; --pf-global--palette--gold-600: #795600; --pf-global--palette--gold-700: #3d2c00; --pf-global--palette--green-100: #bde5b8; --pf-global--palette--green-200: #95d58e; --pf-global--palette--green-300: #6ec664; --pf-global--palette--green-400: #5ba352; --pf-global--palette--green-500: #467f40; --pf-global--palette--green-600: #1e4f18; --pf-global--palette--green-700: #0f280d; --pf-global--palette--light-blue-100: #beedf9; --pf-global--palette--light-blue-200: #7cdbf3; --pf-global--palette--light-blue-300: #35caed; --pf-global--palette--light-blue-400: #00b9e4; --pf-global--palette--light-blue-500: #008bad; --pf-global--palette--light-blue-600: #005c73; --pf-global--palette--light-blue-700: #002d39; --pf-global--palette--light-green-100: #e4f5bc; --pf-global--palette--light-green-200: #c8eb79; --pf-global--palette--light-green-300: #ace12e; --pf-global--palette--light-green-400: #92d400; --pf-global--palette--light-green-500: #6ca100; --pf-global--palette--light-green-600: #486b00; --pf-global--palette--light-green-700: #253600; --pf-global--palette--orange-100: #f4b678; --pf-global--palette--orange-200: #ef9234; --pf-global--palette--orange-300: #ec7a08; --pf-global--palette--orange-400: #c46100; --pf-global--palette--orange-500: #8f4700; --pf-global--palette--orange-600: #773d00; --pf-global--palette--orange-700: #3b1f00; --pf-global--palette--purple-100: #cbc1ff; --pf-global--palette--purple-200: #b2a3ff; --pf-global--palette--purple-300: #a18fff; --pf-global--palette--purple-400: #8476d1; --pf-global--palette--purple-500: #6753ac; --pf-global--palette--purple-600: #40199a; --pf-global--palette--purple-700: #1f0066; --pf-global--palette--red-100: #c9190b; --pf-global--palette--red-200: #a30000; --pf-global--palette--red-300: #7d1007; --pf-global--palette--red-400: #470000; --pf-global--palette--red-500: #2c0000; --pf-global--palette--white: #fff; --pf-global--BackgroundColor--100: #fff; --pf-global--BackgroundColor--150: #f5f5f5; --pf-global--BackgroundColor--200: #fafafa; --pf-global--BackgroundColor--300: #ededed; --pf-global--BackgroundColor--light-100: #fff; --pf-global--BackgroundColor--light-200: #fafafa; --pf-global--BackgroundColor--light-300: #ededed; --pf-global--BackgroundColor--dark-100: #151515; --pf-global--BackgroundColor--dark-200: #3c3f42; --pf-global--BackgroundColor--dark-300: #212427; --pf-global--BackgroundColor--dark-400: #4f5255; --pf-global--BackgroundColor--dark-transparent-100: rgba(3, 3, 3, 0.62); --pf-global--BackgroundColor--dark-transparent-200: rgba(3, 3, 3, 0.32); --pf-global--Color--100: #151515; --pf-global--Color--200: #737679; --pf-global--Color--300: #3c3f42; --pf-global--Color--400: #8a8d90; --pf-global--Color--light-100: #fff; --pf-global--Color--light-200: #ededed; --pf-global--Color--light-300: #d2d2d2; --pf-global--Color--dark-100: #151515; --pf-global--Color--dark-200: #737679; --pf-global--active-color--100: #06c; --pf-global--active-color--200: #bee1f4; --pf-global--active-color--300: #73bcf7; --pf-global--active-color--400: #2b9af3; --pf-global--disabled-color--100: #737679; --pf-global--disabled-color--200: #d2d2d2; --pf-global--disabled-color--300: #ededed; --pf-global--primary-color--100: #06c; --pf-global--primary-color--200: #004080; --pf-global--primary-color--light-100: #73bcf7; --pf-global--primary-color--dark-100: #06c; --pf-global--secondary-color--100: #737679; --pf-global--default-color--100: #73c5c5; --pf-global--default-color--200: #009596; --pf-global--default-color--300: #003737; --pf-global--success-color--100: #92d400; --pf-global--success-color--200: #486b00; --pf-global--info-color--100: #73bcf7; --pf-global--info-color--200: #004368; --pf-global--warning-color--100: #f0ab00; --pf-global--warning-color--200: #795600; --pf-global--danger-color--100: #c9190b; --pf-global--danger-color--200: #a30000; --pf-global--danger-color--300: #470000; --pf-global--BoxShadow--sm: 0 0.0625rem 0.125rem 0 rgba(3, 3, 3, 0.2); --pf-global--BoxShadow--sm-right: 0.25rem 0 0.625rem -0.25rem rgba(3, 3, 3, 0.12); --pf-global--BoxShadow--sm-left: -0.25rem 0 0.625rem -0.25rem rgba(3, 3, 3, 0.12); --pf-global--BoxShadow--sm-bottom: 0 0.25rem 0.625rem -0.25rem rgba(3, 3, 3, 0.12); --pf-global--BoxShadow--sm-top: 0 -0.25rem 0.625rem -0.25rem rgba(3, 3, 3, 0.12); --pf-global--BoxShadow--md: 0 0.0625rem 0.0625rem 0rem rgba(3, 3, 3, 0.05), 0 0.25rem 0.5rem 0.25rem rgba(3, 3, 3, 0.06); --pf-global--BoxShadow--md-right: 0.3125rem 0 0.625rem -0.25rem rgba(3, 3, 3, 0.25); --pf-global--BoxShadow--md-left: -0.3125rem 0 0.625rem -0.25rem rgba(3, 3, 3, 0.25); --pf-global--BoxShadow--md-bottom: 0 0.3125rem 0.625rem -0.25rem rgba(3, 3, 3, 0.25); --pf-global--BoxShadow--md-top: 0 -0.3125rem 0.625rem -0.25rem rgba(3, 3, 3, 0.25); --pf-global--BoxShadow--lg: 0 0.1875rem 0.4375rem 0.1875rem rgba(3, 3, 3, 0.13), 0 0.6875rem 1.5rem 1rem rgba(3, 3, 3, 0.12); --pf-global--BoxShadow--lg-right: 0.75rem 0 0.625rem -0.25rem rgba(3, 3, 3, 0.07); --pf-global--BoxShadow--lg-left: -0.75rem 0 0.625rem -0.25rem rgba(3, 3, 3, 0.07); --pf-global--BoxShadow--lg-bottom: 0 0.75rem 0.625rem -0.25rem rgba(3, 3, 3, 0.07); --pf-global--BoxShadow--lg-top: 0 -0.75rem 0.625rem -0.25rem rgba(3, 3, 3, 0.07); --pf-global--BoxShadow--inset: inset 0 0 0.625rem 0 rgba(3, 3, 3, 0.25); --pf-global--font-path: ./assets/fonts; --pf-global--fonticon-path: ./assets/pficon; --pf-global--spacer--xs: 0.25rem; --pf-global--spacer--sm: 0.5rem; --pf-global--spacer--md: 1rem; --pf-global--spacer--lg: 1.5rem; --pf-global--spacer--xl: 2rem; --pf-global--spacer--2xl: 3rem; --pf-global--spacer--3xl: 4rem; --pf-global--spacer--form-element: 0.375rem; --pf-global--gutter: 1.5rem; --pf-global--gutter--md: 1rem; --pf-global--golden-ratio: 1.681; --pf-global--ZIndex--xs: 100; --pf-global--ZIndex--sm: 200; --pf-global--ZIndex--md: 300; --pf-global--ZIndex--lg: 400; --pf-global--ZIndex--xl: 500; --pf-global--ZIndex--2xl: 600; --pf-global--breakpoint--xs: 0; --pf-global--breakpoint--sm: 576px; --pf-global--breakpoint--md: 768px; --pf-global--breakpoint--lg: 992px; --pf-global--breakpoint--xl: 1200px; --pf-global--breakpoint--2xl: 1450px; --pf-global--link--Color: #06c; --pf-global--link--Color--hover: #004080; --pf-global--link--Color--light: #73bcf7; --pf-global--link--Color--light--hover: #2b9af3; --pf-global--link--Color--dark: #06c; --pf-global--link--Color--dark--hover: #004080; --pf-global--link--FontWeight: 500; --pf-global--link--TextDecoration: none; --pf-global--link--TextDecoration--hover: underline; --pf-global--BorderWidth--sm: 1px; --pf-global--BorderWidth--md: 2px; --pf-global--BorderWidth--lg: 3px; --pf-global--BorderColor--100: #d2d2d2; --pf-global--BorderColor--200: #8a8d90; --pf-global--BorderColor--300: #ededed; --pf-global--BorderColor--dark-100: #d2d2d2; --pf-global--BorderColor--light-100: #b8bbbe; --pf-global--BorderRadius--sm: 3px; --pf-global--BorderRadius--lg: 30em; --pf-global--icon--Color--light: #737679; --pf-global--icon--Color--dark: #151515; --pf-global--icon--FontSize--sm: 0.625rem; --pf-global--icon--FontSize--md: 1.125rem; --pf-global--icon--FontSize--lg: 1.5rem; --pf-global--icon--FontSize--xl: 3.375rem; --pf-global--FontFamily--sans-serif: overpass, overpass, open sans, -apple-system, blinkmacsystemfont, Segoe UI, roboto, Helvetica Neue, arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol; --pf-global--FontFamily--heading--sans-serif: overpass, overpass, open sans, -apple-system, blinkmacsystemfont, Segoe UI, roboto, Helvetica Neue, arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol; --pf-global--FontFamily--monospace: overpass-mono, overpass-mono, SFMono-Regular, menlo, monaco, consolas, Liberation Mono, Courier New, monospace; --pf-global--FontFamily--redhatfont--sans-serif: RedHatText, Overpass, overpass, helvetica, arial, sans-serif; --pf-global--FontFamily--redhatfont--heading--sans-serif: RedHatDisplay, Overpass, overpass, helvetica, arial, sans-serif; --pf-global--FontFamily--redhatfont--monospace: Liberation Mono, consolas, SFMono-Regular, menlo, monaco, Courier New, monospace; --pf-global--FontSize--4xl: 2.25rem; --pf-global--FontSize--3xl: 1.75rem; --pf-global--FontSize--2xl: 1.5rem; --pf-global--FontSize--xl: 1.25rem; --pf-global--FontSize--lg: 1.125rem; --pf-global--FontSize--md: 1rem; --pf-global--FontSize--sm: 0.875rem; --pf-global--FontSize--xs: 0.75rem; --pf-global--FontWeight--light: 300; --pf-global--FontWeight--normal: 400; --pf-global--FontWeight--semi-bold: 500; --pf-global--FontWeight--bold: 600; --pf-global--FontWeight--redhatfont--bold: 700; --pf-global--LineHeight--sm: 1.3; --pf-global--LineHeight--md: 1.5; --pf-global--ListStyle: disc outside; --pf-global--Transition: all 250ms cubic-bezier(0.42, 0, 0.58, 1); --pf-global--TimingFunction: cubic-bezier(0.645, 0.045, 0.355, 1); --pf-global--TransitionDuration: 250ms; --pf-global--arrow--width: 0.9375rem; --pf-global--arrow--width-lg: 1.5625rem; --pf-global--target-size--MinWidth: 44px; --pf-global--target-size--MinHeight: 44px; }

.pf-m-redhat-font { --pf-global--FontFamily--sans-serif: var(--pf-global--FontFamily--redhatfont--sans-serif); --pf-global--FontFamily--heading--sans-serif: var(--pf-global--FontFamily--redhatfont--heading--sans-serif); --pf-global--FontFamily--monospace: var(--pf-global--FontFamily--redhatfont--monospace); --pf-global--FontWeight--semi-bold: var(--pf-global--FontWeight--redhatfont--bold); --pf-global--FontWeight--bold: var(--pf-global--FontWeight--redhatfont--bold); --pf-global--link--FontWeight: var(--pf-global--FontWeight--normal); }

html, body, p, ol, ul, li, dl, dt, dd, blockquote, figure, fieldset, legend, textarea, pre, iframe, hr, h1, h2, h3, h4, h5, h6 { padding: 0; margin: 0; }

html, body { height: 100%; }

h1, h2, h3, h4, h5, h6 { font-size: 100%; font-weight: var(--pf-global--FontWeight--normal); }

ul { list-style: none; }

button, input, optgroup, select, textarea { margin: 0; font-family: inherit; font-size: 100%; line-height: var(--pf-global--LineHeight--md); color: var(--pf-global--Color--100); }

img, embed, iframe, object, audio, video { max-width: 100%; height: auto; }

iframe { border: 0; }

table { border-spacing: 0; border-collapse: collapse; }

td, th { padding: 0; text-align: left; }

*, *::before, *::after { box-sizing: border-box; }

html { font-family: sans-serif; line-height: 1.15; }

body { font-family: var(--pf-global--FontFamily--sans-serif); font-size: var(--pf-global--FontSize--md); font-weight: var(--pf-global--FontWeight--normal); line-height: var(--pf-global--LineHeight--md); text-align: left; background-color: var(--pf-global--BackgroundColor--100); }

a { font-weight: var(--pf-global--link--FontWeight); color: var(--pf-global--link--Color); text-decoration: var(--pf-global--link--TextDecoration); }
a:hover { --pf-global--link--Color: var(--pf-global--link--Color--hover); --pf-global--link--TextDecoration: var(--pf-global--link--TextDecoration--hover); }

button, a { cursor: pointer; }

button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner { padding: 0; border-style: none; }
button:-moz-focusring, [type="button"]:-moz-focusring, [type="reset"]:-moz-focusring, [type="submit"]:-moz-focusring { outline: 1px dotted ButtonText; }

.pf-c-login { --pf-c-login--PaddingTop: var(--pf-global--spacer--lg); --pf-c-login--PaddingBottom: var(--pf-global--spacer--lg); --pf-c-login--xl--BackgroundImage: none; --pf-c-login__container--xl--GridColumnGap: var(--pf-global--spacer--3xl); --pf-c-login__container--MaxWidth: 31.25rem; --pf-c-login__container--xl--MaxWidth: none; --pf-c-login__container--PaddingLeft: 6.125rem; --pf-c-login__container--PaddingRight: 6.125rem; --pf-c-login__container--xl--GridTemplateColumns: 34rem minmax(auto, 34rem); --pf-c-login__header--MarginBottom: var(--pf-global--spacer--md); --pf-c-login__header--sm--PaddingLeft: var(--pf-global--spacer--md); --pf-c-login__header--sm--PaddingRight: var(--pf-global--spacer--md); --pf-c-login__header--xl--MarginBottom: var(--pf-global--spacer--2xl); --pf-c-login__header--xl--MarginTop: var(--pf-global--spacer--3xl); --pf-c-login__header--c-brand--MarginBottom: var(--pf-global--spacer--lg); --pf-c-login__header--c-brand--xl--MarginBottom: var(--pf-global--spacer--2xl); --pf-c-login__main--BackgroundColor: var(--pf-global--BackgroundColor--light-100); --pf-c-login__main--xl--MarginBottom: var(--pf-global--spacer--lg); --pf-c-login__main-header--PaddingTop: var(--pf-global--spacer--2xl); --pf-c-login__main-header--PaddingRight: var(--pf-global--spacer--xl); --pf-c-login__main-header--PaddingBottom: var(--pf-global--spacer--md); --pf-c-login__main-header--PaddingLeft: var(--pf-global--spacer--xl); --pf-c-login__main-header--md--PaddingRight: var(--pf-global--spacer--2xl); --pf-c-login__main-header--md--PaddingLeft: var(--pf-global--spacer--2xl); --pf-c-login__main-header--ColumnGap: var(--pf-global--spacer--md); --pf-c-login__main-header--RowGap: var(--pf-global--spacer--md); --pf-c-login__main-header-desc--MarginBottom: var(--pf-global--spacer--sm); --pf-c-login__main-header-desc--md--MarginBottom: 0; --pf-c-login__main-header-desc--FontSize: var(--pf-global--FontSize--sm); --pf-c-login__main-body--PaddingRight: var(--pf-global--spacer--xl); --pf-c-login__main-body--PaddingBottom: var(--pf-global--spacer--xl); --pf-c-login__main-body--PaddingLeft: var(--pf-global--spacer--xl); --pf-c-login__main-body--md--PaddingRight: var(--pf-global--spacer--2xl); --pf-c-login__main-body--md--PaddingLeft: var(--pf-global--spacer--2xl); --pf-c-login__main-body--c-form__helper-text-icon--FontSize: var(--pf-global--icon--FontSize--md); --pf-c-login__main-body--c-form__helper-text-icon--MarginRight: var(--pf-global--spacer--sm); --pf-c-login__main-footer--PaddingBottom: var(--pf-global--spacer--3xl); --pf-c-login__main-footer--c-title--MarginBottom: var(--pf-global--spacer--md); --pf-c-login__main-footer-links--PaddingTop: var(--pf-global--spacer--sm); --pf-c-login__main-footer-links--PaddingRight: var(--pf-global--spacer--3xl); --pf-c-login__main-footer-links--PaddingBottom: var(--pf-global--spacer--xl); --pf-c-login__main-footer-links--PaddingLeft: var(--pf-global--spacer--3xl); --pf-c-login__main-footer-links-item--PaddingRight: var(--pf-global--spacer--md); --pf-c-login__main-footer-links-item--PaddingLeft: var(--pf-global--spacer--md); --pf-c-login__main-footer-links-item--MarginBottom: var(--pf-global--spacer--sm); --pf-c-login__main-footer-links-item-link-svg--Fill: var(--pf-global--icon--Color--light); --pf-c-login__main-footer-links-item-link-svg--Width: var(--pf-global--icon--FontSize--lg); --pf-c-login__main-footer-links-item-link-svg--Height: var(--pf-global--icon--FontSize--lg); --pf-c-login__main-footer-links-item-link-svg--hover--Fill: var(--pf-global--icon--Color--dark); --pf-c-login__main-footer-band--PaddingTop: var(--pf-global--spacer--lg); --pf-c-login__main-footer-band--PaddingRight: var(--pf-global--spacer--md); --pf-c-login__main-footer-band--PaddingBottom: var(--pf-global--spacer--lg); --pf-c-login__main-footer-band--PaddingLeft: var(--pf-global--spacer--md); --pf-c-login__main-footer-band--BackgroundColor: var(--pf-global--BackgroundColor--300); --pf-c-login__main-footer-band-item--PaddingTop: var(--pf-global--spacer--md); --pf-c-login__footer--sm--PaddingLeft: var(--pf-global--spacer--md); --pf-c-login__footer--sm--PaddingRight: var(--pf-global--spacer--md); --pf-c-login__footer--c-list--PaddingTop: var(--pf-global--spacer--md); --pf-c-login__footer--c-list--xl--PaddingTop: var(--pf-global--spacer--2xl); display: flex; justify-content: center; min-height: 100vh; padding-top: var(--pf-c-login--PaddingTop); padding-bottom: var(--pf-c-login--PaddingBottom); }
@media (min-width: 1200px) { .pf-c-login { --pf-c-login__container--MaxWidth: var(--pf-c-login__container--xl--MaxWidth); } }
@media (min-width: 1200px) { .pf-c-login { --pf-c-login__header--MarginBottom: var(--pf-c-login__header--xl--MarginBottom); --pf-c-login__header--c-brand--MarginBottom: var(--pf-c-login__header--c-brand--xl--MarginBottom); } }
@media (min-width: 768px) { .pf-c-login { --pf-c-login__main-header--PaddingRight: var(--pf-c-login__main-header--md--PaddingRight); --pf-c-login__main-header--PaddingLeft: var(--pf-c-login__main-header--md--PaddingLeft); --pf-c-login__main-header-desc--MarginBottom: var(--pf-c-login__main-header-desc--md--MarginBottom); } }
@media (min-width: 768px) { .pf-c-login { --pf-c-login__main-body--PaddingRight: var(--pf-c-login__main-body--md--PaddingRight); --pf-c-login__main-body--PaddingLeft: var(--pf-c-login__main-body--md--PaddingLeft); } }
@media (min-width: 1200px) { .pf-c-login { --pf-c-login__footer--c-list--PaddingTop: var(--pf-c-login__footer--c-list--xl--PaddingTop); } }
@media (min-width: 1200px) { .pf-c-login { background-image: var(--pf-c-login--xl--BackgroundImage); } }
@media (min-width: 576px) { .pf-c-login { align-items: center; } }

.pf-c-login__container { max-width: var(--pf-c-login__container--MaxWidth); }
@media (min-width: 1200px) { .pf-c-login__container { display: grid; grid-column-gap: var(--pf-c-login__container--xl--GridColumnGap); grid-template-columns: var(--pf-c-login__container--xl--GridTemplateColumns); grid-template-areas: "main header" "main footer" "main ."; padding-right: var(--pf-c-login__container--PaddingRight); padding-left: var(--pf-c-login__container--PaddingLeft); } }

.pf-c-login__header { color: var(--pf-global--Color--100); grid-area: header; }
@media (max-width: 576px) { .pf-c-login__header { padding-right: var(--pf-c-login__header--sm--PaddingRight); padding-left: var(--pf-c-login__header--sm--PaddingLeft); } }
@media (min-width: 1200px) { .pf-c-login__header { margin-top: var(--pf-c-login__header--xl--MarginTop); } }
.pf-c-login__header .pf-c-brand { margin-bottom: var(--pf-c-login__header--c-brand--MarginBottom); }

.pf-c-login__main { grid-area: main; background-color: var(--pf-c-login__main--BackgroundColor); }
.pf-c-login__main > :first-child:not(.pf-c-login__main-header) { padding-top: var(--pf-c-login__main-header--PaddingTop); }
.pf-c-login__main > :last-child:not(.pf-c-login__main-footer) { padding-bottom: var(--pf-c-login__main-footer--PaddingBottom); }
@media (max-width: 1200px) { .pf-c-login__main { margin-bottom: var(--pf-c-login__main--xl--MarginBottom); } }

.pf-c-login__main-header { display: grid; grid-template-columns: 1fr auto; column-gap: var(--pf-c-login__main-header--ColumnGap); row-gap: var(--pf-c-login__main-header--RowGap); align-items: center; padding: var(--pf-c-login__main-header--PaddingTop) var(--pf-c-login__main-header--PaddingRight) var(--pf-c-login__main-header--PaddingBottom) var(--pf-c-login__main-header--PaddingLeft); }
@media (max-width: 768px) { .pf-c-login__main-header { grid-template-columns: 100%; } }
.pf-c-login__main-header .pf-c-dropdown { grid-column: 2 / 3; grid-row: 1; }
@media (max-width: 768px) { .pf-c-login__main-header .pf-c-dropdown { grid-column: auto; grid-row: auto; } }

.pf-c-login__main-header-desc { margin-bottom: var(--pf-c-login__main-header-desc--MarginBottom); font-size: var(--pf-c-login__main-header-desc--FontSize); grid-column: 1 / -1; }

.pf-c-login__main-body { padding-right: var(--pf-c-login__main-body--PaddingRight); padding-bottom: var(--pf-c-login__main-body--PaddingBottom); padding-left: var(--pf-c-login__main-body--PaddingLeft); }
.pf-c-login__main-body .pf-c-form__helper-text { display: flex; align-items: center; }
.pf-c-login__main-body .pf-c-form__helper-text .pf-c-form__helper-text-icon { margin-right: var(--pf-c-login__main-body--c-form__helper-text-icon--MarginRight); font-size: var(--pf-c-login__main-body--c-form__helper-text-icon--FontSize); }

.pf-c-login__main-footer { display: flex; flex-wrap: wrap; }
.pf-c-login__main-footer .pf-c-title { margin-bottom: var(--pf-c-login__main-footer--c-title--MarginBottom); text-align: center; }
.pf-c-login__main-footer > * { flex-basis: 100%; }

.pf-c-login__main-footer-links { display: flex; flex-wrap: wrap; justify-content: center; padding: var(--pf-c-login__main-footer-links--PaddingTop) var(--pf-c-login__main-footer-links--PaddingRight) var(--pf-c-login__main-footer-links--PaddingBottom) var(--pf-c-login__main-footer-links--PaddingLeft); }

.pf-c-login__main-footer-links-item { padding-right: var(--pf-c-login__main-footer-links-item--PaddingRight); padding-left: var(--pf-c-login__main-footer-links-item--PaddingLeft); margin-bottom: var(--pf-c-login__main-footer-links-item--MarginBottom); }

.pf-c-login__main-footer-links-item-link svg { fill: var(--pf-c-login__main-footer-links-item-link-svg--Fill); width: 100%; max-width: var(--pf-c-login__main-footer-links-item-link-svg--Width); height: 100%; max-height: var(--pf-c-login__main-footer-links-item-link-svg--Height); }
.pf-c-login__main-footer-links-item-link:hover svg { fill: var(--pf-c-login__main-footer-links-item-link-svg--hover--Fill); }

.pf-c-login__main-footer-band { padding: var(--pf-c-login__main-footer-band--PaddingTop) var(--pf-c-login__main-footer-band--PaddingRight) var(--pf-c-login__main-footer-band--PaddingBottom) var(--pf-c-login__main-footer-band--PaddingLeft); text-align: center; background-color: var(--pf-c-login__main-footer-band--BackgroundColor); }
.pf-c-login__main-footer-band > * + * { padding-top: var(--pf-c-login__main-footer-band-item--PaddingTop); }

.pf-c-login__footer { color: var(--pf-global--Color--100); grid-area: footer; }
@media (max-width: 576px) { .pf-c-login__footer { padding-right: var(--pf-c-login__footer--sm--PaddingRight); padding-left: var(--pf-c-login__footer--sm--PaddingLeft); } }
.pf-c-login__footer .pf-c-list a { color: unset; }
.pf-c-login__footer .pf-c-list:not(:only-child) { padding-top: var(--pf-c-login__footer--c-list--PaddingTop); }

body { background-color: var(--pf-global--BackgroundColor--dark-100); background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjAuMiwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHdpZHRoPSIxOTJweCIgaGVpZ2h0PSIxNDVweCIgdmlld0JveD0iMCAwIDE5MiAxNDUiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDE5MiAxNDU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojMDIwMzAzO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMTI3LjUsODMuNWMxMi41LDAsMzAuNi0yLjYsMzAuNi0xNy41YzAtMS4yLDAtMi4zLTAuMy0zLjRsLTcuNC0zMi40Yy0xLjctNy4xLTMuMi0xMC4zLTE1LjctMTYuNgoJCWMtOS43LTUtMzAuOC0xMy4xLTM3LjEtMTMuMUM5MS43LDAuNSw5MCw4LDgzLjEsOGMtNi43LDAtMTEuNi01LjYtMTcuOS01LjZjLTYsMC05LjksNC4xLTEyLjksMTIuNWMwLDAtOC40LDIzLjctOS41LDI3LjIKCQljLTAuMiwwLjYtMC4yLDEuNC0wLjIsMS45QzQyLjUsNTMuMyw3OC44LDgzLjUsMTI3LjUsODMuNSBNMTYwLDcyLjFjMS43LDguMiwxLjcsOS4xLDEuNywxMC4xYzAsMTQtMTUuNywyMS44LTM2LjQsMjEuOAoJCWMtNDYuOCwwLTg3LjctMjcuNC04Ny43LTQ1LjVjMC0yLjgsMC42LTUuNCwxLjUtNy4zQzIyLjMsNTIsMC41LDU1LDAuNSw3NC4yYzAsMzEuNSw3NC42LDcwLjMsMTMzLjcsNzAuMwoJCWM0NS4zLDAsNTYuNy0yMC41LDU2LjctMzYuNkMxOTAuOCw5NS4xLDE3OS45LDgwLjcsMTYwLDcyLjEiLz4KPC9nPgo8L3N2Zz4K); background-position: 110% 105%; background-repeat: no-repeat; background-size: 90%; }
@media (min-width: 768px) { body { background-size: 80%; } }
@media (min-width: 1000px) { body { background-position: 110% 100%; background-size: 70%; } }
@media (min-width: 1200px) { body { background-position: 100% 70%; background-size: 70%; } }
@media (min-width: 1400px) { body { background-size: 75%; } }
@media (min-width: 1800px) { body { background-size: 70%; } }
@media (min-width: 1800px) { body { background-position: 75% 80%; background-size: 60%; } }
@media (min-width: 2200px) { body { background-size: 50%; } }

@media (max-width: 1199px) { .pf-c-login__container { width: 100%; } }
.pf-c-login__main-body { padding-bottom: var(--pf-global--spacer--2xl) !important; }

@media (min-width: 1200px) { .pf-c-brand { height: 60px; } }
.pf-c-form { --pf-c-form--GridGap: var(--pf-global--gutter); --pf-c-form__label--Color: var(--pf-global--Color--100); --pf-c-form__label--FontWeight: var(--pf-global--FontWeight--normal); --pf-c-form__label--FontSize: var(--pf-global--FontSize--sm); --pf-c-form__label--LineHeight: var(--pf-global--LineHeight--sm); --pf-c-form__label--PaddingTop: var(--pf-global--spacer--sm); --pf-c-form__label--PaddingBottom: var(--pf-global--spacer--sm); --pf-c-form__label--m-disabled--Color: var(--pf-global--disabled-color--100); --pf-c-form__label-text--FontWeight: var(--pf-global--FontWeight--bold); --pf-c-form__label-required--MarginLeft: var(--pf-global--spacer--xs); --pf-c-form__label-required--FontSize: var(--pf-global--FontSize--sm); --pf-c-form__label-required--Color: var(--pf-global--danger-color--100); --pf-c-form__group--MarginLeft: var(--pf-global--spacer--sm); --pf-c-form--m-horizontal--md__group--GridTemplateColumns: 150px 1fr; --pf-c-form__group--m-action--MarginTop: var(--pf-global--spacer--xl); --pf-c-form__actions--child--MarginTop: var(--pf-global--spacer--sm); --pf-c-form__actions--child--MarginRight: var(--pf-global--spacer--sm); --pf-c-form__actions--child--MarginBottom: var(--pf-global--spacer--sm); --pf-c-form__actions--child--MarginLeft: var(--pf-global--spacer--sm); --pf-c-form__actions--MarginTop: calc(var(--pf-c-form__actions--child--MarginTop) * -1); --pf-c-form__actions--MarginRight: calc(var(--pf-c-form__actions--child--MarginRight) * -1); --pf-c-form__actions--MarginBottom: calc(var(--pf-c-form__actions--child--MarginBottom) * -1); --pf-c-form__actions--MarginLeft: calc(var(--pf-c-form__actions--child--MarginLeft) * -1); --pf-c-form__helper-text--MarginTop: var(--pf-global--spacer--xs); --pf-c-form__helper-text--FontSize: var(--pf-global--FontSize--sm); --pf-c-form__helper-text--Color: var(--pf-global--Color--100); --pf-c-form--m-inline--MarginRight: var(--pf-global--spacer--lg); --pf-c-form--m-error--Color: var(--pf-global--danger-color--100); --pf-c-form--m-success--Color: var(--pf-global--success-color--200); display: grid; grid-gap: var(--pf-c-form--GridGap); }
.pf-c-form.pf-m-horizontal.pf-m-align-right .pf-c-form__label { text-align: right; }
.pf-c-form.pf-m-horizontal .pf-c-form__group { display: grid; grid-column-gap: var(--pf-c-form--GridGap); }
.pf-c-form.pf-m-horizontal .pf-c-form__group .pf-c-form-control { align-self: start; }
@media (min-width: 768px) { .pf-c-form.pf-m-horizontal .pf-c-form__group { grid-template-columns: var(--pf-c-form--m-horizontal--md__group--GridTemplateColumns); }
  .pf-c-form.pf-m-horizontal .pf-c-form__group .pf-c-form__label { padding-top: var(--pf-c-form__label--PaddingTop); padding-bottom: 0; }
  .pf-c-form.pf-m-horizontal .pf-c-form__group .pf-c-form__label.pf-m-no-padding-top { --pf-c-form__label--PaddingTop: 0; }
  .pf-c-form.pf-m-horizontal .pf-c-form-control, .pf-c-form.pf-m-horizontal .pf-c-form__horizontal-group, .pf-c-form.pf-m-horizontal .pf-c-form__helper-text, .pf-c-form.pf-m-horizontal .pf-c-check { grid-column-start: 2; } }

.pf-c-form__group.pf-m-action { margin-top: var(--pf-c-form__group--m-action--MarginTop); }
.pf-c-form__group.pf-m-inline { display: flex; flex-flow: row wrap; }
.pf-c-form__group.pf-m-inline .pf-c-form__label { flex-basis: 100%; }
.pf-c-form__group.pf-m-inline > *:not(.pf-c-form__label) { flex: auto 0; margin-right: var(--pf-c-form--m-inline--MarginRight); }
.pf-c-form__group .pf-c-form__label { padding-bottom: var(--pf-c-form__label--PaddingBottom); }
.pf-c-form__group .pf-c-form__helper-text { margin-top: var(--pf-c-form__helper-text--MarginTop); }

.pf-c-form__label { display: inline-block; font-size: var(--pf-c-form__label--FontSize); font-weight: var(--pf-c-form__label--FontWeight); line-height: var(--pf-c-form__label--LineHeight); color: var(--pf-c-form__label--Color); }
.pf-c-form__label::selection { background-color: none; }
.pf-c-form__label:not(.pf-m-disabled):hover { cursor: pointer; }
.pf-c-form__label.pf-m-disabled { --pf-c-form__label--Color: var(--pf-c-form__label--m-disabled--Color); }
.pf-c-form__label.pf-m-disabled:hover { cursor: not-allowed; }

.pf-c-form__label-text { font-weight: var(--pf-c-form__label-text--FontWeight); }

.pf-c-form__label-required { margin-left: var(--pf-c-form__label-required--MarginLeft); font-size: var(--pf-c-form__label-required--FontSize); color: var(--pf-c-form__label-required--Color); }

.pf-c-form__helper-text { font-size: var(--pf-c-form__helper-text--FontSize); color: var(--pf-c-form__helper-text--Color); }
.pf-c-form__helper-text.pf-m-error { --pf-c-form__helper-text--Color: var(--pf-c-form--m-error--Color); }
.pf-c-form__helper-text.pf-m-success { --pf-c-form__helper-text--Color: var(--pf-c-form--m-success--Color); }
.pf-c-form__helper-text.pf-m-inactive { display: none; visibility: hidden; }
.pf-c-form__helper-text.pf-m-hidden { visibility: hidden; opacity: 0; }

.pf-c-form__fieldset { border: 0; }

.pf-c-form__actions { display: flex; flex-wrap: wrap; margin-top: var(--pf-c-form__actions--MarginTop); margin-right: var(--pf-c-form__actions--MarginRight); margin-bottom: var(--pf-c-form__actions--MarginBottom); margin-left: var(--pf-c-form__actions--MarginLeft); overflow: hidden; }
.pf-c-form__actions > * { margin-top: var(--pf-c-form__actions--child--MarginTop); margin-right: var(--pf-c-form__actions--child--MarginRight); margin-bottom: var(--pf-c-form__actions--child--MarginBottom); margin-left: var(--pf-c-form__actions--child--MarginLeft); }

.pf-c-form-control { --pf-c-form-control--FontSize: var(--pf-global--FontSize--md); --pf-c-form-control--LineHeight: var(--pf-global--LineHeight--md); --pf-c-form-control--BorderWidth: var(--pf-global--BorderWidth--sm); --pf-c-form-control--BorderTopColor: var(--pf-global--BorderColor--300); --pf-c-form-control--BorderRightColor: var(--pf-global--BorderColor--300); --pf-c-form-control--BorderBottomColor: var(--pf-global--BorderColor--200); --pf-c-form-control--BorderLeftColor: var(--pf-global--BorderColor--300); --pf-c-form-control--BorderRadius: 0; --pf-c-form-control--BackgroundColor: var(--pf-global--BackgroundColor--100); --pf-c-form-control--Height: calc(var(--pf-c-form-control--FontSize) * var(--pf-c-form-control--LineHeight) + var(--pf-c-form-control--BorderWidth) * 2 + var(--pf-c-form-control--PaddingTop) + var(--pf-c-form-control--PaddingBottom)); --pf-c-form-control--PaddingTop: calc(var(--pf-global--spacer--form-element) - var(--pf-global--BorderWidth--sm)); --pf-c-form-control--PaddingBottom: calc(var(--pf-global--spacer--form-element) - var(--pf-global--BorderWidth--sm)); --pf-c-form-control--PaddingRight: var(--pf-global--spacer--sm); --pf-c-form-control--PaddingLeft: var(--pf-global--spacer--sm); --pf-c-form-control--hover--BorderBottomColor: var(--pf-global--primary-color--100); --pf-c-form-control--focus--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-form-control--focus--PaddingBottom: calc(var(--pf-global--spacer--form-element) - var(--pf-c-form-control--focus--BorderBottomWidth)); --pf-c-form-control--focus--BorderBottomColor: var(--pf-global--primary-color--100); --pf-c-form-control--placeholder--Color: var(--pf-global--Color--dark-200); --pf-c-form-control--disabled--Color: var(--pf-global--disabled-color--100); --pf-c-form-control--disabled--BackgroundColor: var(--pf-global--disabled-color--300); --pf-c-form-control--disabled--BorderColor: transparent; --pf-c-form-control--readonly--focus--BackgroundColor: var(--pf-global--disabled-color--300); --pf-c-form-control--readonly--focus--PaddingBottom: var(--pf-c-form-control--PaddingBottom); --pf-c-form-control--readonly--focus--BorderBottomWidth: var(--pf-global--BorderWidth--sm); --pf-c-form-control--readonly--focus--BorderBottomColor: var(--pf-global--disabled-color--100); --pf-c-form-control--invalid--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-form-control--invalid--PaddingBottom: calc(var(--pf-global--spacer--form-element) - var(--pf-c-form-control--invalid--BorderBottomWidth)); --pf-c-form-control--invalid--BorderBottomColor: var(--pf-global--danger-color--100); --pf-c-form-control--invalid--PaddingRight: var(--pf-global--spacer--xl); --pf-c-form-control--invalid--BackgroundPosition: calc(100% - var(--pf-c-form-control--PaddingLeft)) var(--pf-c-form-control--PaddingLeft); --pf-c-form-control--invalid--BackgroundSize: var(--pf-c-form-control--FontSize) var(--pf-c-form-control--FontSize); --pf-c-form-control--invalid--BackgroundUrl: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23c9190b' d='M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zm-248 50c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z'/%3E%3C/svg%3E"); --pf-c-form-control--invalid--exclamation--Background: var(--pf-c-form-control--invalid--BackgroundUrl) var(--pf-c-form-control--invalid--BackgroundPosition) / var(--pf-c-form-control--invalid--BackgroundSize) no-repeat; --pf-c-form-control--invalid--Background: var(--pf-c-form-control--BackgroundColor) var(--pf-c-form-control--invalid--exclamation--Background); --pf-c-form-control--success--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-form-control--success--PaddingBottom: calc(var(--pf-global--spacer--form-element) - var(--pf-c-form-control--success--BorderBottomWidth)); --pf-c-form-control--success--BorderBottomColor: var(--pf-global--success-color--100); --pf-c-form-control--success--PaddingRight: var(--pf-global--spacer--xl); --pf-c-form-control--success--BackgroundPosition: calc(100% - var(--pf-c-form-control--PaddingLeft)) var(--pf-c-form-control--PaddingLeft); --pf-c-form-control--success--BackgroundSize: var(--pf-c-form-control--FontSize) var(--pf-c-form-control--FontSize); --pf-c-form-control--success--BackgroundUrl: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%2392d400' d='M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z'/%3E%3C/svg%3E"); --pf-c-form-control--success--check--Background: var(--pf-c-form-control--success--BackgroundUrl) var(--pf-c-form-control--success--BackgroundPosition) / var(--pf-c-form-control--success--BackgroundSize) no-repeat; --pf-c-form-control--success--Background: var(--pf-c-form-control--BackgroundColor) var(--pf-c-form-control--success--check--Background); --pf-c-form-control--m-search--PaddingLeft: var(--pf-global--spacer--xl); --pf-c-form-control--m-search--BackgroundPosition: var(--pf-c-form-control--PaddingLeft); --pf-c-form-control--m-search--BackgroundSize: var(--pf-c-form-control--FontSize) var(--pf-c-form-control--FontSize); --pf-c-form-control--m-search--BackgroundUrl: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='%23737679' d='M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z'/%3E%3C/svg%3E"); --pf-c-form-control--m-search--Background: var(--pf-c-form-control--m-search--BackgroundUrl) var(--pf-c-form-control--m-search--BackgroundPosition) / var(--pf-c-form-control--m-search--BackgroundSize) no-repeat; --pf-c-form-control__select--PaddingRight: var(--pf-global--spacer--lg); --pf-c-form-control__select--BackgroundUrl: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'%3E%3Cpath fill='%23urrentColor' d='M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z'/%3E%3C/svg%3E"); --pf-c-form-control__select--BackgroundSize: 0.875rem; --pf-c-form-control__select--BackgroundPosition: calc(100% - var(--pf-global--spacer--sm)) center; --pf-c-form-control__select--arrow--Background: var(--pf-c-form-control--BackgroundColor) var(--pf-c-form-control__select--BackgroundUrl) var(--pf-c-form-control__select--BackgroundPosition) / var(--pf-c-form-control__select--BackgroundSize) no-repeat; --pf-c-form-control__select--Background: var(--pf-c-form-control__select--arrow--Background); --pf-c-form-control__select--invalid--Background: var(--pf-c-form-control--invalid--exclamation--Background), var(--pf-c-form-control__select--arrow--Background); --pf-c-form-control__select--invalid--PaddingRight: calc(var(--pf-global--spacer--sm) + var(--pf-global--spacer--2xl)); --pf-c-form-control__select--success--Background: var(--pf-c-form-control--success--check--Background), var(--pf-c-form-control__select--arrow--Background); --pf-c-form-control__select--success--PaddingRight: calc(var(--pf-global--spacer--sm) + var(--pf-global--spacer--2xl)); color: var(--pf-global--Color--100); width: 100%; padding: var(--pf-c-form-control--PaddingTop) var(--pf-c-form-control--PaddingRight) var(--pf-c-form-control--PaddingBottom) var(--pf-c-form-control--PaddingLeft); font-size: var(--pf-c-form-control--FontSize); line-height: var(--pf-c-form-control--LineHeight); background-color: var(--pf-c-form-control--BackgroundColor); border: var(--pf-c-form-control--BorderWidth) solid; border-color: var(--pf-c-form-control--BorderTopColor) var(--pf-c-form-control--BorderRightColor) var(--pf-c-form-control--BorderBottomColor) var(--pf-c-form-control--BorderLeftColor); border-radius: var(--pf-c-form-control--BorderRadius); -moz-appearance: none; -webkit-appearance: none; }
.pf-c-form-control::placeholder { --pf-c-form-control--Color: var(--pf-c-form-control--placeholder--Color); }
.pf-c-form-control:not(textarea) { height: var(--pf-c-form-control--Height); text-overflow: ellipsis; }
.pf-c-form-control[readonly], .pf-c-form-control[readonly].pf-m-hover, .pf-c-form-control[readonly]:hover, .pf-c-form-control[readonly].pf-m-focus, .pf-c-form-control[readonly]:focus { --pf-c-form-control--BorderBottomColor: var(--pf-c-form-control--readonly--focus--BorderBottomColor); padding-bottom: var(--pf-c-form-control--readonly--focus--PaddingBottom); background-color: var(--pf-c-form-control--readonly--focus--BackgroundColor); border-bottom-width: var(--pf-c-form-control--readonly--focus--BorderBottomWidth); }
.pf-c-form-control.pf-m-hover, .pf-c-form-control:hover { --pf-c-form-control--BorderBottomColor: var(--pf-c-form-control--hover--BorderBottomColor); }
.pf-c-form-control.pf-m-focus, .pf-c-form-control:focus { --pf-c-form-control--BorderBottomColor: var(--pf-c-form-control--focus--BorderBottomColor); padding-bottom: var(--pf-c-form-control--focus--PaddingBottom); border-bottom-width: var(--pf-c-form-control--focus--BorderBottomWidth); }
.pf-c-form-control:disabled { --pf-c-form-control--Color: var(--pf-c-form-control--disabled--Color); --pf-c-form-control--BackgroundColor: var(--pf-c-form-control--disabled--BackgroundColor); cursor: not-allowed; border-color: var(--pf-c-form-control--disabled--BorderColor); }
.pf-c-form-control[aria-invalid="true"] { --pf-c-form-control--PaddingRight: var(--pf-c-form-control--invalid--PaddingRight); --pf-c-form-control--BorderBottomColor: var(--pf-c-form-control--invalid--BorderBottomColor); padding-bottom: var(--pf-c-form-control--invalid--PaddingBottom); background: var(--pf-c-form-control--invalid--Background); border-bottom-width: var(--pf-c-form-control--invalid--BorderBottomWidth); }
.pf-c-form-control.pf-m-success { --pf-c-form-control--PaddingRight: var(--pf-c-form-control--success--PaddingRight); --pf-c-form-control--BorderBottomColor: var(--pf-c-form-control--success--BorderBottomColor); padding-bottom: var(--pf-c-form-control--success--PaddingBottom); background: var(--pf-c-form-control--success--Background); border-bottom-width: var(--pf-c-form-control--success--BorderBottomWidth); }
.pf-c-form-control.pf-m-search { padding-left: var(--pf-c-form-control--m-search--PaddingLeft); background: var(--pf-c-form-control--m-search--Background); }
select.pf-c-form-control { --pf-c-form-control--PaddingRight: var(--pf-c-form-control__select--PaddingRight); background: var(--pf-c-form-control__select--Background); }
select.pf-c-form-control[aria-invalid="true"] { --pf-c-form-control--PaddingRight: var(--pf-c-form-control__select--invalid--PaddingRight); --pf-c-form-control--invalid--BackgroundPosition: calc(100% - var(--pf-global--spacer--sm) - var(--pf-global--spacer--lg)); --pf-c-form-control--invalid--Background: var(--pf-c-form-control__select--invalid--Background); }
select.pf-c-form-control.pf-m-success { --pf-c-form-control--PaddingRight: var(--pf-c-form-control__select--success--PaddingRight); --pf-c-form-control--success--BackgroundPosition: calc(100% - var(--pf-global--spacer--sm) - var(--pf-global--spacer--lg)); --pf-c-form-control--success--Background: var(--pf-c-form-control__select--success--Background); }
.pf-c-form-control.pf-m-resize-vertical { resize: vertical; }
.pf-c-form-control.pf-m-resize-horizontal { resize: horizontal; }

.error-placeholder { min-height: 21px; }

.pf-m-error__icon { margin-right: 5px; }

.pf-c-form__helper-text.pf-m-error { color: var(--pf-global--danger-color--100); }

.pf-c-button { --pf-c-button--PaddingTop: var(--pf-global--spacer--form-element); --pf-c-button--PaddingRight: var(--pf-global--spacer--md); --pf-c-button--PaddingBottom: var(--pf-global--spacer--form-element); --pf-c-button--PaddingLeft: var(--pf-global--spacer--md); --pf-c-button--LineHeight: var(--pf-global--LineHeight--md); --pf-c-button--FontWeight: var(--pf-global--FontWeight--semi-bold); --pf-c-button--FontSize: var(--pf-global--FontSize--md); --pf-c-button--BorderRadius: var(--pf-global--BorderRadius--sm); --pf-c-button--BorderColor: transparent; --pf-c-button--BorderWidth: var(--pf-global--BorderWidth--sm); --pf-c-button--hover--BorderWidth: var(--pf-global--BorderWidth--md); --pf-c-button--focus--BorderWidth: var(--pf-global--BorderWidth--md); --pf-c-button--active--BorderWidth: var(--pf-global--BorderWidth--md); --pf-c-button--disabled--Color: var(--pf-global--disabled-color--100); --pf-c-button--disabled--BackgroundColor: var(--pf-global--disabled-color--200); --pf-c-button--disabled--BorderColor: transparent; --pf-c-button--m-primary--BackgroundColor: var(--pf-global--primary-color--100); --pf-c-button--m-primary--Color: var(--pf-global--Color--light-100); --pf-c-button--m-primary--hover--BackgroundColor: var(--pf-global--primary-color--200); --pf-c-button--m-primary--hover--Color: var(--pf-global--Color--light-100); --pf-c-button--m-primary--focus--BackgroundColor: var(--pf-global--primary-color--200); --pf-c-button--m-primary--focus--Color: var(--pf-global--Color--light-100); --pf-c-button--m-primary--active--BackgroundColor: var(--pf-global--primary-color--200); --pf-c-button--m-primary--active--Color: var(--pf-global--Color--light-100); --pf-c-button--m-secondary--BackgroundColor: transparent; --pf-c-button--m-secondary--BorderColor: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--Color: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--hover--BackgroundColor: transparent; --pf-c-button--m-secondary--hover--BorderColor: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--hover--Color: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--focus--BackgroundColor: transparent; --pf-c-button--m-secondary--focus--BorderColor: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--focus--Color: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--active--BackgroundColor: transparent; --pf-c-button--m-secondary--active--BorderColor: var(--pf-global--primary-color--100); --pf-c-button--m-secondary--active--Color: var(--pf-global--primary-color--100); --pf-c-button--m-tertiary--BackgroundColor: transparent; --pf-c-button--m-tertiary--BorderColor: var(--pf-global--Color--100); --pf-c-button--m-tertiary--Color: var(--pf-global--Color--100); --pf-c-button--m-tertiary--hover--BackgroundColor: transparent; --pf-c-button--m-tertiary--hover--BorderColor: var(--pf-global--Color--100); --pf-c-button--m-tertiary--hover--Color: var(--pf-global--Color--100); --pf-c-button--m-tertiary--focus--BackgroundColor: transparent; --pf-c-button--m-tertiary--focus--BorderColor: var(--pf-global--Color--100); --pf-c-button--m-tertiary--focus--Color: var(--pf-global--Color--100); --pf-c-button--m-tertiary--active--BackgroundColor: transparent; --pf-c-button--m-tertiary--active--BorderColor: var(--pf-global--Color--100); --pf-c-button--m-tertiary--active--Color: var(--pf-global--Color--100); --pf-c-button--m-danger--BackgroundColor: var(--pf-global--danger-color--100); --pf-c-button--m-danger--Color: var(--pf-global--Color--light-100); --pf-c-button--m-danger--hover--BackgroundColor: var(--pf-global--danger-color--200); --pf-c-button--m-danger--hover--Color: var(--pf-global--Color--light-100); --pf-c-button--m-danger--focus--BackgroundColor: var(--pf-global--danger-color--200); --pf-c-button--m-danger--focus--Color: var(--pf-global--Color--light-100); --pf-c-button--m-danger--active--BackgroundColor: var(--pf-global--danger-color--200); --pf-c-button--m-danger--active--Color: var(--pf-global--Color--light-100); --pf-c-button--m-link--Color: var(--pf-global--link--Color); --pf-c-button--m-link--hover--Color: var(--pf-global--link--Color--hover); --pf-c-button--m-link--focus--Color: var(--pf-global--link--Color--hover); --pf-c-button--m-link--active--Color: var(--pf-global--link--Color--hover); --pf-c-button--m-link--disabled--BackgroundColor: transparent; --pf-c-button--m-link--m-inline--hover--TextDecoration: var(--pf-global--link--TextDecoration--hover); --pf-c-button--m-link--m-inline--hover--Color: var(--pf-global--link--Color--hover); --pf-c-button--m-plain--Color: var(--pf-global--Color--200); --pf-c-button--m-plain--hover--Color: var(--pf-global--Color--100); --pf-c-button--m-plain--focus--Color: var(--pf-global--Color--100); --pf-c-button--m-plain--active--Color: var(--pf-global--Color--100); --pf-c-button--m-plain--disabled--Color: var(--pf-global--disabled-color--200); --pf-c-button--m-plain--disabled--BackgroundColor: transparent; --pf-c-button--m-control--after--BorderWidth: var(--pf-global--BorderWidth--sm); --pf-c-button--m-control--after--BorderTopColor: var(--pf-global--BorderColor--300); --pf-c-button--m-control--after--BorderRightColor: var(--pf-global--BorderColor--300); --pf-c-button--m-control--after--BorderBottomColor: var(--pf-global--BorderColor--200); --pf-c-button--m-control--after--BorderLeftColor: var(--pf-global--BorderColor--300); --pf-c-button--m-control--hover--after--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-button--m-control--hover--after--BorderBottomColor: var(--pf-global--active-color--100); --pf-c-button--m-control--active--after--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-button--m-control--active--after--BorderBottomColor: var(--pf-global--active-color--100); --pf-c-button--m-control--focus--after--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-button--m-control--focus--after--BorderBottomColor: var(--pf-global--active-color--100); --pf-c-button--m-control--m-expanded--after--BorderBottomWidth: var(--pf-global--BorderWidth--md); --pf-c-button--m-control--m-expanded--after--BorderBottomColor: var(--pf-global--active-color--100); --pf-c-button--m-control--disabled--after--BorderBottomColor: var(--pf-global--BorderColor--300); --pf-c-button--m-control--disabled--BackgroundColor: transparent; --pf-c-button__icon--MarginRight: var(--pf-global--spacer--xs); --pf-c-button__text--icon--MarginLeft: var(--pf-global--spacer--xs); position: relative; display: inline-block; padding: var(--pf-c-button--PaddingTop) var(--pf-c-button--PaddingRight) var(--pf-c-button--PaddingBottom) var(--pf-c-button--PaddingLeft); font-size: var(--pf-c-button--FontSize); font-weight: var(--pf-c-button--FontWeight); line-height: var(--pf-c-button--LineHeight); text-align: center; white-space: nowrap; user-select: none; border: 0; border-radius: var(--pf-c-button--BorderRadius); }
.pf-c-button .pf-c-button__icon { margin-right: var(--pf-c-button__icon--MarginRight); }
.pf-c-button .pf-c-button__text + .pf-c-button__icon { margin-right: 0; margin-left: var(--pf-c-button__text--icon--MarginLeft); }
.pf-c-button::after { position: absolute; top: 0; right: 0; bottom: 0; left: 0; content: ""; border: var(--pf-c-button--BorderWidth) solid; border-color: var(--pf-c-button--BorderColor); border-radius: var(--pf-c-button--BorderRadius); }
.pf-c-button:hover, .pf-c-button.pf-m-hover { text-decoration: none; }
.pf-c-button:hover::after, .pf-c-button.pf-m-hover::after { --pf-c-button--BorderWidth: var(--pf-c-button--hover--BorderWidth); }
.pf-c-button:active::after, .pf-c-button.pf-m-active::after { --pf-c-button--BorderWidth: var(--pf-c-button--active--BorderWidth); }
.pf-c-button:focus::after, .pf-c-button.pf-m-focus::after { --pf-c-button--BorderWidth: var(--pf-c-button--focus--BorderWidth); }
.pf-c-button.pf-m-block { display: block; width: 100%; }
.pf-c-button.pf-m-primary { color: var(--pf-c-button--m-primary--Color); background-color: var(--pf-c-button--m-primary--BackgroundColor); }
.pf-c-button.pf-m-primary:hover, .pf-c-button.pf-m-primary.pf-m-hover { --pf-c-button--m-primary--Color: var(--pf-c-button--m-primary--hover--Color); --pf-c-button--m-primary--BackgroundColor: var(--pf-c-button--m-primary--hover--BackgroundColor); }
.pf-c-button.pf-m-primary:active, .pf-c-button.pf-m-primary.pf-m-active { --pf-c-button--m-primary--Color: var(--pf-c-button--m-primary--active--Color); --pf-c-button--m-primary--BackgroundColor: var(--pf-c-button--m-primary--active--BackgroundColor); }
.pf-c-button.pf-m-primary:focus, .pf-c-button.pf-m-primary.pf-m-focus { --pf-c-button--m-primary--Color: var(--pf-c-button--m-primary--focus--Color); --pf-c-button--m-primary--BackgroundColor: var(--pf-c-button--m-primary--focus--BackgroundColor); }
.pf-c-button.pf-m-secondary { color: var(--pf-c-button--m-secondary--Color); background-color: var(--pf-c-button--m-secondary--BackgroundColor); }
.pf-c-button.pf-m-secondary::after { --pf-c-button--BorderColor: var(--pf-c-button--m-secondary--BorderColor); }
.pf-c-button.pf-m-secondary:hover, .pf-c-button.pf-m-secondary.pf-m-hover { --pf-c-button--m-secondary--Color: var(--pf-c-button--m-secondary--hover--Color); --pf-c-button--m-secondary--BackgroundColor: var(--pf-c-button--m-secondary--hover--BackgroundColor); }
.pf-c-button.pf-m-secondary:hover::after, .pf-c-button.pf-m-secondary.pf-m-hover::after { --pf-c-button--BorderColor: var(--pf-c-button--m-secondary--hover--BorderColor); }
.pf-c-button.pf-m-secondary:active, .pf-c-button.pf-m-secondary.pf-m-active { --pf-c-button--m-secondary--Color: var(--pf-c-button--m-secondary--active--Color); --pf-c-button--m-secondary--BackgroundColor: var(--pf-c-button--m-secondary--active--BackgroundColor); }
.pf-c-button.pf-m-secondary:active::after, .pf-c-button.pf-m-secondary.pf-m-active::after { --pf-c-button--BorderColor: var(--pf-c-button--m-secondary--active--BorderColor); }
.pf-c-button.pf-m-secondary:focus, .pf-c-button.pf-m-secondary.pf-m-focus { --pf-c-button--m-secondary--Color: var(--pf-c-button--m-secondary--focus--Color); --pf-c-button--m-secondary--BackgroundColor: var(--pf-c-button--m-secondary--focus--BackgroundColor); }
.pf-c-button.pf-m-secondary:focus::after, .pf-c-button.pf-m-secondary.pf-m-focus::after { --pf-c-button--BorderColor: var(--pf-c-button--m-secondary--focus--BorderColor); }
.pf-c-button.pf-m-tertiary { color: var(--pf-c-button--m-tertiary--Color); background-color: var(--pf-c-button--m-tertiary--BackgroundColor); }
.pf-c-button.pf-m-tertiary::after { --pf-c-button--BorderColor: var(--pf-c-button--m-tertiary--BorderColor); }
.pf-c-button.pf-m-tertiary:hover, .pf-c-button.pf-m-tertiary.pf-m-hover { --pf-c-button--m-tertiary--Color: var(--pf-c-button--m-tertiary--hover--Color); --pf-c-button--m-tertiary--BackgroundColor: var(--pf-c-button--m-tertiary--hover--BackgroundColor); }
.pf-c-button.pf-m-tertiary:hover::after, .pf-c-button.pf-m-tertiary.pf-m-hover::after { --pf-c-button--BorderColor: var(--pf-c-button--m-tertiary--hover--BorderColor); }
.pf-c-button.pf-m-tertiary:active, .pf-c-button.pf-m-tertiary.pf-m-active { --pf-c-button--m-tertiary--Color: var(--pf-c-button--m-tertiary--active--Color); --pf-c-button--m-tertiary--BackgroundColor: var(--pf-c-button--m-tertiary--active--BackgroundColor); }
.pf-c-button.pf-m-tertiary:active::after, .pf-c-button.pf-m-tertiary.pf-m-active::after { --pf-c-button--BorderColor: var(--pf-c-button--m-tertiary--active--BorderColor); }
.pf-c-button.pf-m-tertiary:focus, .pf-c-button.pf-m-tertiary.pf-m-focus { --pf-c-button--m-tertiary--Color: var(--pf-c-button--m-tertiary--focus--Color); --pf-c-button--m-tertiary--BackgroundColor: var(--pf-c-button--m-tertiary--focus--BackgroundColor); }
.pf-c-button.pf-m-tertiary:focus::after, .pf-c-button.pf-m-tertiary.pf-m-focus::after { --pf-c-button--BorderColor: var(--pf-c-button--m-tertiary--focus--BorderColor); }
.pf-c-button.pf-m-danger { color: var(--pf-c-button--m-danger--Color); background-color: var(--pf-c-button--m-danger--BackgroundColor); }
.pf-c-button.pf-m-danger:hover, .pf-c-button.pf-m-danger.pf-m-hover { --pf-c-button--m-danger--Color: var(--pf-c-button--m-danger--hover--Color); --pf-c-button--m-danger--BackgroundColor: var(--pf-c-button--m-danger--hover--BackgroundColor); }
.pf-c-button.pf-m-danger:active, .pf-c-button.pf-m-danger.pf-m-active { --pf-c-button--m-danger--Color: var(--pf-c-button--m-danger--active--Color); --pf-c-button--m-danger--BackgroundColor: var(--pf-c-button--m-danger--active--BackgroundColor); }
.pf-c-button.pf-m-danger:focus, .pf-c-button.pf-m-danger.pf-m-focus { --pf-c-button--m-danger--Color: var(--pf-c-button--m-danger--focus--Color); --pf-c-button--m-danger--BackgroundColor: var(--pf-c-button--m-danger--focus--BackgroundColor); }
.pf-c-button.pf-m-link { color: var(--pf-c-button--m-link--Color); }
.pf-c-button.pf-m-link:not(.pf-m-inline):hover, .pf-c-button.pf-m-link:not(.pf-m-inline).pf-m-hover { --pf-c-button--m-link--Color: var(--pf-c-button--m-link--hover--Color); }
.pf-c-button.pf-m-link:not(.pf-m-inline):active, .pf-c-button.pf-m-link:not(.pf-m-inline).pf-m-active { --pf-c-button--m-link--Color: var(--pf-c-button--m-link--active--Color); }
.pf-c-button.pf-m-link:not(.pf-m-inline):focus, .pf-c-button.pf-m-link:not(.pf-m-inline).pf-m-focus { --pf-c-button--m-link--Color: var(--pf-c-button--m-link--focus--Color); }
.pf-c-button.pf-m-link:disabled, .pf-c-button.pf-m-link.pf-m-disabled { background-color: var(--pf-c-button--m-link--disabled--BackgroundColor); }
.pf-c-button.pf-m-link.pf-m-inline { display: inline; padding: 0; }
.pf-c-button.pf-m-link.pf-m-inline:hover { --pf-c-button--m-link--Color: var(--pf-c-button--m-link--m-inline--hover--Color); text-decoration: var(--pf-c-button--m-link--m-inline--hover--TextDecoration); }
.pf-c-button.pf-m-control::after { --pf-c-button--BorderWidth: var(--pf-c-button--m-control--after--BorderWidth); --pf-c-button--BorderColor: var(--pf-c-button--m-control--after--BorderTopColor) var(--pf-c-button--m-control--after--BorderRightColor) var(--pf-c-button--m-control--after--BorderBottomColor) var(--pf-c-button--m-control--after--BorderLeftColor); border-radius: initial; }
.pf-c-button.pf-m-control:hover::after, .pf-c-button.pf-m-control.pf-m-hover::after { --pf-c-button--m-control--after--BorderBottomColor: var(--pf-c-button--m-control--hover--after--BorderBottomColor); border-bottom-width: var(--pf-c-button--m-control--hover--after--BorderBottomWidth); }
.pf-c-button.pf-m-control:active::after, .pf-c-button.pf-m-control.pf-m-active::after { --pf-c-button--m-control--after--BorderBottomColor: var(--pf-c-button--m-control--active--after--BorderBottomColor); border-bottom-width: var(--pf-c-button--m-control--active--after--BorderBottomWidth); }
.pf-c-button.pf-m-control:focus::after, .pf-c-button.pf-m-control.pf-m-focus::after { --pf-c-button--m-control--after--BorderBottomColor: var(--pf-c-button--m-control--focus--after--BorderBottomColor); border-bottom-width: var(--pf-c-button--m-control--focus--after--BorderBottomWidth); }
.pf-c-button.pf-m-control.pf-m-expanded::after { --pf-c-button--m-control--after--BorderBottomColor: var(--pf-c-button--m-control--m-expanded--after--BorderBottomColor); border-bottom-width: var(--pf-c-button--m-control--m-expanded--after--BorderBottomWidth); }
.pf-c-button.pf-m-control:disabled, .pf-c-button.pf-m-control.pf-m-disabled { background-color: var(--pf-c-button--m-control--disabled--BackgroundColor); }
.pf-c-button.pf-m-control:disabled::after, .pf-c-button.pf-m-control.pf-m-disabled::after { --pf-c-button--m-control--after--BorderBottomColor: var(--pf-c-button--m-control--disabled--after--BorderBottomColor); }
.pf-c-button:disabled, .pf-c-button.pf-m-disabled { color: var(--pf-c-button--disabled--Color); pointer-events: none; background-color: var(--pf-c-button--disabled--BackgroundColor); }
.pf-c-button:disabled::after, .pf-c-button.pf-m-disabled::after { --pf-c-button--BorderColor: var(--pf-c-button--disabled--BorderColor); }
.pf-c-button.pf-m-plain { color: var(--pf-c-button--m-plain--Color); }
.pf-c-button.pf-m-plain:hover, .pf-c-button.pf-m-plain.pf-m-hover { --pf-c-button--m-plain--Color: var(--pf-c-button--m-plain--hover--Color); }
.pf-c-button.pf-m-plain:active, .pf-c-button.pf-m-plain.pf-m-active { --pf-c-button--m-plain--Color: var(--pf-c-button--m-plain--active--Color); }
.pf-c-button.pf-m-plain:focus, .pf-c-button.pf-m-plain.pf-m-focus { --pf-c-button--m-plain--Color: var(--pf-c-button--m-plain--focus--Color); }
.pf-c-button.pf-m-plain:disabled, .pf-c-button.pf-m-plain.pf-m-disabled { --pf-c-button--m-plain--Color: var(--pf-c-button--m-plain--disabled--Color); background-color: var(--pf-c-button--m-plain--disabled--BackgroundColor); }

.pf-m-redhat-font .pf-c-button { --pf-c-button--FontWeight: var(--pf-global--FontWeight--normal); }

.pf-c-title.pf-m-3xl { font-size: 28px; line-height: 36.4px; }

    </style>
  </head>

  <body class="pf-m-redhat-font">
{% endraw %}
{% if ocp.banner_top_text is defined and ocp.banner_top_text | length > 0 %}
    <div style="background-color: {{ ocp.banner_top_color | default('#06C') }}; color: #fff; text-align: center; width: 100%; position: fixed;">
      <p>{{ ocp.banner_top_text }}</p>
    </div>
{% endif %}
{% raw %}
    <div class="pf-c-login">
      <div class="pf-c-login__container">
        <header class="pf-c-login__header">
          <img src="data:image/svg+xml;base64,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" alt="Red Hat OpenShift logo" class="pf-c-brand" />

        </header>
        <main class="pf-c-login__main">
          <header class="pf-c-login__main-header">
            <h1 class="pf-c-title pf-m-3xl">{{ .Locale.LogInToYourAccount }}</h1>
          </header>
          <div class="pf-c-login__main-body">
            <form class="pf-c-form" role="form" action="{{ .Action }}" method="POST">
              <input type="hidden" name="{{ .Names.Then }}" value="{{ .Values.Then }}">
              <input type="hidden" name="{{ .Names.CSRF }}" value="{{ .Values.CSRF }}">
              <div class="error-placeholder">
                {{ if .Error }}
                <p class="pf-c-form__helper-text pf-m-error">
                  <svg style="vertical-align:-0.125em" fill="currentColor" height="1em" width="1em" viewBox="0 0 512 512" aria-hidden="true" role="img" class="pf-m-error__icon">
                    <path d="M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zm-248 50c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z" transform=""></path>
                  </svg>
                  {{ .Error }}
                </p>
                {{ end }}
              </div>
              <div class="pf-c-form__group">
                <label class="pf-c-form__label" for="inputUsername">
                  <span class="pf-c-form__label-text">{{ .Locale.Username }}</span>
                  <span class="pf-c-form__label-required" aria-hidden="true">*</span>
                </label>
                <input type="text" class="pf-c-form-control" id="inputUsername" placeholder="" tabindex="1" autofocus="autofocus" type="text" name="{{ .Names.Username }}" value="{{ .Values.Username }}">
              </div>
              <div class="pf-c-form__group">
                <label class="pf-c-form__label" for="inputPassword">
                  <span class="pf-c-form__label-text">{{ .Locale.Password }}</span>
                  <span class="pf-c-form__label-required" aria-hidden="true">*</span>
                </label>
                <input type="password" class="pf-c-form-control" id="inputPassword" placeholder="" tabindex="2" type="password" name="{{ .Names.Password }}" value="">
              </div>
              <div class="pf-c-form__group pf-m-action">
                <button class="pf-c-button pf-m-primary pf-m-block" type="submit" tabindex="3">{{ .Locale.LogIn }}</button>
              </div>
            </form>
          </div>
        </main>
        <footer class="pf-c-login__footer">
{% endraw %}
{% if ocp.banner_login_text is defined and ocp.banner_login_text | length > 0 %}
    {{ ocp.banner_login_text }}
{% else %}
{%     raw %}
          <p>{{ .Locale.WelcomeTo }} Red Hat OpenShift</p>
{%     endraw %}
{% endif %}
{% raw %}
        </footer>
      </div>
    </div>
{% endraw %}
{% if ocp.banner_bottom_text is defined and ocp.banner_bottom_text | length > 0 %}
    <div style="background-color: {{ ocp.banner_bottom_color | default('#06C') }}; color: #fff; text-align: center; width: 100%; position: fixed; bottom: 0;">
      <p>{{ ocp.banner_bottom_text }}</p>
    </div>
{% endif %}
{% raw %}
  </body>
</html>
{% endraw %}
