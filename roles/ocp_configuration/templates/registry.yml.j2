apiVersion: machineconfiguration.openshift.io/v1
kind: MachineConfig
metadata:
  labels:
    machineconfiguration.openshift.io/role: {{ ocp.registry.0 }}
  name: 99-{{ ocp.registry.0 }}-registry-{{ ocp.registry.1.name }}
spec:
  config:
    ignition:
      version: 3.2.0
    storage:
      files:
        - contents:
            compression: ""
            source: data:,{{ lookup('template', 'registry.conf.j2') | urlencode }}
          mode: 0644
          overwrite: true
          path: /etc/containers/registries.d/{{ ocp.registry.1.name }}.conf
