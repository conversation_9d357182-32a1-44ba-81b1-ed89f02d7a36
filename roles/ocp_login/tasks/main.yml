# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-06-14 <PERSON>, <PERSON>                                          #
#               2023-06-14 <PERSON><PERSON>, <PERSON> D                                         #
#               2023-06-15 <PERSON>, <PERSON> J                                          #
#               2023-06-15 <PERSON><PERSON>, <PERSON> D                                         #
#               2023-06-20 Sam<PERSON>, <PERSON> D                                         #
#               2023-06-21 Sam<PERSON>, <PERSON> D                                         #
#               2023-07-19 Sam<PERSON>, <PERSON> D                                         #
#               2023-10-01 espy                                                   #
#               2023-10-12 Sam<PERSON>, <PERSON>                                           #
#               2023-10-25 espy                                                   #
#               2023-11-01 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-06 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-29 <PERSON><PERSON>, <PERSON>                                           #
#               2023-12-11 espy                                                   #
#               2023-12-12 espy                                                   #
#               2023-12-12 Sam<PERSON>, Landon                                           #
#               2024-02-15 espy                                                   #
#               2024-02-19 espy                                                   #
#               2024-12-19 Sam<PERSON>, <PERSON>                                           #
#               2025-01-07 <PERSON><PERSON>, <PERSON>                                           #
#               2025-01-20 <PERSON>, <PERSON> J                                          #
#               2025-01-24 Sam<PERSON>, <PERSON>                                           #
#               2025-01-30 espy                                                   #
# Create Date:  2023-06-14                                                        #
# Author:       <PERSON><PERSON>, <PERSON>                                                    #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Validate OpenShift host value
  ansible.builtin.assert:
    that: ocp.cluster.host is regex("^https.*:\d+")
    fail_msg: "The ocp.cluster.host value must be in the format https://<host name>:port"

- name: Create ~/.kube directory
  ansible.builtin.file:
    path: ~/.kube
    state: directory
    mode: '0750'

- name: Check if existing .kube/config works
  kubernetes.core.k8s_info:
    api_version: v1
    kind: Node
    host: "{{ ocp.cluster.host }}"
  register: k8s_status_default
  failed_when: false

- name: If default kubeconfig file doesn't work
  when: k8s_status_default.resources is not defined
  block:
    - name: Check if provided kubeconfig file is valid
      kubernetes.core.k8s_info:
        api_version: v1
        kind: Node
        kubeconfig: "{{ ocp.kubeconfig_file }}"
        host: "{{ ocp.cluster.host }}"
      register: k8s_status_kubeconfig
      failed_when: false

    - name: Copy provided kubeconfig file to default kubeconfig location
      ansible.builtin.copy:
        src: "{{ ocp.kubeconfig_file }}"
        dest: ~/.kube/config
        mode: '0600'
      when: k8s_status_kubeconfig.resources is defined

    - name: If provided kubeconfig file doesn't work
      when: k8s_status_kubeconfig.resources is not defined
      block:
        - name: Validate OpenShift host value
          ansible.builtin.assert:
            that:
              - ocp.cluster.username or ocp.cluster.api_key
              - ocp.cluster.password or ocp.cluster.api_key
            fail_msg: "All required login credentials are not defined"
          register: validate_credentials
          failed_when: false

        - name: Generate kubeconfig file
          ansible.builtin.command:
            cmd: |
              oc login
              --server={{ ocp.cluster.host }}
              {{ '--insecure-skip-tls-verify' if not ocp.validate_certs }}
              {{ '--token=' + ocp.cluster.api_key if ocp.cluster.api_key }}
              {{ '--username=' + ocp.cluster.username if ocp.cluster.username }}
              {{ '--password=' + ocp.cluster.password if ocp.cluster.password }}
          changed_when: true
          register: k8s_status_auth
          ignore_errors: true
          when: ocp.cluster.api_key is defined or (ocp.cluster.username is defined and ocp.cluster.password is defined)

        - name: Fail if no method of logging in worked and display all values
          ansible.builtin.fail:
            msg: |
              Unable to log into cluster with provided methods
              Server: {{ ocp.cluster.host }}
              Existing kubeconfig file returned: {{ k8s_status_default.msg if k8s_status_default.failed}}
              {{ "Kubeconfig file was provided: " + ocp.kubeconfig_file if ocp.kubeconfig_file else "There was no provided kubeconfig file" }}
              Provided kubeconfig file [{{ ocp.kubeconfig_file }}] returned: {{ k8s_status_kubeconfig.msg }}
              Provided Credentials: {{ validate_credentials.msg if validate_credentials.failed else "OK" }}
              Login attempt with credentials returned: {{ (k8s_status_auth.stderr is defined) | ternary(
                k8s_status_auth.stderr, k8s_status_auth.stdout | default("Skipped") ) }}
          when: (k8s_status_auth.skipped is defined and k8s_status_auth.skipped) or
                (k8s_status_auth.failed is defined and k8s_status_auth.failed)

        - name: Get contents of kubeconfig
          ansible.builtin.slurp:
            src: ~/.kube/config
          register: encoded_kubeconfig

        - name: Remove old certificate authority from kubeconfig
          ansible.builtin.lineinfile:
            path: ~/.kube/config
            regexp: '^    certificate-authority-data:'
            state: absent

        - name: Add certificate authority to kubeconfig
          ansible.builtin.lineinfile:
            path: ~/.kube/config
            insertbefore: '^    server:'
            line: "    certificate-authority-data: {{ new_cert_data }}"
          vars:
            kubeconfig_yaml: "{{ (encoded_kubeconfig.content) | b64decode | from_yaml }}"
            encoded_cert: "{{ kubeconfig_yaml.clusters[0].cluster['certificate-authority-data'] | default('') }}"
            full_cert_chain: "{{ encoded_cert | b64decode | default('') }}"
            cert_regex: '-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----'
            kubeconfig_certs: "{{ full_cert_chain | regex_findall(cert_regex, multiline=True) }}"
            provided_certs: "{{ lookup('file', ocp.ca_cert) | regex_findall(cert_regex, multiline=True) }}"
            combined_cert: "{{ kubeconfig_certs | union(provided_certs) | unique | join('\n') }}"
            new_cert_data: "{{ combined_cert | b64encode }}"
