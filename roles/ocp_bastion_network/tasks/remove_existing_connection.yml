# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        remove_existing_connection.yml                                    #
# Version:                                                                        #
#               2023-10-01 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-05-28 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-01                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: remove_existing_connection | Get name of connection using baremetal ip
  ansible.builtin.shell:
    cmd: set -o pipefail && nmcli -g IP4.ADDRESS,GENERAL.CONNECTION device show | grep {{ ip_address }} -A 1 | tail -n 1
  register: ip_address_connection
  changed_when: false
  failed_when: false

- name: remove_existing_connection | Get existing connection name attached to provided interface
  ansible.builtin.shell:
    cmd: "set -o pipefail && nmcli -t -f NAME,DEVICE con | grep :{{ interface_name }} | awk -F: '{print $1}'"
  register: interface_name_connection
  changed_when: false
  failed_when: false

- name: remove_existing_connection | Remove nmcli connection {{ ip_address_connection.stdout | default("None") }}
  ansible.builtin.command:
    cmd: nmcli con del "{{ ip_address_connection.stdout }}"
  changed_when: true
  when: ip_address_connection.stdout

- name: remove_existing_connection | Remove nmcli connection {{ interface_name_connection.stdout | default("None") }}
  ansible.builtin.command:
    cmd: nmcli con del "{{ interface_name_connection.stdout }}"
  changed_when: true
  when: interface_name_connection.stdout and interface_name_connection.stdout  != ip_address_connection.stdout

- name: remove_existing_connection | Remove nmcli connection {{ bridge_name | default("None") }}
  ansible.builtin.command:
    cmd: nmcli con del {{ bridge_name }}
    removes: "{{ rhel_8_file if ocp.rhel_major_version | int == 8 else rhel_9_file }}"
  when: bridge_name
  vars:
    rhel_8_file: /etc/sysconfig/network-scripts/ifcfg-{{ bridge_name }}
    rhel_9_file: /etc/NetworkManager/system-connections/{{ bridge_name }}.nmconnection
