# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-08-31 espy                                                   #
#               2023-10-01 espy                                                   #
#               2023-10-12 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-11-01 espy                                                   #
#               2024-01-25 espy                                                   #
#               2024-02-10 espy                                                   #
#               2024-03-04 espy                                                   #
#               2024-04-01 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-06-05 espy                                                   #
#               2024-06-07 <PERSON><PERSON><PERSON><PERSON>, <PERSON>                                    #
#               2024-11-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-08-31                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Root commands
  become: true
  become_user: root
  block:
    - name: Install nmstate
      ansible.builtin.yum:
        name: nmstate
        state: present

    - name: Remove existing connections with same interface name or ip address or bridge - baremetal_network
      ansible.builtin.include_tasks: remove_existing_connection.yml
      vars:
        ip_address: "{{ ocp.baremetal_network_bastion_ip }}"
        interface_name: "{{ ocp.bastion_baremetal_network_interface_name }}"
        bridge_name: "{{ ocp.baremetal_network_temp_bridge_name }}"

    - name: Remove existing connections with same interface name or ip address or bridge - provisioning_network
      ansible.builtin.include_tasks: remove_existing_connection.yml
      vars:
        ip_address: "{{ ocp.provisioning_network_bastion_ip }}"
        interface_name: "{{ ocp.bastion_provisioning_network_interface_name }}"
        bridge_name: "{{ ocp.provisioning_network_temp_bridge_name }}"
      when: ocp.setup_provisioning_network

    - name: Copy nmstate file
      ansible.builtin.copy:
        content: "{{ network_config | to_nice_yaml(indent=2) }}"
        dest: /etc/NetworkManager/conf.d/nmstate.yaml
        mode: "0644"

    - name: Replace quoted prefix length with non quoted Version
      ansible.builtin.replace:
        path: /etc/NetworkManager/conf.d/nmstate.yaml
        regexp: "'(\\d+)'"
        replace: "\\1"

    - name: Set network configuration per network config yaml
      ansible.builtin.command:
        cmd: nmstatectl apply /etc/NetworkManager/conf.d/nmstate.yaml
      changed_when: true

    - name: Add internal zone to interfaces
      ansible.builtin.command:
        cmd: nmcli con mod {{ item }} connection.zone internal
      loop: "{{ list_of_interfaces }}"
      changed_when: true

    - name: Delete unsused networks
      ansible.builtin.shell:
        cmd: |
         set -o pipefail
         nmcli con del $(nmcli --get-values UUID,DEVICE,STATE,ACTIVE con show | grep -v :activated: | awk -F: '{print $1}')
      register: delete_connection_command
      changed_when: delete_connection_command.rc == 0
      failed_when: delete_connection_command.rc != 0 and delete_connection_command.rc != 10

    - name: Disable ipv6 for every connection with IPV6
      ansible.builtin.shell:
        cmd: |
          set -o pipefail
          for connection in $(nmcli --get-values UUID,SLAVE,FILENAME con show | grep -v ':team:\|:bond:\|:bridge:\|lo.nmconnection' | awk -F: '{print $1}');
            do nmcli con mod $connection ipv6.method disabled;
          done
      changed_when: true

    - name: Bring up all connections
      ansible.builtin.shell:
        cmd: |
          set -o pipefail
          for connection in $(nmcli --get-values UUID con show);
            do nmcli con up $connection;
          done
      changed_when: true

    - name: Restart firewalld
      ansible.builtin.systemd:
        name: firewalld
        state: restarted
