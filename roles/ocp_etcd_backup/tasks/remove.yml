# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        remove.yml                                                        #
# Version:                                                                        #
#               2024-09-11 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-09-11                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: remove | Login to OpenShift
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_login

- name: remove | Remove Helm Chart etcd-backup
  kubernetes.core.helm:
    release_name: etcd-backup
    release_namespace: "{{ namespace }}"
    release_state: absent

- name: remove | Remove namespace "{{ namespace }}"
  kubernetes.core.k8s:
    kind: Namespace
    name: "{{ namespace }}"
    state: absent
