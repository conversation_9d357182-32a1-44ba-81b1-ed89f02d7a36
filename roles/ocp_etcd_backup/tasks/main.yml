# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-06-19 espy                                                   #
#               2023-10-25 espy                                                   #
#               2024-09-03 espy                                                   #
#               2024-09-11 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-06-19                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Login to OpenShift
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_login

- name: "Create namespace: {{ namespace }}"
  kubernetes.core.k8s:
    kind: Namespace
    name: "{{ namespace }}"
    state: present

- name: Deploy etcd-backup
  kubernetes.core.helm:
    chart_ref: "{{ role_path }}/files/chart"
    release_name: etcd-backup
    release_namespace: "{{ namespace }}"
    release_state: present
    values: "{{ ocp.etcd_backup_settings }}"
    wait: "{{ wait_for_helm }}"
    wait_timeout: "{{ wait_timeout }}"
