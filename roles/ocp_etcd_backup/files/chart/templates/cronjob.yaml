kind: CronJob
apiVersion: batch/v1
metadata:
  name: {{ .Release.Name }}-cj
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Values.appLabel }}
spec:
  schedule: "{{ .Values.schedule }}"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 5
  failedJobsHistoryLimit: 5
  jobTemplate:
    metadata:
      labels:
        app: {{ .Values.appLabel }}
    spec:
      backoffLimit: 0
      template:
        metadata:
          labels:
            app: {{ .Values.appLabel }}
        spec:
          containers:
            - name: backup
              image: "registry.redhat.io/openshift4/ose-cli"
              command: ["/bin/bash", "/scripts/backup_script.sh"]
              volumeMounts:
                - name: script-volume
                  mountPath: /scripts
          volumes:
            - name: script-volume
              configMap:
                name: {{ .Release.Name }}-script
          restartPolicy: "Never"
          terminationGracePeriodSeconds: 30
          activeDeadlineSeconds: 500
          dnsPolicy: "ClusterFirst"
          serviceAccountName: {{ .Release.Name }}-sa
