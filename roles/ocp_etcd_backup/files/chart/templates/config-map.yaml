kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ .Release.Name }}-script
data:
  backup_script.sh: |
    #!/bin/sh
    convert_lines_to_array() {
        local lines=$1
        local -n output_array=$2
        IFS=$'\n' read -d '' -r -a output_array <<< "$lines"
    }
    echo "Convert function defined"
    list_of_nodes=$(oc get no -l node-role.kubernetes.io/master --no-headers -o name)
    echo "list_of_nodes defined"
    convert_lines_to_array "$list_of_nodes" array_of_nodes
    echo "array_of_nodes defined"
    number_of_nodes=${#array_of_nodes[@]}
    echo "number_of_nodes is $number_of_nodes"
    random_index=$((RANDOM % number_of_nodes))
    echo "random_index is $random_index"
    random_node=${array_of_nodes[$random_index]}
    echo "random_node is $random_node"
    backup_command='chroot /host sudo -E /usr/local/bin/cluster-backup.sh /home/<USER>/backup/'
    echo "backup_command is $backup_command"
    delete_old_backup_command='chroot /host sudo -E find /home/<USER>/backup/ -type f -mmin +"1" -delete'
    echo "delete_old_backup_command is $delete_old_backup_command"
    NEXUS_USER='{{ .Values.nexus.username }}'
    NEXUS_TOKEN='{{ .Values.nexus.token }}'
    NEXUS_REPOSITORY='{{ .Values.nexus.repository }}'
    echo "NEXUS_REPOSITORY is $NEXUS_REPOSITORY"
    NEXUS_FOLDER='{{ .Values.nexus.folder }}'
    echo "NEXUS_FOLDER is $NEXUS_FOLDER"
    CLUSTER_NAME=$(echo $random_node | cut -d '.' -f 2- | cut -d '.' -f 1)
    echo "CLUSTER_NAME is $CLUSTER_NAME"
    backup_to_nexus_command_db="curl --cacert /host/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem -u $NEXUS_USER:$NEXUS_TOKEN --upload-file /host/home/<USER>/backup/*.db $NEXUS_REPOSITORY/$NEXUS_FOLDER/${CLUSTER_NAME}-etcd-backup.db"
    backup_to_nexus_command_tar="curl --cacert /host/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem -u $NEXUS_USER:$NEXUS_TOKEN --upload-file /host/home/<USER>/backup/*.tar.gz $NEXUS_REPOSITORY/$NEXUS_FOLDER/${CLUSTER_NAME}-etcd-backup.tar.gz"
    set -e
    echo "oc debug ${random_node} --to-namespace={{ .Release.Namespace }} -- bash -c \"${backup_command}\""
    echo "Backing up to node"
    oc debug ${random_node} --to-namespace={{ .Release.Namespace }} -- bash -c "${backup_command}"
    echo "Deleting old backups from node"
    oc debug ${random_node} --to-namespace={{ .Release.Namespace }} -- bash -c "${delete_old_backup_command}"
    echo "Backing up db to Nexux"
    oc debug ${random_node} --to-namespace={{ .Release.Namespace }} -- bash -c "${backup_to_nexus_command_db}"
    echo "Backing up tar to Nexux"
    oc debug ${random_node} --to-namespace={{ .Release.Namespace }} -- bash -c "${backup_to_nexus_command_tar}"
    echo "Backup succesfully completed"
