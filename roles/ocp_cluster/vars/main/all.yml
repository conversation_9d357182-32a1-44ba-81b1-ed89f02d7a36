# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-05-09 <PERSON><PERSON><PERSON>, Wayne                                      #
#               2023-05-15 espy                                                   #
#               2023-07-03 espy                                                   #
#               2023-08-01 espy                                                   #
#               2023-08-23 espy                                                   #
#               2023-08-28 espy                                                   #
#               2024-06-19 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-05-09                                                        #
# Author:       Casa<PERSON><PERSON>, Wayne                                                 #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

baremetal_master_nodes_hostnames: >-
  {{
    baremetal_master_nodes |
    map(attribute='abstract') |
    map(attribute='name') |
    map('regex_replace', '$', '.' + ocp.hostname)
  }}
baremetal_worker_nodes_hostnames: >-
  {{
    baremetal_worker_nodes |
    map(attribute='abstract') |
    map(attribute='name') |
    map('regex_replace', '$', '.' + ocp.hostname)
  }}
bootstrap_node_ssh_args: >-
  -o ProxyCommand="ssh -i ~/.ssh/id_rsa -W %h:%p
  {{ hostvars[groups["bastion"][0]].ocp.ocp_user }}@{{ hostvars[groups["bastion"][0]].ansible_host }}"
custom_boot_disk_config: >-
  {{
    ocp.nodes_tpm_encryption or
    (ocp.nodes_tang_servers | length > 0) or
    (ocp.nodes_mirror_devices and ocp.nodes_mirror_devices | length > 0)
  }}
baremetal_master_nodes: "{{ ocp.baremetal_list | selectattr('abstract.purpose', 'equalto', 'master') | list }}"
baremetal_network_prefix: "{{ ocp.baremetal_network_cidr | lmco.openshift.cidr_prefix }}"
baremetal_worker_nodes: "{{ ocp.baremetal_list | selectattr('abstract.purpose', 'equalto', 'worker') | list }}"
bootstrap_image_url: "{{ ocp.galaxy_nexus_repo }}/{{ coreos_qemu_name }}?sha256={{ coreos_qemu_uncompressed_sha256 }}"
butane_files_have_changed: false
config_namespace: "openshift-config"
disconnected_registry_pull_secret: '{"auths":{"{{ ocp.registry_hostname }}":{"auth":"{{ registry_auth_value }}","email":""}}}'
okdmirrorbug: true
openshift_base_version: "{{ ocp.openshift_version | split('.') | slice(2) | first | join('.') }}"
baremetal_registry_pull_secret: "{{ disconnected_registry_pull_secret }}"
aws_registry_pull_secret: '{"auths":{"quay-io.{{ ocp.galaxy_nexus_hostname }}":{"auth":"{{ registry_auth_value }}","email":""}}}'
pull_secret_text: "{{ aws_registry_pull_secret if ocp.platform == 'aws' else baremetal_registry_pull_secret }}"
registry_auth_value: "{{ (ocp.galaxy_nexus_username + ':' + ocp.galaxy_nexus_password) | b64encode }}"
wildcard_cert_name: "wildcard-tls-cert"
role_required_vars:
  - ocp.baremetal_list
  - ocp.bmc_api
  - ocp.galaxy_nexus_password
  - ocp.openshift_cluster
  - ocp.openshift_install_file
  - ocp.openshift_install_path
