# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-01 espy                                                   #
#               2023-10-04 Sam<PERSON>, Landon                                           #
#               2023-10-25 espy                                                   #
#               2023-11-01 espy                                                   #
#               2024-01-25 espy                                                   #
#               2024-01-26 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-02-10 espy                                                   #
#               2024-02-11 espy                                                   #
#               2024-03-04 espy                                                   #
#               2024-04-01 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-01                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

default_baremetal_bridge:
  name: "{{ ocp.baremetal_network_temp_bridge_name }}"
  type: linux-bridge
  state: up
  bridge:
    options:
      stp:
        enabled: false
    port:
      - name: "{{ ocp.bastion_baremetal_network_interface_name }}"
        stp-hairpin-mode: false
  ipv4:
    enabled: true
    address:
      - ip: "{{ ocp.baremetal_network_bastion_ip }}"
        prefix-length: "{{ ocp.baremetal_network_prefix_length }}"
    dhcp: false
  ipv6:
    enabled: false
default_provisioning_bridge:
  name: "{{ ocp.provisioning_network_temp_bridge_name }}"
  state: up
  type: linux-bridge
  bridge:
    options:
      stp:
        enabled: false
    port:
      - name: "{{ ocp.bastion_provisioning_network_interface_name }}"
        stp-hairpin-mode: false
  ipv4:
    enabled: false
  ipv6:
    enabled: false
default_baremetal_connection:
  name: "{{ ocp.bastion_baremetal_network_interface_name }}"
  state: up
  ipv4:
    enabled: false
  ipv6:
    enabled: false
default_provisioning_connection:
  name: "{{ ocp.bastion_provisioning_network_interface_name }}"
  state: up
  ipv4:
    enabled: false
    dhcp: false
  ipv6:
    enabled: false
default_network_config_interfaces: >-
  {{
    [default_baremetal_bridge, default_baremetal_connection, default_provisioning_bridge, default_provisioning_connection] if
    ocp.setup_provisioning_network else
    [default_baremetal_bridge, default_baremetal_connection]
  }}

network_config:
  interfaces: "{{ default_network_config_interfaces | lmco.openshift.merge_nmstates(ocp.bastion.network.interfaces) }}"
  dns-resolver:
    config:
      search:
        - "{{ ocp.hostname }}"

list_of_interfaces: >-
  {{
    [ocp.baremetal_network_temp_bridge_name, ocp.provisioning_network_temp_bridge_name] if
    ocp.setup_provisioning_network else
    [ocp.baremetal_network_temp_bridge_name]
  }}
