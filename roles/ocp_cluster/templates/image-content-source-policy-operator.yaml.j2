apiVersion: operator.openshift.io/v1alpha1
kind: ImageContentSourcePolicy
metadata:
    name: all
spec:
    repositoryDigestMirrors:
    -   mirrors:
        - registry-redhat-io.{{ ocp.galaxy_nexus_hostname }}
        source: registry.redhat.io
    -   mirrors:
        - quay-io.{{ ocp.galaxy_nexus_hostname }}
        source: quay.io
    -   mirrors:
        - registry-connect-redhat-com.{{ ocp.galaxy_nexus_hostname }}
        source: registry.connect.redhat.com
    -   mirrors:
        - registry-ci-openshift-org.{{ ocp.galaxy_nexus_hostname }}
        source: registry.ci.openshift.org
    -   mirrors:
        - registry-1-docker-io.{{ ocp.galaxy_nexus_hostname }}
        source: registry-1.docker.io
    -   mirrors:
        - registry-1-docker-io.{{ ocp.galaxy_nexus_hostname }}
        source: docker.io
    -   mirrors:
        - icr-io.{{ ocp.galaxy_nexus_hostname }}
        source: icr.io
    -   mirrors:
        - k8s-gcr-io.{{ ocp.galaxy_nexus_hostname }}
        source: k8s.gcr.io
    -   mirrors:
        - gcr-io.{{ ocp.galaxy_nexus_hostname }}
        source: gcr.io
    -   mirrors:
        - registry-access-redhat-com.{{ ocp.galaxy_nexus_hostname }}
        source: registry.access.redhat.com
    -   mirrors:
        - cp-stg-icr-io.{{ ocp.galaxy_nexus_hostname }}
        source: cp.stg.icr.io
    -   mirrors:
        - ghcr-io.{{ ocp.galaxy_nexus_hostname }}
        source: ghcr.io
    -   mirrors:
        - registry-gitlab-com.{{ ocp.galaxy_nexus_hostname }}
        source: registry.gitlab.com
    -   mirrors:
        - registry-k8s-io.{{ ocp.galaxy_nexus_hostname }}
        source: registry.k8s.io
    -   mirrors:
        - nvcr-io.{{ ocp.galaxy_nexus_hostname }}
        source: nvcr.io
    -   mirrors:
        - registry.{{ ocp.galaxy_nexus_hostname }}
        source: registry
