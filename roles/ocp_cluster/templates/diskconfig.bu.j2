#jinja2: trim_blocks:False
{# j2lint: disable=jinja-statements-delimiter #}
variant: openshift
version: {{ openshift_base_version }}.0
metadata:
  name: {{ machine_role }}-storage
  labels:
    machineconfiguration.openshift.io/role: {{ machine_role }}
boot_device:
  layout: x86_64
{%- if ocp.nodes_tpm_encryption or ( ocp.nodes_tang_servers | length > 0 ) %}
  luks:
{%-     if ocp.nodes_tpm_encryption %}
    tpm2: true
{%-     endif %}
{%-     if ocp.nodes_tang_servers | length > 0 %}
{%-         for tang_server in ocp.nodes_tang_servers %}
    tang:
      - url: {{ tang_server.url }}
        thumbprint: {{ tang_server.thumbprint }}
{%-         endfor %}
    threshold: {{ ocp.nodes_luks_threshold }}
{%-     endif %}
{%- endif %}
{%- if ocp.nodes_mirror_devices and ocp.nodes_mirror_devices | length > 0 %}
  mirror:
    devices:
{%-     for device in ocp.nodes_mirror_devices %}
      - {{ device }}
{%-     endfor %}
{%- endif %}
openshift:
  fips: {{ ocp.fips_enabled }}
