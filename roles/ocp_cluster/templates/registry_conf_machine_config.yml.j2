apiVersion: machineconfiguration.openshift.io/v1
kind: MachineConfig
metadata:
  labels:
    machineconfiguration.openshift.io/role: {{ role }}
  name: 99-helm-chart-disconnected-images-{{ role }}
spec:
  config:
    ignition:
      version: 3.2.0
    storage:
      files:
        - contents:
            source: data:text/plain;charset=utf-8;base64,{{ b64_encoded_conf_file }}
          filesystem: root
          mode: 420
          path: /etc/containers/registries.conf.d/99-helm-chart-disconnected-images-{{ role }}.conf
          overwrite: true
