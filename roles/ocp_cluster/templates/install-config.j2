{# j2lint: disable=single-statement-per-line #}
apiVersion: v1
baseDomain: "{{ ocp.hostname.split('.')[1:] | join('.') }}"
{% if ocp.setup_proxy and ocp.http_proxy != "" and ocp.https_proxy != "" and (no_proxy | length) > 0 %}
#-------------------------------------------#
# LMCO Proxy Servers                        #
#-------------------------------------------#
proxy:
  httpProxy: "{{ ocp.http_proxy }}"
  httpsProxy: "{{ ocp.https_proxy }}"
  noProxy: "{{ no_proxy | join(',') }},.{{ ocp.hostname.split('.')[1:] | join('.') }},{{ ocp.provisioning_network_cidr }},{{ ocp.baremetal_network_cidr }}{% for node in ocp.baremetal_list %},{{ node.bmc.ipv4_address }}{% endfor %}"
{% endif %}

#-------------------------------------------#
# LMCO Certificate Chain                    #
#-------------------------------------------#
additionalTrustBundle: |
{% if ocp.platform == 'baremetal' %}
{{ lookup('file', ocp.registry_crt_file ) | indent(width=2, first=True) }}
{% endif %}
{{ lookup('file', ocp.ca_cert) | indent(width=2, first=True) }}

#-------------------------------------------#
# Cluster info                              #
#-------------------------------------------#
metadata:
  name: {{ ocp.openshift_cluster }}
fips: {{ "true" if ocp.distro == 'ocp' and ocp.fips_enabled else "false" }}

#-------------------------------------------#
# Control and compute plane node config.    #
#-------------------------------------------#
{% if ocp.platform == 'aws' %}
publish: Internal
credentialsMode: Mint
controlPlane:
  architecture: amd64
  hyperthreading: Enabled
  name: master
  platform:
    aws:
      zones:
        - {{ ocp.aws.az }}
      rootVolume:
        iops: 2000
        size: 200
        type: io1
      type: m6i.4xlarge
  replicas: {{ ocp.aws.master_count }}

compute:
  - architecture: amd64
    hyperthreading: Enabled
    name: worker
    platform:
      aws:
        zones:
          - {{ ocp.aws.az }}
        rootVolume:
          iops: 2000
          size: 200
          type: io1
        type: m6i.4xlarge
    replicas: {{ ocp.aws.worker_count }}

platform:
  aws:
    region: {{ ocp.aws.region }}
    propagateUserTags: true
    userTags:  # number of user tags cannot be more than 8
      lmprogram: galaxy
      galaxy-cluster: {{ ocp.openshift_cluster }}
    subnets:
      - {{ ocp.aws.baremetal_subnet_id }}
    lbType: NLB
    preserveBootstrapIgnition: true
{%     if ocp.aws.galaxy_openshift_ipi_role is defined %}
    hostedZoneRole: {{ ocp.aws.galaxy_openshift_ipi_role }}
{%     endif %}
{% endif %}

{% if ocp.platform == 'baremetal' %}
controlPlane:
  name: master
  replicas: {{ ocp.baremetal_list | selectattr('abstract.purpose', 'equalto', 'master') | list | length }}
  platform:
    baremetal: {}
compute:
- name: worker
  replicas: {{ ocp.baremetal_list | selectattr('abstract.purpose', 'equalto', 'worker') | list | length }}

platform:
  baremetal:
    apiVIP: "{{ ocp.baremetal_network_openshift_api_vip }}"
    ingressVIP: "{{ ocp.baremetal_network_openshift_ingress_vip }}"
    externalBridge: {{ ocp.baremetal_network_temp_bridge_name }}
{%     if ocp.bmc_api == 'redfish-virtualmedia' %}
    provisioningNetwork: "Disabled"
{%     else %}
    provisioningBridge: {{ ocp.provisioning_network_temp_bridge_name }}
    provisioningNetworkCIDR: "{{ ocp.provisioning_network_cidr }}"
    provisioningNetwork: "Managed"
{%     endif %}
    bootstrapExternalStaticIP: "{{ ocp.baremetal_network_bootstrap_ip }}"
    BootstrapExternalStaticDNS: "{{ ocp.baremetal_network_dns_ip }}"
    bootstrapExternalStaticGateway: "{{ ocp.baremetal_network_gateway_ip }}"
{%     if ocp.setup_coreos_cache %}
    bootstrapOSImage: {{ bootstrap_image_url }}
{%     endif %}
    hosts:
{%     set master_index = [0] %}
{%     set worker_index = [0] %}
{%     for node in ocp.baremetal_list %}
    - name: {{ node.abstract.name }}.{{ ocp.hostname }}
      role: {{ node.abstract.purpose }}
      bmc:
{%         if ocp.bmc_api == 'redfish' %}
        address: "redfish://{{ node.bmc.ipv4_address }}/redfish/v1/Systems/{{ node.bmc.system_id }}"
{%         endif %}
{%         if ocp.bmc_api == 'redfish-virtualmedia' %}
        address: "redfish-virtualmedia://{{ node.bmc.ipv4_address }}.{{ ocp.galaxy_proxy }}:{{ ocp.galaxy_proxy_port }}/redfish/v1/Systems/{{ node.bmc.system_id }}"
{%         endif %}
{%         if ocp.bmc_api == 'ipmi' %}
        address: "ipmi://{{ node.bmc.ipv4_address }}"
{%         endif %}
        username: "{{ node.bmc.username }}"
        password: "{{ node.bmc.password }}"
        disableCertificateVerification: True
{%         if ocp.bmc_api != 'redfish-virtualmedia' %}
      bootMACAddress: "{{ node.openshift.provisioning_network_mac_address }}"
{%         else %}
      bootMACAddress: "{{ node.bmc | lmco.openshift.get_default_mac }}"
{%         endif %}
      rootDeviceHints:
        deviceName: "{{ node.openshift.coreos_installation_device }}"
{%         if node.abstract.purpose == 'master' %}
{%             set node_host_ip = master_index[-1] + ocp.master_nodes_starting_ip %}
{%             set node_ip = baremetal_network_prefix + '.' + ( node_host_ip | string ) %}
{%             set _ = master_index.append(master_index[-1] + 1) %}
{%         elif node.abstract.purpose == 'worker' %}
{%             set node_host_ip = worker_index[-1] + ocp.worker_nodes_starting_ip %}
{%             set node_ip = baremetal_network_prefix + '.' + ( node_host_ip | string ) %}
{%             set _ = worker_index.append(worker_index[-1] + 1) %}
{%         endif %}
{%         set node_mac_address = node.openshift.baremetal_network_mac_address if node.openshift.baremetal_network_mac_address is defined else none %}
{%         set node_baremetal_interface_name = node.openshift.baremetal_interface_name if node.openshift.baremetal_interface_name is defined else 'baremetal' %}
{%         set default_baremetal_network_config_interfaces_baremetal =
{
  'name': node_baremetal_interface_name,
  'type': 'ethernet',
  'state': 'up',
  'ipv4':
  {
    'dhcp': false,
    'address':
    [
      {
        'ip': node_ip,
        'prefix-length': (ocp.baremetal_network_cidr.split('/')[1] | int)
      }
    ],
    'enabled': true
  }
}
%}
{%         if node_mac_address is not none %}
{%             set default_baremetal_network_config_interfaces_baremetal = default_baremetal_network_config_interfaces_baremetal | combine({"identifier": 'mac-address'}) %}
{%             set default_baremetal_network_config_interfaces_baremetal = default_baremetal_network_config_interfaces_baremetal | combine({"mac-address": node_mac_address}) %}
{%         endif %}
{%         set default_baremetal_network_config_dns_resolver =
{
  'config':
  {
    'server':[ocp.baremetal_network_dns_ip],
    'search':[ocp.hostname]
  }
},
%}
{%         set default_baremetal_network_config_routes =
{
  'config':
  [
    {
      'destination': '0.0.0.0/0',
      'next-hop-address': ocp.baremetal_network_gateway_ip,
      'next-hop-interface': node_baremetal_interface_name
    }
  ]
}
%}
{%         set default_baremetal_network_config =
{
  'interfaces': [ default_baremetal_network_config_interfaces_baremetal ],
  'dns-resolver': default_baremetal_network_config_dns_resolver,
  'routes': default_baremetal_network_config_routes
}
%}
      networkConfig:
        interfaces:
          {{ default_baremetal_network_config.interfaces | lmco.openshift.merge_nmstates((node.network_config | default({})).interfaces | default([])) | to_yaml | indent(width=10, first=False) }}
        dns-resolver:
          {{ default_baremetal_network_config['dns-resolver'] | combine((node.network_config | default({}))['dns-resolver'] | default({})) | to_yaml | indent(width=10, first=False) }}
        routes:
          {{ default_baremetal_network_config.routes | combine((node.network_config | default({})).routes | default({})) | to_yaml | indent(width=10, first=False) }}
{%     endfor %}
{% endif %}

#-------------------------------------------#
# Network Configurations.                   #
# Either OpenShiftSDN or OVNKubernetes.     #
#-------------------------------------------#
networking:
  clusterNetwork:
  - cidr: **********/14
    hostPrefix: 23
  machineNetwork:
  - cidr: {{ ocp.baremetal_network_cidr }}
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16

#-------------------------------------------#
# Mirror registry                           #
#-------------------------------------------#
imageContentSources:
{% if ocp.platform == 'baremetal' %}
{%     for mirror in ocp_mirrors %}
  - {{ mirror | to_nice_yaml | indent(width=4, first=False) }}
{%     endfor %}
{%     if okdmirrorbug %}
  - mirrors:
    - {{ ocp.registry_hostname }}/openshift/release-images
    source: quay.io/openshift/okd
{%     endif %}
{% endif %}
{% if ocp.platform == 'aws' %}
  - mirrors:
    - quay-io.{{ ocp.galaxy_nexus_hostname }}
    source: quay.io
{% endif %}
#-------------------------------------------#
# Keys and access                           #
#-------------------------------------------#
pullSecret: '{{ pull_secret_text | regex_replace("'", '"') }}'
sshKey: '{{ ssh_key.content | b64decode | trim }}'
