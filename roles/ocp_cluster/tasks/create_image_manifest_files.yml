# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_image_manifest_files.yml                                   #
# Version:                                                                        #
#               2023-12-20 espy                                                   #
#               2024-04-27 espy                                                   #
#               2024-05-08 espy                                                   #
#               2024-08-12 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-12-20                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_image_manifest_files | Get ICSP file name
  ansible.builtin.find:
    paths: "{{ ocp.openshift_install_path }}/nexus-data/oc-mirror-workspace"
    patterns: imageContentSourcePolicy.*
    use_regex: true
    recurse: true
  register: image_content_source_policy

- name: create_image_manifest_files | Get all created ICSPs
  ansible.builtin.slurp:
    src: "{{ icsp_file.path }}"
  vars:
    icsp_file: "{{ image_content_source_policy.files | first }}"
  register: slurped_icsp_file

- name: create_image_manifest_files | Create operator ICSP file in manifests folder
  ansible.builtin.copy:
    content: "{{ new_operator_section | to_nice_yaml | regex_replace(ocp.default_registry_url, ocp.registry_hostname) }}"
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/image-content-source-policy-operator.yaml"
    mode: "0640"
  vars:
    original_icsp_contents: "{{ slurped_icsp_file.content | b64decode | from_yaml_all }}"
    operator_sections_found: "{{ original_icsp_contents | selectattr('metadata', 'defined') | selectattr('metadata.name', 'equalto', 'operator-0') }}"
    operator_section: "{{ operator_sections_found | list | first }}"
    new_operator_section: "{{ operator_section | combine({'metadata': {'name': 'all-operators'}}) }}"
  when: operator_sections_found

- name: create_image_manifest_files | Create openshift ICSP file in manifests folder
  ansible.builtin.copy:
    content: "{{ new_openshift_section | to_nice_yaml | regex_replace(ocp.default_registry_url, ocp.registry_hostname) }}"
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/image-content-source-policy-openshift.yaml"
    mode: "0640"
  vars:
    original_icsp_contents: "{{ slurped_icsp_file.content | b64decode | from_yaml_all }}"
    openshift_section: "{{ original_icsp_contents | selectattr('metadata', 'defined') | selectattr('metadata.name', 'equalto', 'release-0') | list | first }}"
    new_openshift_section: "{{ openshift_section | combine({'metadata': {'name': 'openshift'}}) }}"

- name: create_image_manifest_files | Create helm images container conf machine config contents
  ansible.builtin.set_fact:
    list_of_registry_blocks: "{{ list_of_registry_blocks | default([]) + [registry_block] }}"
  vars:
    original_icsp_contents: "{{ slurped_icsp_file.content | b64decode | from_yaml_all }}"
    operator_section: "{{ original_icsp_contents | selectattr('metadata', 'defined') | selectattr('metadata.name', 'equalto', 'generic-0') | list | first }}"
    registry_block: "{{ lookup('template', 'registry-section.conf.j2') }}"
  loop: "{{ operator_section.spec.repositoryDigestMirrors }}"
  loop_control:
    loop_var: mirror
    label: "{{ mirror.source }}"

- name: create_image_manifest_files | Create helm images container conf machine config file
  ansible.builtin.template:
    src: registry_conf_machine_config.yml.j2
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/99-helm-chart-disconnected-images-{{ role }}.yaml"
    mode: "0640"
  loop:
    - master
    - worker
  loop_control:
    loop_var: role
  vars:
    registry_conf_content: "{{ list_of_registry_blocks | join('\n') | regex_replace(ocp.default_registry_url, ocp.registry_hostname) }}"
    b64_encoded_conf_file: "{{ registry_conf_content | b64encode }}"
