# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: aws_create_image_manifest_files | Create operator ICSP file in manifests folder
  ansible.builtin.template:
    src: image-content-source-policy-operator.yaml.j2
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/image-content-source-policy.yaml"
    mode: "0640"
