# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_new_cluster_files.yml                                      #
# Version:                                                                        #
#               2023-10-12 espy                                                   #
#               2023-10-25 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-12                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_new_cluster_files | Create backup openshift install config for reference
  ansible.builtin.copy:
    src: "{{ ocp.openshift_install_path }}/latest_build/"
    dest: "{{ ocp.openshift_install_path }}/latest_build-{{ ansible_date_time.iso8601_basic_short }}/"
    mode: '0755'
    remote_src: true

- name: create_new_cluster_files | Delete old Openshift install directory if it exists
  ansible.builtin.file:
    path: "{{ ocp.openshift_install_path }}/cluster-configs"
    state: absent

- name: create_new_cluster_files | Create Openshift install directory
  ansible.builtin.file:
    path: "{{ ocp.openshift_install_path }}/cluster-configs"
    state: directory
    mode: '0755'
