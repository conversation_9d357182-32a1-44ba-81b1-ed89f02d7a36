# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-01 espy                                                   #
#               2023-10-12 espy                                                   #
#               2023-10-25 espy                                                   #
#               2024-01-25 espy                                                   #
#               2024-02-10 espy                                                   #
#               2024-03-04 espy                                                   #
#               2024-06-07 <PERSON><PERSON><PERSON><PERSON>, Michael M                                    #
#               2024-11-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-01                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: temp_install_bridges | Collect service facts
  ansible.builtin.service_facts:

- name: temp_install_bridges | Run root required modules
  become: true
  become_user: root
  block:
    - name: temp_install_bridges | Copy nmstate file
      ansible.builtin.copy:
        content: "{{ network_config | to_nice_yaml(indent=2) }}"
        dest: /etc/NetworkManager/conf.d/nmstate.yaml
        mode: "0644"

    - name: temp_install_bridges | Replace quoted prefix length with non quoted Version
      ansible.builtin.replace:
        path: /etc/NetworkManager/conf.d/nmstate.yaml
        regexp: "'(\\d+)'"
        replace: "\\1"

    - name: temp_install_bridges | Set network configuration per network config yaml
      ansible.builtin.command:
        cmd: nmstatectl apply /etc/NetworkManager/conf.d/nmstate.yaml
      changed_when: true

    - name: temp_install_bridges | Add internal zone to interfaces
      ansible.builtin.command:
        cmd: nmcli con mod {{ item }} connection.zone internal
      loop: "{{ list_of_interfaces }}"
      changed_when: true

    - name: temp_install_bridges | Bring up connection
      ansible.builtin.command:
        cmd: nmcli con up {{ item }}
      loop: "{{ list_of_interfaces }}"
      changed_when: true

    - name: temp_install_bridges | Restart firewalld
      ansible.builtin.systemd:
        name: firewalld
        state: restarted
