# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_passwd_manifest.yml                                        #
# Version:                                                                        #
#               2024-01-17 espy                                                   #
#               2024-06-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-01-17                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_passwd_manifest | Create manifests for setting password on coreos
  ansible.builtin.template:
    src: coreos_default_pw.yml.j2
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/99-{{ role }}-set-core-passwd.yaml"
    mode: "0640"
  loop:
    - master
    - worker
  loop_control:
    loop_var: role
