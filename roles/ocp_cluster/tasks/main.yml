# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-05-09 <PERSON><PERSON><PERSON>, <PERSON>                                      #
#               2023-05-15 espy                                                   #
#               2023-05-20 espy                                                   #
#               2023-05-23 espy                                                   #
#               2023-05-24 espy                                                   #
#               2023-05-27 espy                                                   #
#               2023-06-01 espy                                                   #
#               2023-06-07 Sam<PERSON>, <PERSON> D                                         #
#               2023-06-14 Sam<PERSON>, <PERSON> D                                         #
#               2023-06-21 espy                                                   #
#               2023-06-23 espy                                                   #
#               2023-06-28 espy                                                   #
#               2023-07-03 espy                                                   #
#               2023-07-20 espy                                                   #
#               2023-07-26 espy                                                   #
#               2023-07-27 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-08-16 espy                                                   #
#               2023-08-23 espy                                                   #
#               2023-08-28 espy                                                   #
#               2023-09-05 espy                                                   #
#               2023-09-06 espy                                                   #
#               2023-09-13 <PERSON>, <PERSON> J                                          #
#               2023-09-15 espy                                                   #
#               2023-09-18 espy                                                   #
#               2023-10-01 espy                                                   #
#               2023-10-11 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-11-01 Sams, Landon                                           #
#               2023-11-05 espy                                                   #
#               2023-11-06 Sams, Landon                                           #
#               2023-11-07 espy                                                   #
#               2023-11-10 espy                                                   #
#               2023-11-22 espy                                                   #
#               2023-11-28 espy                                                   #
#               2023-12-11 espy                                                   #
#               2023-12-12 Sams, Landon                                           #
#               2023-12-19 espy                                                   #
#               2024-01-08 espy                                                   #
#               2024-01-23 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-02-12 espy                                                   #
#               2024-02-19 espy                                                   #
#               2024-03-04 espy                                                   #
#               2024-03-04 Sams, Landon                                           #
#               2024-03-06 espy                                                   #
#               2024-03-07 espy                                                   #
#               2024-04-28 espy                                                   #
#               2024-05-05 espy                                                   #
#               2024-05-06 espy                                                   #
#               2024-06-05 espy                                                   #
#               2024-06-06 espy                                                   #
#               2024-06-18 espy                                                   #
#               2024-06-19 espy                                                   #
#               2024-08-07 espy                                                   #
#               2024-08-12 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-05-09                                                        #
# Author:       Casagrande, Wayne                                                 #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Import ocp coreos cache variables
  ansible.builtin.include_vars:
    file: "{{ ocp.ocp_install_files }}/{{ ocp.verson_vars_file }}"

- name: Wait for nexus to become active
  ansible.builtin.uri:
    url: https://{{ ocp.galaxy_nexus_hostname }}/service/rest/v1/status
    return_content: true
  register: nexus_response
  retries: 20
  delay: 30
  until: nexus_response.status == 200

- name: Setup libvirt for ocp installation
  ansible.builtin.include_role:
    name: lmco.linux.linux_libvirt
    public: true
  vars:
    galaxy_linux:
      libvirt_user: "{{ ocp.ocp_user }}"
  when: ocp.setup_libvirt

- name: Create Openshift install directory
  ansible.builtin.file:
    path: "{{ ocp.openshift_install_path }}"
    state: directory
    mode: '0755'
    owner: "{{ ocp.ocp_user }}"
    group: "{{ ocp.ocp_user }}"
  become: true
  become_user: root

- name: Create install config
  ansible.builtin.include_tasks:
    file: setup_install.yml
  vars:
    no_proxy: "{{ ocp.standard_no_proxy_addresses }}"
    mirror_devices: "{{ ocp.node_boot_mirror_devices }}"
  when: ocp.create_install_config

- name: Prompt user to let them know cluster will be deployed
  ansible.builtin.pause:
    prompt: |
      Cluster will be deployed and blow away any existing cluster.  If this is a mistake please pull over here.
      If this is the intention, continue.
      Do you wish to continue? Enter to continue, CTRL + C to quit.
  when: deploy_cluster

- name: Create new cluster install files
  ansible.builtin.include_tasks:
    file: create_new_cluster_files.yml
  when: deploy_cluster

- name: Create bridges for bootstrap vm
  ansible.builtin.include_tasks:
    file: temp_install_bridges.yml
  when: deploy_cluster

- name: Install Openshift
  ansible.builtin.include_tasks:
    file: baremetal_pre_install.yml
  when: deploy_cluster

- name: Install and track openshift install
  ansible.builtin.include_tasks: run_installer.yml
  when: deploy_cluster

- name: Remove bridges and revert to default network settings
  ansible.builtin.include_role:
    name: lmco.openshift.ocp_bastion_network

- name: Fetch kubeconfig
  ansible.builtin.fetch:
    src: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    dest: "{{ ocp.kubeconfig_file }}"
    flat: true
