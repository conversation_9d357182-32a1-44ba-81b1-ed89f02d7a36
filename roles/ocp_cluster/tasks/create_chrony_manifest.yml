# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_chrony_manifest.yml                                        #
# Version:                                                                        #
#               2024-01-17 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-01-17                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_chrony_manifest | Create manifests for chrony ntp servers
  ansible.builtin.template:
    src: chrony.yml.j2
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/99-chrony-config-{{ role }}.yaml"
    mode: "0640"
  loop:
    - master
    - worker
  loop_control:
    loop_var: role
