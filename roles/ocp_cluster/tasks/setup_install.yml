# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-05-15 espy                                                   #
#               2023-05-18 espy                                                   #
#               2023-05-20 espy                                                   #
#               2023-05-23 espy                                                   #
#               2023-05-24 espy                                                   #
#               2023-05-27 espy                                                   #
#               2023-06-01 espy                                                   #
#               2023-06-07 Sams, <PERSON> D                                         #
#               2023-06-19 espy                                                   #
#               2023-06-23 espy                                                   #
#               2023-06-28 espy                                                   #
#               2023-07-03 espy                                                   #
#               2023-07-26 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-08-16 espy                                                   #
#               2023-08-20 espy                                                   #
#               2023-08-23 espy                                                   #
#               2023-08-28 espy                                                   #
#               2023-09-06 espy                                                   #
#               2023-09-07 espy                                                   #
#               2023-09-15 espy                                                   #
#               2023-10-01 espy                                                   #
#               2023-10-11 Sam<PERSON>, Landon                                           #
#               2023-10-12 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-10-26 espy                                                   #
#               2023-11-22 espy                                                   #
#               2023-11-28 espy                                                   #
#               2023-11-29 espy                                                   #
#               2023-12-06 espy                                                   #
#               2023-12-20 espy                                                   #
#               2024-01-04 espy                                                   #
#               2024-01-08 espy                                                   #
#               2024-01-09 espy                                                   #
#               2024-01-15 espy                                                   #
#               2024-01-16 espy                                                   #
#               2024-01-17 espy                                                   #
#               2024-01-18 espy                                                   #
#               2024-01-23 espy                                                   #
#               2024-01-24 espy                                                   #
#               2024-01-25 espy                                                   #
#               2024-02-14 espy                                                   #
#               2024-02-20 espy                                                   #
#               2024-03-04 espy                                                   #
#               2024-03-07 espy                                                   #
#               2024-03-20 espy                                                   #
#               2024-04-27 espy                                                   #
#               2024-05-08 espy                                                   #
#               2024-05-16 espy                                                   #
#               2024-06-18 espy                                                   #
#               2024-11-20 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-05-15                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# TODO: Do we still need this?
- name: setup_install | Gather facts
  ansible.builtin.gather_facts:

# TODO: Will this ever be different then the key provided?
- name: setup_install | Get created public key
  ansible.builtin.slurp:
    src: ~/.ssh/id_rsa.pub
  register: ssh_key

- name: setup_install | Delete old latest build if exists
  ansible.builtin.file:
    path: "{{ ocp.openshift_install_path }}/latest_build/manifests"
    state: absent

- name: setup_install | Create latest build folder
  ansible.builtin.file:
    path: "{{ build_directory }}"
    state: directory
    mode: '0755'
  loop:
    - "{{ ocp.openshift_install_path }}/latest_build"
    - "{{ ocp.openshift_install_path }}/latest_build/manifests"
    - "{{ ocp.openshift_install_path }}/latest_build/installer-manifests"
  loop_control:
    loop_var: build_directory

- name: setup_install | Create butane file for disk configuration
  ansible.builtin.copy:
    content: "{{ lookup('lmco.openshift.butane', butane_template_location) }}"
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/99-{{ machine_role }}-disk-config.yml"
    mode: '0644'
  vars:
    butane_template_location: "{{ role_path }}/templates/diskconfig.bu.j2"
  when: custom_boot_disk_config
  loop:
    - master
    - worker
  loop_control:
    loop_var: machine_role

- name: setup_install | Create image content source policy manifests
  ansible.builtin.include_tasks: create_image_manifest_files.yml
  when: ocp.platform == 'baremetal'

- name: setup_install | Create operator hub catalog source manifests
  ansible.builtin.include_tasks: create_operator_hub_catalog_source.yml
  when: ocp.platform == 'baremetal' and not ocp.dev_connected

- name: setup_install | Create image content source policy manifests
  ansible.builtin.include_tasks: aws_create_image_manifest_files.yml
  when: ocp.platform == 'aws'

- name: setup_install | Create operator hub catalog source manifests
  ansible.builtin.include_tasks: aws_create_operator_hub_catalog_source.yml
  when: ocp.platform == 'aws'

- name: setup_install | Create Passwd manifests
  ansible.builtin.include_tasks: create_passwd_manifest.yml
  when: ocp.coreos_password

- name: setup_install | Create Chrony manifests
  ansible.builtin.include_tasks: create_chrony_manifest.yml
  when: ocp.bastion_ntp_enabled

- name: setup_install | Check if kubeconfig exists
  ansible.builtin.stat:
    path: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
  register: bastion_kubeconfig_file
  failed_when: false
  changed_when: not bastion_kubeconfig_file.stat.exists
  when: not ocp.force_redeployment

- name: setup_install | Check if cluster is running
  ansible.builtin.command:
    cmd: oc get co --insecure-skip-tls-verify
  register: oc_command
  environment:
    KUBECONFIG: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
  changed_when: no_cluster_installed_error in oc_command.stdout or no_kubeconfig_error in oc_command.stdout
  vars:
    no_cluster_installed_error: "Unable to connect to the server"
    no_kubeconfig_error: "Missing or incomplete configuration info"
  failed_when:
    - oc_command.rc != 0
    - no_cluster_installed_error not in oc_command.stdout
  when: not ocp.force_redeployment and bastion_kubeconfig_file.stat.exists

- name: setup_install | Figure out if cluster needs to be redeployed
  ansible.builtin.set_fact:
    deploy_cluster: >-
      {{
        ocp.force_redeployment or
        bastion_kubeconfig_file.changed or
        oc_command.changed
      }}

- name: setup_install | Create openshift install config from template
  ansible.builtin.template:
    src: install-config.j2
    dest: "{{ ocp.openshift_install_path }}/latest_build/install-config.yaml"
    mode: '0755'
    lstrip_blocks: true
  register: install_config_task
  vars:
    original_icsp_contents: "{{ slurped_icsp_file.content | b64decode | regex_replace(ocp.default_registry_url, ocp.registry_hostname) | from_yaml_all }}"
    release_section: "{{ original_icsp_contents | selectattr('metadata', 'defined') | selectattr('metadata.name', 'equalto', 'release-0') | list | first }}"
    ocp_mirrors: "{{ release_section.spec.repositoryDigestMirrors }}"

- name: setup_install | If install config changed and not deploying cluster
  ansible.builtin.include_tasks:
    file: install_config_changes.yml
  when: not deploy_cluster

- name: setup_install | Display deployment values
  ansible.builtin.debug:
    var: "{{ item }}"
  loop:
    - ocp.force_redeployment
    - bastion_kubeconfig_file.changed
    - oc_command.changed

- name: setup_install | Fetch kubeconfig if it exists and the cluster wasn't deployed
  ansible.builtin.fetch:
    src: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    dest: "{{ ocp.kubeconfig_file }}"
    flat: true
  when: not deploy_cluster

- name: setup_install | Create local folder for manifests files
  ansible.builtin.file:
    path: "{{ ocp.manifest_files_folder }}"
    state: directory
    mode: "0755"
  delegate_to: localhost

- name: setup_install | Get names of all manifests files
  ansible.builtin.find:
    paths: "{{ ocp.openshift_install_path }}/latest_build/manifests"
  register: manifest_files

- name: setup_install | Fetch all the manifests files to apply after install
  ansible.builtin.fetch:
    src: "{{ manifest_file.path }}"
    dest: "{{ ocp.manifest_files_folder }}/"
    flat: true
  loop: "{{ manifest_files.files }}"
  loop_control:
    loop_var: manifest_file
    label: "{{ manifest_file.path | basename }}"
