# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        turn_off_nodes.yml                                                #
# Version:                                                                        #
#               2023-10-01 espy                                                   #
#               2023-10-12 espy                                                   #
#               2023-10-16 espy                                                   #
#               2023-10-25 espy                                                   #
#               2024-01-18 espy                                                   #
#               2024-02-01 espy                                                   #
#               2024-02-02 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-02-10 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-01                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: turn_off_nodes | Check if baremetal nodes variable was provided
  ansible.builtin.fail:
    msg: "Warning: Baremetal nodes have not been provided.  Will be unable to shutdown nodes if needed"
  when: ocp.baremetal_list | length == 0
  failed_when: false

- name: turn_off_nodes | Turn off all nodes using IPMI block
  when: ocp.bmc_api == 'ipmi'
  block:
    - name: turn_off_nodes | Turn off nodes using IPMI
      ansible.builtin.command:
        cmd: ipmitool -I lanplus -H {{ bmc_ip_address }} -U {{ bmc_user }} -P "{{ bmc_password }}" chassis power off
      register: ipmitool_result
      loop: "{{ ocp.baremetal_list }}"
      vars:
        bmc_user: "{{ node.bmc.username }}"
        bmc_password: "{{ node.bmc.password }}"
        bmc_ip_address: "{{ node.bmc.ipv4_address }}"
      loop_control:
        loop_var: node
        label: "{{ node.abstract.name }}"
      changed_when: true

- name: turn_off_nodes | Turn off all nodes using redfish block
  when: ocp.bmc_api == 'redfish' or ocp.bmc_api == 'redfish-virtualmedia'
  block:
    - name: turn_off_nodes | Turn off nodes through redfish api
      ansible.builtin.uri:
        url: "https://{{ bmc_ip_address }}/redfish/v1/Systems/{{ system_id }}/Actions/ComputerSystem.Reset/"
        method: "POST"
        user: "{{ bmc_user }}"
        password: "{{ bmc_password }}"
        force_basic_auth: true
        body:
          ResetType: ForceOff
        body_format: json
        validate_certs: false
        return_content: true
        follow_redirects: all
      register: result
      loop: "{{ ocp.baremetal_list }}"
      vars:
        bmc_user: "{{ node.bmc.username }}"
        bmc_password: "{{ node.bmc.password }}"
        bmc_ip_address: "{{ node.bmc.ipv4_address }}"
        system_id: "{{ node.bmc.system_id }}"
        ilo6_off_already_message: "Base.1.5.NoOperation"
        ilo6_success_message: "Task for Computer Reset"
        ilo5_off_already_message: "Power is off"
        ilo5_success_message: "Success"
      environment:
        no_proxy: "{{ bmc_ip_address }}"
      failed_when: ilo5_off_already_message not in result.content and
        ilo6_off_already_message not in result.content and
        ilo5_success_message not in result.content and
        ilo6_success_message not in result.content
      changed_when: ilo5_success_message in result.content or ilo6_success_message in result.content
      loop_control:
        loop_var: node
        label: "{{ node.abstract.name }}"
