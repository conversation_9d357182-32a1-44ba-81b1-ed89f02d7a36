# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-05-09 <PERSON><PERSON><PERSON>, <PERSON>                                      #
#               2023-05-15 espy                                                   #
#               2023-05-20 espy                                                   #
#               2023-05-23 espy                                                   #
#               2023-05-24 espy                                                   #
#               2023-05-27 espy                                                   #
#               2023-06-01 espy                                                   #
#               2023-06-07 Sam<PERSON>, <PERSON> D                                         #
#               2023-06-14 Sam<PERSON>, <PERSON> D                                         #
#               2023-06-21 espy                                                   #
#               2023-06-23 espy                                                   #
#               2023-06-28 espy                                                   #
#               2023-07-03 espy                                                   #
#               2023-07-26 espy                                                   #
#               2023-07-31 espy                                                   #
#               2023-08-22 espy                                                   #
#               2023-08-23 espy                                                   #
#               2023-08-28 espy                                                   #
#               2023-08-30 espy                                                   #
#               2023-08-31 espy                                                   #
#               2023-09-05 espy                                                   #
#               2023-09-15 espy                                                   #
#               2023-09-20 espy                                                   #
#               2023-10-01 espy                                                   #
#               2023-10-02 espy                                                   #
#               2023-10-09 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-10-26 espy                                                   #
#               2023-10-31 espy                                                   #
#               2023-11-01 espy                                                   #
#               2023-11-22 espy                                                   #
#               2023-12-20 espy                                                   #
#               2024-01-24 espy                                                   #
#               2024-01-24 Hunter, Ian J                                          #
#               2024-02-13 espy                                                   #
#               2024-04-27 espy                                                   #
#               2024-04-28 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-06-20 espy                                                   #
#               2024-07-30 espy                                                   #
#               2024-11-21 espy                                                   #
#               2024-11-25 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-05-09                                                        #
# Author:       Casagrande, Wayne                                                 #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: baremetal_pre_install | Collect service facts
  ansible.builtin.service_facts:

- name: baremetal_pre_install | Install packages needed for openshift install
  ansible.builtin.yum:
    name: ipmitool
    state: present
  when: ocp.bmc_api == 'ipmi'
  become: true
  become_user: root

- name: baremetal_pre_install | Get all vms with bootstrap in its name
  ansible.builtin.shell:
    cmd: set -o pipefail && virsh list --all | tail -n +3 | grep bootstrap | awk {'print $2'}
  environment:
    LIBVIRT_DEFAULT_URI: qemu:///system
  register: bootstrap_vms
  failed_when: false
  changed_when: false

- name: baremetal_pre_install | Delete previous vms
  ansible.builtin.shell:
    cmd: |
      virsh destroy {{ bootstrap_vm_id }}
      virsh undefine {{ bootstrap_vm_id }}
      virsh vol-delete {{ bootstrap_vm_id }} --pool {{ bootstrap_vm_id }}
      virsh vol-delete {{ bootstrap_vm_id }}.ign --pool {{ bootstrap_vm_id }}
      virsh pool-destroy {{ bootstrap_vm_id }}
      virsh pool-undefine {{ bootstrap_vm_id }}
  environment:
    LIBVIRT_DEFAULT_URI: qemu:///system
  loop: "{{ bootstrap_vms.stdout.split(\n) }}"
  vars:
    bootstrap_vm_id: "{{ item }}"
  changed_when: true

- name: baremetal_pre_install | Remove any previous openshift images
  ansible.builtin.file:
    path: /var/lib/libvirt/openshift-images
    state: absent
  become: true
  become_user: root

- name: baremetal_pre_install | Set NIC boot order
  lmco.openshift.set_nic_boot_order:
    username: "{{ node.bmc.username }}"
    password: "{{ node.bmc.password }}"
    ipv4_address: "{{ node.bmc.ipv4_address }}"
    system_id: "{{ node.bmc.system_id }}"
    boot_mac: "{{ node.openshift.provisioning_network_mac_address }}"
  loop: "{{ ocp.baremetal_list }}"
  loop_control:
    loop_var: node
    label: "{{ node.abstract.name }}"
  when: ocp.bmc_api == 'redfish'

- name: baremetal_pre_install | Reset TPM2 Module on all cluster nodes
  ansible.builtin.uri:
    url: https://{{ node.bmc.ipv4_address }}/redfish/v1/Systems/{{ node.bmc.system_id }}/bios/settings/
    method: "PATCH"
    user: "{{ node.bmc.username }}"
    password: "{{ node.bmc.password }}"
    force_basic_auth: true
    body:
      Attributes:
        Tpm2Operation: Clear
    body_format: json
    validate_certs: false
    return_content: true
    follow_redirects: all
  register: result
  failed_when: "'SystemResetRequired' not in result.content"
  changed_when: "'SystemResetRequired' in result.content"
  when: ocp.nodes_tpm_encryption
  loop: "{{ ocp.baremetal_list }}"
  loop_control:
    loop_var: node
    label: "{{ node.abstract.name }}"
  environment:
    no_proxy: "{{ node.bmc.ipv4_address }}"

- name: baremetal_pre_install | Turn of Nodes
  ansible.builtin.include_tasks: turn_off_nodes.yml

# TODO Update wait for nodes to take a variable on which nodes to wait for to turn off
- name: baremetal_pre_install | Pause for 10 seconds so that nodes fully shut off
  ansible.builtin.pause:
    seconds: 20

- name: baremetal_pre_install | Check for duplicate ips in baremetal network
  ansible.builtin.shell:
    cmd: |
      ips=({{ ocp.baremetal_network_cidr | lmco.openshift.cidr_to_ips }})
      for ip in "${ips[@]}"; do
          set -o pipefail && arping -c 4 -w 3 -I {{ ocp.baremetal_network_temp_bridge_name }} $ip |\
          grep "Unicast reply from"{{ multi_bastion_grep_filter if groups.bastion | length != 1 else "" }} &
      done
      wait
  register: arping
  changed_when: false
  failed_when: dup_response in arping.stdout
  vars:
    dup_response: Unicast reply from
    multi_bastion_grep_filter: "| grep -v -E '{{ list_of_exceptions | join('|') }}'"
    list_of_exceptions: "{{ list_of_default_exceptions + ocp.baremetal_network_existing_servers }}"
    list_of_default_exceptions:
      - "{{ ocp.baremetal_network_bastion_vip }}"
      - "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(9) }}"

- name: baremetal_pre_install | Check for duplicate ips in baremetal network from second bastion
  ansible.builtin.shell:
    cmd: |
      ips=({{ ocp.baremetal_network_cidr | lmco.openshift.cidr_to_ips }})
      for ip in "${ips[@]}"; do
          set -o pipefail && arping -c 4 -w 3 -I {{ ocp.bastion_baremetal_network_interface_name }} $ip |\
          grep "Unicast reply from"{{ multi_bastion_grep_filter if groups.bastion | length != 1 else "" }} &
      done
      wait
  register: arping
  changed_when: false
  failed_when: stdout in arping and dup_response in arping.stdout
  vars:
    stdout: stdout
    dup_response: Unicast reply from
    multi_bastion_grep_filter: "| grep -v -E '{{ list_of_exceptions | join('|') }}'"
    list_of_exceptions: "{{ [ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(8)] + ocp.baremetal_network_existing_servers }}"
  when: groups.bastion | length != 1
  delegate_to: '{{ groups["bastion"][1] }}'

- name: baremetal_pre_install | Check for duplicate ips in the provisioning network
  ansible.builtin.shell:
    cmd: |
      ips=({{ ocp.provisioning_network_cidr | lmco.openshift.cidr_to_ips }})
      for ip in "${ips[@]}"; do
          set -o pipefail && arping -c 4 -w 3 -I {{ ocp.bastion_provisioning_network_interface_name }} $ip | grep "Unicast reply from" &
      done
      wait
  register: arping
  changed_when: false
  failed_when: dup_response in arping.stdout
  vars:
    dup_response: Unicast reply from
  when: ocp.setup_provisioning_network
