# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: aws_create_operator_hub_catalog_source | Get all catalog source files
  ansible.builtin.template:
    src: catalogSource-operator-index.yaml.j2
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/{{ operator_name }}-catalogSource-operator-index.yaml"
    mode: "0640"
  loop:
    - redhat-operator-index
    - certified-operator-index
  loop_control:
    loop_var: operator_name
    label: "{{ operator_name }}"

- name: aws_create_operator_hub_catalog_source | Disable Default Catalog Source
  ansible.builtin.copy:
    src: disable_default_operators.yml
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/disable_default_operators.yaml"
    mode: "0640"
