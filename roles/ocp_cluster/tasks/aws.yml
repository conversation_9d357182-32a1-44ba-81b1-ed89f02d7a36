# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# TODO: Probably only need to install the libvirt library not the whole thing.
- name: aws | Setup libvirt for ocp installation
  ansible.builtin.include_role:
    name: lmco.linux.linux_libvirt
    public: true
  vars:
    galaxy_linux:
      libvirt_user: "{{ ocp.ocp_user }}"
  when: ocp.setup_libvirt

- name: aws | Create Openshift install directory
  ansible.builtin.file:
    path: "{{ ocp.openshift_install_path }}"
    state: directory
    mode: '0755'
    owner: "{{ ocp.ocp_user }}"
    group: "{{ ocp.ocp_user }}"
  become: true
  become_user: root

- name: aws | Create install config
  ansible.builtin.include_tasks:
    file: setup_install.yml
  vars:
    no_proxy: "{{ ocp.standard_no_proxy_addresses }}"
    mirror_devices: "{{ ocp.node_boot_mirror_devices }}"
  when: ocp.create_install_config

- name: aws | Prompt user to let them know cluster will be deployed
  ansible.builtin.pause:
    prompt: |
      Cluster will be deployed and blow away any existing cluster.  If this is a mistake please pull over here.
      If this is the intention, continue.
      Do you wish to continue? Enter to continue, CTRL + C to quit.
  when: deploy_cluster

- name: aws | Kill processes if any found
  ansible.builtin.command:
    cmd: "pkill -9 -f ~/.local/bin/{{ ocp.openshift_install_file }}"
  register: kill_result
  changed_when: kill_result.rc == 0
  failed_when: kill_result.rc != 0 and kill_result.rc != 1

- name: aws | Remove existing cluster
  lmco.openshift.remove_aws_cluster:
    cluster: "{{ ocp.openshift_cluster }}"
  when: deploy_cluster
  delegate_to: localhost
  environment:
    AWS_CA_BUNDLE: /etc/pki/ca-trust/source/anchors/Combined_pem.pem

- name: aws | Create new cluster install files
  ansible.builtin.include_tasks:
    file: create_new_cluster_files.yml
  when: deploy_cluster

- name: aws | Create AWS Folder
  ansible.builtin.file:
    path: ~/.aws
    mode: "0700"
    state: directory

- name: aws | Create aws credentials file
  ansible.builtin.copy:
    dest: ~/.aws/credentials
    mode: "0600"
    content: |
      [default]
      aws_access_key_id     = {{ ocp.aws.aws_access_key_id }}
      aws_secret_access_key = {{ ocp.aws.aws_secret_access_key }}

- name: aws | Create aws config file
  ansible.builtin.copy:
    dest: ~/.aws/config
    mode: "0600"
    content: |
      [default]
      region = {{ ocp.aws.region }}

- name: aws | Install and track openshift install
  ansible.builtin.include_tasks: run_installer.yml
  when: deploy_cluster

- name: aws | Fetch kubeconfig
  ansible.builtin.fetch:
    src: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    dest: "{{ ocp.kubeconfig_file }}"
    flat: true
