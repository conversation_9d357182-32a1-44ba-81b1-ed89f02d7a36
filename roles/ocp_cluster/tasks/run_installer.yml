# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        install_openshift.yml                                             #
# Version:                                                                        #
#               2023-10-01 espy                                                   #
#               2023-10-11 <PERSON><PERSON>, <PERSON>                                           #
#               2023-10-12 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-11-07 espy                                                   #
#               2023-12-21 espy                                                   #
#               2024-01-08 espy                                                   #
#               2024-02-05 espy                                                   #
#               2024-02-06 espy                                                   #
#               2024-02-07 <PERSON><PERSON><PERSON><PERSON>, <PERSON>                                    #
#               2024-02-08 espy                                                   #
#               2024-03-05 espy                                                   #
#               2024-04-27 espy                                                   #
#               2024-08-15 espy                                                   #
#               2024-08-16 espy                                                   #
#               2024-09-09 espy                                                   #
#               2024-11-25 espy                                                   #
#               2024-11-26 espy                                                   #
#               2024-12-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-01                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: run_installer | Get openshift installer from galaxy nexus
  ansible.builtin.get_url:
    url: "{{ ocp.galaxy_nexus_repo }}/{{ ocp.openshift_install_file }}"
    dest: /usr/local/bin/{{ ocp.openshift_install_file }}
    mode: "0755"
  when: ocp.platform == 'baremetal'
  become: true
  become_user: root

- name: run_installer | Get openshift installer from galaxy nexus
  ansible.builtin.unarchive:
    src: "{{ installer_url }}"
    dest: /usr/local/bin/
    mode: "0755"
    remote_src: true
    include:
      - openshift-install
  when: ocp.platform == 'aws'
  vars:
    openshift_mirror_proxy: "{{ ocp.galaxy_nexus_hostname }}/repository/mirror-openshift-com"
    github_proxy: "{{ ocp.galaxy_nexus_hostname }}/repository/github-com"
    openshift_installer_url: "https://{{ openshift_mirror_proxy }}/pub/openshift-v4/clients/ocp/{{ ocp.openshift_version }}/openshift-install-linux.tar.gz"
    okd_installer_url: "https://{{ github_proxy }}/okd-project/okd/releases/download/{{ ocp.okd_version }}/openshift-install-linux-{{ ocp.okd_version }}.tar.gz"
    installer_url: >-
      {{
        openshift_installer_url if ocp.distro == 'ocp'
        else
        okd_installer_url if ocp.distro == 'okd'
        else
        None
      }}
  become: true
  become_user: root

- name: run_installer | Copy openshift install config to install directory
  ansible.builtin.copy:
    src: "{{ ocp.openshift_install_path }}/latest_build/install-config.yaml"
    dest: "{{ ocp.openshift_install_path }}/cluster-configs/install-config.yaml"
    mode: '0644'
    remote_src: true

- name: run_installer | Block to handle fips bug with openshift
  when: ocp.fips_enabled and ocp.openshift_version.split('.')[1] | int < 16
  block:
    - name: run_installer | Disable FIPS Temporarily since theres a bug with the openshift installer
      ansible.builtin.command:
        cmd: fips-mode-setup --disable
      changed_when: true
      become: true
      become_user: root

    - name: run_installer | Reboot after disabling FIPS
      ansible.builtin.reboot:
        pre_reboot_delay: 0
      become: true
      become_user: root

    - name: run_installer | Wait for nexus to become active
      ansible.builtin.uri:
        url: https://{{ ocp.galaxy_nexus_hostname }}/service/rest/v1/status
        return_content: true
      register: nexus_response
      retries: 20
      delay: 30
      until: nexus_response.status == 200

- name: run_installer | Create manifest files
  ansible.builtin.command:
    cmd: |
      /usr/local/bin/{{ ocp.openshift_install_file }}
      --dir {{ ocp.openshift_install_path }}/cluster-configs
      --log-level debug
      create manifests
    chdir: "{{ ocp.openshift_install_path }}"
  changed_when: true

- name: run_installer | Copy installer manifests files to latest install folder
  ansible.builtin.copy:
    dest: "{{ ocp.openshift_install_path }}/latest_build/installer-manifests/"
    src: "{{ ocp.openshift_install_path }}/cluster-configs/manifests/"
    mode: '0755'
    remote_src: true

- name: run_installer | Copy manifests files to cluster configs folder
  ansible.builtin.copy:
    src: "{{ ocp.openshift_install_path }}/latest_build/manifests/"
    dest: "{{ ocp.openshift_install_path }}/cluster-configs/manifests/"
    mode: '0755'
    remote_src: true

- name: run_installer | Delete install config created ICSP manifest files
  ansible.builtin.file:
    path: "{{ ocp.openshift_install_path }}/cluster-configs/manifests/image-content-source-policy-{{ file_no }}.yaml"
    state: absent
  loop:
    - 0
    - 1
  loop_control:
    loop_var: file_no

- name: run_installer | Install openshift
  ansible.builtin.command:
    cmd: |
      /usr/local/bin/{{ ocp.openshift_install_file }}
      --dir {{ ocp.openshift_install_path }}/cluster-configs
      --log-level debug
      create cluster
    chdir: "{{ ocp.openshift_install_path }}"
  changed_when: true
  async: 3600
  poll: 0

- name: run_installer | Wait for kubeconfig file
  ansible.builtin.wait_for:
    path: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    timeout: 90
    msg: Timeout waiting for kubeconfig file

- name: run_installer | Get contents of kubeconfig
  ansible.builtin.slurp:
    src: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
  register: encoded_kubeconfig

- name: run_installer | Remove old certificate authority from kubeconfig
  ansible.builtin.lineinfile:
    path: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    regexp: '^    certificate-authority-data:'
    state: absent

- name: run_installer | Add certificate authority to kubeconfig
  ansible.builtin.lineinfile:
    path: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    insertbefore: '^    server:'
    line: "    certificate-authority-data: {{ new_cert_data }}"
  vars:
    kubeconfig_yaml: "{{ (encoded_kubeconfig.content) | b64decode | from_yaml }}"
    encoded_cert: "{{ kubeconfig_yaml.clusters[0].cluster['certificate-authority-data'] | default('') }}"
    full_cert_chain: "{{ encoded_cert | b64decode | default('') }}"
    cert_regex: '-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----'
    kubeconfig_certs: "{{ full_cert_chain | regex_findall(cert_regex, multiline=True) }}"
    provided_certs: "{{ lookup('file', ocp.ca_cert) | regex_findall(cert_regex, multiline=True) }}"
    combined_cert: "{{ kubeconfig_certs | union(provided_certs) | unique | join('\n') }}"
    new_cert_data: "{{ combined_cert | b64encode }}"

- name: run_installer | Create .kube directory
  ansible.builtin.file:
    path: ~/.kube
    state: directory
    mode: "0755"

- name: run_installer | Copy kubeconfig to use location
  ansible.builtin.copy:
    remote_src: true
    src: "{{ ocp.openshift_install_path }}/cluster-configs/auth/kubeconfig"
    dest: ~/.kube/config
    mode: "0600"

- name: run_installer | Wait for bootstrap node to be spin up through libvirt
  ansible.builtin.command:
    cmd: virsh list --name
  changed_when: true
  environment:
    LIBVIRT_DEFAULT_URI: qemu:///system
  register: virsh_output
  vars:
    bootstrap: 'bootstrap'
  until: bootstrap in virsh_output.stdout
  retries: 600
  delay: 5
  when: ocp.bastion_libvirt_enabled

- name: run_installer | Wait for bootstrap node to be spin up on AWS
  amazon.aws.ec2_instance_info:
    region: "{{ ocp.aws.region }}"
    filters:
      "tag:Name": "{{ ocp.openshift_cluster }}*-bootstrap"
      instance-state-name: "running"
    aws_ca_bundle: /etc/pki/ca-trust/source/anchors/Combined_pem.pem
  register: ec2_info
  changed_when: true
  until: ec2_info.instances is defined and ec2_info.instances | length == 1
  retries: 30
  delay: 10
  when: ocp.platform == 'aws'
  delegate_to: localhost

- name: run_installer | Set bootstrap IP
  ansible.builtin.set_fact:
    bootstrap_baremetal_ip: "{{ ec2_info.instances[0].private_ip_address if ocp.platform == 'aws' else ocp.baremetal_network_bootstrap_ip }}"

- name:  run_installer | Add ssh shortcuts to ssh config file
  ansible.builtin.blockinfile:
    create: true
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR SSH CONFIG FOR BOOTSTRAP NODE"
    path: "~/.ssh/config"
    mode: "0600"
    block: |
      Host bootstrap
        User core
        Hostname {{ bootstrap_baremetal_ip }}
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
  when: ocp.platform == 'aws'

- name: run_installer | Wait for master nodes to be spin up on AWS
  amazon.aws.ec2_instance_info:
    region: "{{ ocp.aws.region }}"
    filters:
      "tag:galaxy-cluster": "{{ ocp.openshift_cluster }}*-master-*"
      instance-state-name: "running"
    aws_ca_bundle: /etc/pki/ca-trust/source/anchors/Combined_pem.pem
  register: ec2_info
  delegate_to: localhost
  when: ocp.platform == 'aws'

- name: run_installer | Wait for master nodes to be spin up on AWS
  amazon.aws.ec2_instance_info:
    region: "{{ ocp.aws.region }}"
    filters:
      "tag:galaxy-cluster": "{{ ocp.openshift_cluster }}*-master-*"
      instance-state-name: "running"
    aws_ca_bundle: /etc/pki/ca-trust/source/anchors/Combined_pem.pem
  register: ec2_info
  changed_when: true
  until: ec2_info.instances is defined and ec2_info.instances | length == ocp.aws.master_count
  retries: 30
  delay: 10
  when: ocp.platform == 'aws'
  delegate_to: localhost

- name:  run_installer | Add ssh shortcuts to ssh config file for master nodes
  ansible.builtin.blockinfile:
    create: true
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR SSH CONFIG FOR {{ node.tags.Name }}"
    path: "~/.ssh/config"
    mode: "0600"
    block: |
      Host m{{ node.tags.Name[-1] }}
        User core
        Hostname {{ node.private_ip_address }}
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
  loop: "{{ ec2_info.instances }}"
  loop_control:
    loop_var: node
    label: "{{ node.tags.Name }}"
  when: ocp.platform == 'aws'

- name: run_installer | Waiting for port 22 to open on baremetal network on bootstrap node
  ansible.builtin.wait_for:
    host: "{{ bootstrap_baremetal_ip }}"
    port: 22
    delay: 1
    state: started
    timeout: 1000
    sleep: 1
    msg: Bootstrap node did not become accessible through ssh through the baremetal network

- name: run_installer | Fix for fedora core os
  ansible.builtin.script: fedora_bug_fix.bash
  delegate_to: "{{ bootstrap_baremetal_ip }}"
  become: true
  become_user: root
  vars:
    ansible_ssh_user: core
    ansible_ssh_common_args: "{{ bootstrap_node_ssh_args }}"
  when: ocp.distro == 'okd'

- name: run_installer | Wait for bootstrap node to initally shutdown
  ansible.builtin.wait_for:
    host: "{{ bootstrap_baremetal_ip }}"
    port: 22
    delay: 0
    state: stopped
    timeout: 1000
    sleep: 1
    msg: Bootstrap node never initially shutdown
  when: ocp.distro == 'okd'

- name: run_installer | Wait for bootstrap node to reboot
  ansible.builtin.wait_for:
    host: "{{ bootstrap_baremetal_ip }}"
    port: 22
    delay: 0
    state: started
    timeout: 1000
    sleep: 1
    msg: Bootstrap node never rebooted
  when: ocp.distro == 'okd'

- name: run_installer | Bootstrap node commands
  delegate_to: "{{ bootstrap_baremetal_ip }}"
  become: true
  become_user: root
  vars:
    ansible_ssh_user: core
    ansible_ssh_common_args: "{{ bootstrap_node_ssh_args }}"
  block:
    - name: run_installer | Wait for dnsmasq container to spin up inside bootstrap node
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        dnsmasq: 'dnsmasq'
      until: dnsmasq in podman_output.stdout
      retries: 600
      delay: 10
      when: ocp.bmc_api == 'redfish' or ocp.bmc_api == 'ipmi'

    - name: run_installer | Wait for cluster-bootstrap, and httpd containers to spin up
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        bootstrap: 'cluster-bootstrap'
      until: bootstrap in podman_output.stdout
      retries: 600
      delay: 10

    - name: run_installer | Wait for image-customization to spin up
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        customization: 'image-customization'
      until: customization in podman_output.stdout
      retries: 600
      delay: 10
      when: ocp.bmc_api == 'redfish' or ocp.bmc_api == 'ipmi'

    - name: run_installer | Wait for ironic-inspector to spin up
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        inspector: 'ironic-inspector'
      until: inspector in podman_output.stdout
      retries: 600
      delay: 10
      when: (ocp.bmc_api == 'redfish' or ocp.bmc_api == 'ipmi') and ocp.openshift_version == '4.14.31'

    - name: run_installer | Wait for ironic-ramdisk-logs containers to spin up
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        ironic: 'ironic'
      until: ironic in podman_output.stdout
      retries: 600
      delay: 10
      when: ocp.bmc_api == 'redfish' or ocp.bmc_api == 'ipmi'

    - name: run_installer | Wait for ironic-ramdisk-logs containers to spin up
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        ramdisk: 'ironic-ramdisk-logs'
      until: ramdisk in podman_output.stdout
      retries: 600
      delay: 10
      when: ocp.bmc_api == 'redfish' or ocp.bmc_api == 'ipmi'

    - name: run_installer | Wait for ironic-ramdisk-logs containers to spin up
      ansible.builtin.raw: podman ps
      changed_when: false
      register: podman_output
      vars:
        baremetal: 'baremetal-operator'
      until: baremetal in podman_output.stdout
      retries: 600
      delay: 10
      when: (ocp.bmc_api == 'redfish' or ocp.bmc_api == 'ipmi') and ocp.openshift_version.split('.')[1] | int >= 16

- name: run_installer | Wait for initial boot of master nodes
  lmco.openshift.async_wait_for_boot:
    hosts: "{{ baremetal_master_nodes }}"

- name: run_installer | Wait for SSH open on master nodes on baremetal network and run update time server command
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_master_nodes_hostnames }}"
    command: "sudo chronyc add server {{ ocp.baremetal_network_ntp_ip }}"
    successful_outputs:
      - "200 OK"
      - "511 Source already present"

- name: run_installer | Wait for initial shutdown of master nodes
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_master_nodes_hostnames }}"
    ssh_port_state: closed

- name: run_installer | Wait for second boot of master nodes boot
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_master_nodes_hostnames }}"

- name: run_installer | Wait for second shutdown of master nodes
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_master_nodes_hostnames }}"
    ssh_port_state: closed
  async: 3600
  poll: 30

- name: run_installer | Wait for final boot of master nodes boot
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_master_nodes_hostnames }}"

- name: run_installer | Wait for Bootstrap Complete
  ansible.builtin.command:
    cmd: |
      /usr/local/bin/{{ ocp.openshift_install_file }}
      --dir {{ ocp.openshift_install_path }}/cluster-configs
      --log-level debug
      wait-for bootstrap-complete
    chdir: "{{ ocp.openshift_install_path }}"
  changed_when: true
  async: 3600
  poll: 30

- name: run_installer | Wait for bootstrap node to be spin up on AWS
  amazon.aws.ec2_instance_info:
    region: "{{ ocp.aws.region }}"
    filters:
      "tag:galaxy-cluster": "{{ ocp.openshift_cluster }}*-worker-*"
      instance-state-name: "running"
    aws_ca_bundle: /etc/pki/ca-trust/source/anchors/Combined_pem.pem
  register: ec2_info
  changed_when: true
  until: ec2_info.instances is defined and ec2_info.instances | length == ocp.aws.worker_count
  retries: 30
  delay: 10
  when: ocp.platform == 'aws'
  delegate_to: localhost

- name:  run_installer | Add ssh shortcuts to ssh config file for worker nodes
  ansible.builtin.blockinfile:
    create: true
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR SSH CONFIG FOR {{ node.tags.Name }}"
    path: "~/.ssh/config"
    mode: "0600"
    block: |
      Host w{{ node.tags.Name[-1] }}
        User core
        Hostname {{ node.private_ip_address }}
        StrictHostKeyChecking no
        UserKnownHostsFile /dev/null
  loop: "{{ ec2_info.instances }}"
  loop_control:
    loop_var: node
    label: "{{ node.tags.Name }}"
  when: ocp.platform == 'aws'

- name: run_installer | Wait for initial boot of worker nodes
  lmco.openshift.async_wait_for_boot:
    hosts: "{{ baremetal_worker_nodes }}"
    timeout: 3600

- name: run_installer | Wait for SSH open on worker nodes on baremetal network and run update time server command
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_worker_nodes_hostnames }}"
    command: "sudo chronyc add server {{ ocp.baremetal_network_ntp_ip }}"
    successful_outputs:
      - "200 OK"
      - "511 Source already present"

- name: run_installer | Wait for initial shutdown of worker nodes
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_worker_nodes_hostnames }}"
    ssh_port_state: closed

- name: run_installer | Wait for second boot of worker nodes boot
  lmco.openshift.async_wait_for_ssh:
    hosts: "{{ baremetal_worker_nodes_hostnames }}"

- name: run_installer | Wait for Install Complete
  ansible.builtin.command:
    cmd: |
      /usr/local/bin/{{ ocp.openshift_install_file }}
      --dir {{ ocp.openshift_install_path }}/cluster-configs
      --log-level debug
      wait-for install-complete
    chdir: "{{ ocp.openshift_install_path }}"
  changed_when: true
  async: 3600
  poll: 30

- name: run_installer | Reenable FIPS since theres a bug with the openshift installer
  ansible.builtin.command:
    cmd: fips-mode-setup --enable
  changed_when: true
  become: true
  become_user: root
  when: ocp.fips_enabled and ocp.openshift_version.split('.')[1] | int < 16

- name: run_installer | Reboot after Reenabling FIPS
  ansible.builtin.reboot:
    pre_reboot_delay: 0
  become: true
  become_user: root
  when: ocp.fips_enabled and ocp.openshift_version.split('.')[1] | int < 16
