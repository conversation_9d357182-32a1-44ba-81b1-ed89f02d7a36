# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        install_config_changes.yml                                        #
# Version:                                                                        #
#               2024-05-16 espy                                                   #
#               2024-05-17 espy                                                   #
#               2024-12-18 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-05-16                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: install_config_changes | Get latest install config
  ansible.builtin.find:
    paths: "{{ ocp.openshift_install_path }}"
    recurse: false
    file_type: directory
    patterns: "latest_build-*"
  register: latest_build_results

- name: install_config_changes | Slurp contents of new install config
  ansible.builtin.slurp:
    path: "{{ ocp.openshift_install_path }}/latest_build/install-config.yaml"
  register: encoded_new_config

- name: install_config_changes | Slurp contents of current install config
  ansible.builtin.slurp:
    path: "{{ latest_build_results.files | map(attribute='path') | sort(reverse=True) | first }}/install-config.yaml"
  register: encoded_current_config

- name: install_config_changes | Determine if install config has any nonidompotent changes
  lmco.openshift.compare_install_configs:
    install_config_1: "{{ encoded_new_config.content | b64decode | from_yaml }}"
    install_config_2: "{{ encoded_current_config.content | b64decode | from_yaml }}"
  register: install_config_compare

- name: install_config_changes | Prompt user to let them know some changes will not be applied
  ansible.builtin.pause:
    prompt: |
      Changes were found in the new install config that were not in the existing cluster install config.
      The changes are listed below:
      {{ install_config_compare.unsupported_list | to_nice_yaml }}
      These changes will not be applied to the existing cluster.
      Do you wish to continue? Enter to continue, CTRL + C to quit.
  when: install_config_compare.unsupported_list
