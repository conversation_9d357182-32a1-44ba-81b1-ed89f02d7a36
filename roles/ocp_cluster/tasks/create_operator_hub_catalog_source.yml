# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_operator_hub_catalog_source.yml                            #
# Version:                                                                        #
#               2024-01-04 espy                                                   #
#               2024-01-08 espy                                                   #
#               2024-04-30 <PERSON><PERSON><PERSON><PERSON>, Michael <PERSON>                                    #
#               2024-09-16 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-01-04                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_operator_hub_catalog_source | List files catalog source files in oc-mirror-workspace
  ansible.builtin.find:
    paths: "{{ ocp.openshift_install_path }}/nexus-data/oc-mirror-workspace"
    patterns: catalogSource.*
    use_regex: true
    recurse: true
  register: catalog_source_operator_indexes

- name: create_operator_hub_catalog_source | Hardcode Replace name with cs-name because new version of oc mirror does this
  ansible.builtin.replace:
    path: "{{ catalog_source.path }}"
    regexp: " {2}name: (cs-)?"
    replace: "  name: "
  loop: "{{ catalog_source_operator_indexes.files }}"
  loop_control:
    loop_var: catalog_source
    label: "{{ catalog_source.path }}"

- name: create_operator_hub_catalog_source | Hardcode Replace name operators instead of operator-index because new version of oc mirror does this
  ansible.builtin.replace:
    path: "{{ catalog_source.path }}"
    regexp: "operator-index$"
    replace: "operators"
  loop: "{{ catalog_source_operator_indexes.files }}"
  loop_control:
    loop_var: catalog_source
    label: "{{ catalog_source.path }}"

- name: create_operator_hub_catalog_source | Hardcode Replace registry urls
  ansible.builtin.replace:
    path: "{{ catalog_source.path }}"
    regexp: "{{ ocp.default_registry_url }}"
    replace: "{{ ocp.registry_hostname }}"
  loop: "{{ catalog_source_operator_indexes.files }}"
  loop_control:
    loop_var: catalog_source
    label: "{{ catalog_source.path }}"

- name: create_operator_hub_catalog_source | Get all catalog source files
  ansible.builtin.copy:
    src: "{{ catalog_source.path }}"
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/"
    remote_src: true
    mode: "0640"
  loop: "{{ catalog_source_operator_indexes.files }}"
  loop_control:
    loop_var: catalog_source
    label: "{{ catalog_source.path }}"

- name: create_operator_hub_catalog_source | Disable Default Catalog Source
  ansible.builtin.copy:
    src: disable_default_operators.yml
    dest: "{{ ocp.openshift_install_path }}/latest_build/manifests/disable_default_operators.yaml"
    mode: "0640"
