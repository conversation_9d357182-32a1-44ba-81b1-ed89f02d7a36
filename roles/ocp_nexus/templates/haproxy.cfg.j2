{# j2lint: disable=jinja-variable-lower-case #}
#############################################################################################
# Global Section                                                                            #
#############################################################################################
global
  daemon
  maxconn                   10000
  log     /dev/log local2 debug

#############################################################################################
# Defaults Section                                                                          #
#############################################################################################
defaults
  mode                      http
  option                    http-server-close
  option                    httplog
  option                    logasap
  timeout http-request      10m
  timeout queue             10m
  timeout connect           10m
  timeout client            10m
  timeout server            10m
  timeout http-keep-alive   10m
  timeout check             10m
  log                       global
  log-format "Status: %ST Server: %s TotalTime: %Tt BytesRead: %B Method: %HM URI: %HU"

frontend all_http
  bind *:80
  redirect scheme https code 301

frontend all_https
  bind *:443 ssl crt {{ haproxy_container_cert_file }}
  mode http
  acl {{ galaxy_nexus_domain }}_host    hdr(host) -i {{ galaxy_nexus_domain }}
  use_backend {{ galaxy_nexus_domain }}_backend if {{ galaxy_nexus_domain }}_host
{% for domain in registries %}
  acl is_{{ domain.name }} hdr(host) -i {{ domain.proxy.remoteUrl | urlsplit('netloc') | regex_replace('\\.', '-') | regex_replace('$', '.' + galaxy_nexus_domain) }}
  use_backend {{ domain.name }}_backend if is_{{ domain.name }}
{% endfor %}

{% for domain in registries %}
backend {{ domain.name }}_backend
  mode http
  server {{ domain.name }} 127.0.0.1:{{ domain.docker.httpPort }}
{% endfor %}

backend {{ galaxy_nexus_domain }}_backend
  mode http
  server nexus 127.0.0.1:{{ nexus_http_port }}
