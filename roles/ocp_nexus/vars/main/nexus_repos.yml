# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        nexus_repos.yml                                                   #
# Version:                                                                        #
#               2025-04-07 espy                                                   #
# Create Date:  2025-04-07                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

registries:
  - name: quay-io
    proxy:
      remoteUrl: "https://quay.io"
    httpClient:
      authentication:
        type: username
        username: "{{ quay_username }}"
        password: "{{ quay_password }}"
    docker:
      httpPort: 8082
  - name: registry-redhat-io
    proxy:
      remoteUrl: "https://registry.redhat.io"
    httpClient:
      authentication:
        type: username
        username: "{{ registry_redhat_io_username }}"
        password: "{{ registry_redhat_io_password }}"
    docker:
      httpPort: 8083
  - name: registry-connect-redhat-com
    proxy:
      remoteUrl: "https://registry.connect.redhat.com"
    httpClient:
      authentication:
        type: username
        username: "{{ registry_connect_redhat_com_username }}"
        password: "{{ registry_connect_redhat_com_password }}"
    docker:
      httpPort: 8084
  - name: registry-ci-openshift-org
    proxy:
      remoteUrl: "https://registry.ci.openshift.org"
    docker:
      httpPort: 8085
  - name: docker-io
    proxy:
      remoteUrl: "https://registry-1.docker.io"
    docker:
      httpPort: 8086
  - name: icr-io
    proxy:
      remoteUrl: "https://icr.io"
    docker:
      httpPort: 8087
  - name: k8s-gcr-io
    proxy:
      remoteUrl: "https://k8s.gcr.io"
    docker:
      httpPort: 8088
  - name: gcr-io
    proxy:
      remoteUrl: "https://gcr.io"
    docker:
      httpPort: 8089
  - name: registry-access-redhat-com
    proxy:
      remoteUrl: "https://registry.access.redhat.com"
    docker:
      httpPort: 8090
  - name: cp-stg-icr-io
    proxy:
      remoteUrl: "https://cp.stg.icr.io"
    docker:
      httpPort: 8091
  - name: ghcr-io
    proxy:
      remoteUrl: "https://ghcr.io"
    docker:
      httpPort: 8092
  - name: registry-gitlab-com
    proxy:
      remoteUrl: "https://registry.gitlab.com"
    docker:
      httpPort: 8093
  - name: registry-k8s-io
    proxy:
      remoteUrl: "https://registry.k8s.io"
    docker:
      httpPort: 8094
  - name: nvcr-io
    proxy:
      remoteUrl: "https://nvcr.io"
    docker:
      httpPort: 8095

yum_repositories:
  - name: rhel-9.4-yum
    proxy:
      remoteUrl: "https://nexus.global.lmco.com/repository/Galaxy/rhel9.4/"
    httpClient:
      authentication:
        type: username
        username: "{{ global_nexus_user }}"
        password: "{{ global_nexus_token }}"
  - name: gitlab-yum
    proxy:
      remoteUrl: "https://packages.gitlab.com/"

raw_repositories:
  - name: builds-coreos-fedoraproject-org
    proxy:
      remoteUrl: "https://builds.coreos.fedoraproject.org/"
  - name: mirror-openshift-com
    proxy:
      remoteUrl: "https://mirror.openshift.com/"
  - name: rhcos-mirror-openshift-com
    proxy:
      remoteUrl: "https://rhcos.mirror.openshift.com/"
  - name: github-com
    proxy:
      remoteUrl: "https://github.com/"
  - name: gitlab-com
    proxy:
      remoteUrl: "https://gitlab.com/"
