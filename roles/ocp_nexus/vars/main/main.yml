# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

shell_color: "{{ lookup('env', 'SHELL_COLOR') | default('0;93', true) }}"
http_proxy: http://proxy-zsgov.external.lmco.com:80
https_proxy: http://proxy-zsgov.external.lmco.com:80
lmca_certs_url: https://crl.external.lmco.com/trust/pem/lmchain/LMChain_pem.pem
nexus_http_port: 8081
no_proxy_addresses:
  - .lmco.com
  - localhost
  - 127.0.0.1
ca_cert: /etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
server_name: galaxy-registry
galaxy_home: "/home/<USER>"
haproxy_directory: "{{ galaxy_home }}/haproxy"
haproxy_certs_directory: "{{ haproxy_directory }}/certs"
haproxy_config_file: "{{ haproxy_directory }}/haproxy.conf"
haproxy_container_name: haproxy
haproxy_image_name: harbor.global.lmco.com/ext.hub.docker.com/library/haproxy:3.0.3
haproxy_tls_pem_file: "{{ haproxy_certs_directory }}/tls.pem"
nexus_container_name: nexus
nexus_image_name: harbor.global.lmco.com/ext.hub.docker.com/sonatype/nexus3:latest
nexus_data_dir: "{{ galaxy_home }}/nexus"
galaxy_nexus_url: "https://{{ galaxy_nexus_domain }}"
galaxy_nexus_api: "{{ galaxy_nexus_url }}/service/rest/v1"
haproxy_container_cert_file: /usr/local/etc/haproxy/tls.pem
default_body: &default_body
  online: true
  type: proxy
  storage:
    blobStoreName: default
    strictContentTypeValidation: true
    writePolicy: ALLOW
  cleanup: null
  proxy:
    contentMaxAge: -1
    metadataMaxAge: 1440
  negativeCache:
    enabled: true
    timeToLive: 1440
  httpClient:
    blocked: false
    autoBlock: true
    connection:
      retries: null
      userAgentSuffix: null
      timeout: null
      enableCircularRedirects: false
      enableCookies: false
      useTrustStore: true
  routingRuleName: null

default_yum_body:
  << : *default_body
  format: yum
default_raw_body:
  << : *default_body
  format: raw
  raw:
    contentDisposition: ATTACHMENT
default_docker_body:
  << : *default_body
  format: docker
  docker:
    v1Enabled: false
    forceBasicAuth: true
    httpsPort: null
    subdomain: null
  dockerProxy:
    indexType: REGISTRY
    indexUrl: null
    cacheForeignLayers: false
    foreignLayerUrlWhitelist: []

list_of_destinations: >-
  {{
    registries
    | map(attribute='proxy.remoteUrl')
    | map('urlsplit', 'netloc')
    | map('regex_replace', '\\.', '-')
    | map('regex_replace', '$', '.' + galaxy_nexus_domain)
  }}

nexus_repositories: >
  {{
    registries |
    map('combine', default_docker_body, recursive=True) +
    yum_repositories |
    map('combine', default_yum_body, recursive=True) +
    raw_repositories |
    map('combine', default_raw_body, recursive=True)
  }}
