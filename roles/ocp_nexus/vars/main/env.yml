# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        env.yml                                                           #
# Version:                                                                        #
#               2025-04-07 espy                                                   #
# Create Date:  2025-04-07                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

ami_id: "{{ lookup('env', 'AMI_ID') }}"
subnet_id: "{{ lookup('env', 'SUBNET_ID') }}"
server_ip: "{{ lookup('env', 'SERVER_IP') }}"
server_username: "{{ lookup('env', 'SERVER_USERNAME') }}"
server_password: "{{ lookup('env', 'SERVER_PASSWORD') }}"
public_ssh_key: "{{ lookup('env', 'PUBLIC_SSH_KEY') }}"
galaxy_key: "{{ lookup('env', 'GALAXY_KEY') }}"
global_nexus_user: "{{ lookup('env', 'GLOBAL_NEXUS_USER') }}"
global_nexus_token: "{{ lookup('env', 'GLOBAL_NEXUS_TOKEN') }}"
global_nexus_url: "{{ lookup('env', 'GLOBAL_NEXUS_URL') }}"
full_cert: "{{ lookup('env', 'FULL_CERT') }}"
galaxy_nexus_domain: "{{ lookup('env', 'GALAXY_NEXUS_DOMAIN') }}"
galaxy_nexus_username: "{{ lookup('env', 'GALAXY_NEXUS_USER') }}"
galaxy_nexus_password: "{{ lookup('env', 'GALAXY_NEXUS_PASSWORD') }}"
quay_username: "{{ lookup('env', 'QUAY_USERNAME') }}"
quay_password: "{{ lookup('env', 'QUAY_PASSWORD') }}"
gitlab_url: "{{ lookup('env', 'GITLAB_URL') }}"
gitlab_runner_token: "{{ lookup('env', 'GITLAB_RUNNER_TOKEN') }}"
registry_redhat_io_username: "{{ lookup('env', 'REGISTRY_REDHAT_IO_USERNAME') }}"
registry_redhat_io_password: "{{ lookup('env', 'REGISTRY_REDHAT_IO_PASSWORD') }}"
registry_connect_redhat_com_username: "{{ lookup('env', 'REGISTRY_CONNECT_REDHAT_COM_USERNAME') }}"
registry_connect_redhat_com_password: "{{ lookup('env', 'REGISTRY_CONNECT_REDHAT_COM_PASSWORD') }}"
