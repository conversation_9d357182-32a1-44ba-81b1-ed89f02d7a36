# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        gitlab.yml                                                        #
# Version:                                                                        #
#               2025-04-07 espy                                                   #
# Create Date:  2025-04-07                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

gitlab_runner_description: "Runner on {{ galaxy_nexus_domain }} aws"
gitlab_runner_tags: "aws,{{ galaxy_nexus_domain }}"
gitlab_runner_executor: "docker"
gitlab_runner_docker_image: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest"
gitlab_runner_docker_volumes: ["/cache"]
podman_config_dir: "/etc/gitlab-runner/podman"
