# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        gitlab.yml                                                        #
# Version:                                                                        #
#               2025-04-07 espy                                                   #
# Create Date:  2025-04-07                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: gitlab | Add GitLab Runner repository
  ansible.builtin.yum_repository:
    name: gitlab-runner
    description: GitLab Runner Repository
    baseurl: https://{{ galaxy_nexus_domain }}/repository/gitlab-yum/runner/gitlab-runner/el/9/$basearch
    gpgcheck: false
    enabled: true

- name: gitlab | Install GitLab Runner package
  ansible.builtin.yum:
    name: gitlab-runner
    state: present

- name: gitlab | Create Podman configuration directory
  ansible.builtin.file:
    path: "{{ podman_config_dir }}"
    state: directory
    mode: '0755'

- name: gitlab | Configure GitLab Runner to use Podman
  ansible.builtin.copy:
    dest: "/etc/gitlab-runner/config.toml.template"
    content: |
      [[runners]]
        [runners.docker]
          tls_verify = true
          image = "{{ gitlab_runner_docker_image }}"
          privileged = true
          disable_entrypoint_overwrite = false
          oom_kill_disable = false
          disable_cache = false
          volumes = {{ gitlab_runner_docker_volumes | to_json }}
          shm_size = 0
          runtime = "podman"
    backup: true
    mode: "0644"

- name: gitlab | Create Podman socket configuration for systemd
  ansible.builtin.copy:
    dest: "{{ podman_config_dir }}/podman.service"
    content: |
      [Unit]
      Description=Podman API Service
      Requires=podman.socket
      After=podman.socket
      Documentation=man:podman-system-service(1)

      [Service]
      Type=oneshot
      ExecStart=/usr/bin/true
      RemainAfterExit=yes

      [Install]
      WantedBy=multi-user.target default.target
    backup: true
    mode: "0644"

- name: gitlab | Create Podman socket for systemd
  ansible.builtin.copy:
    dest: "{{ podman_config_dir }}/podman.socket"
    content: |
      [Unit]
      Description=Podman API Socket
      Documentation=man:podman-system-service(1)

      [Socket]
      ListenStream=/run/podman/podman.sock
      SocketMode=0660
      SocketUser=root
      SocketGroup=gitlab-runner

      [Install]
      WantedBy=sockets.target
    backup: true
    mode: "0644"

- name: gitlab | Link Podman systemd files to system location
  ansible.builtin.file:
    src: "{{ podman_config_dir }}/{{ item }}"
    dest: "/etc/systemd/system/{{ item }}"
    state: link
  loop:
    - podman.service
    - podman.socket

- name: gitlab | Add gitlab-runner user to required groups
  ansible.builtin.user:
    name: gitlab-runner
    groups: systemd-journal
    append: true

- name: gitlab | Reload systemd to recognize new services
  ansible.builtin.systemd:
    daemon_reload: true

- name: gitlab | Enable and start Podman socket
  ansible.builtin.systemd:
    name: podman.socket
    state: started
    enabled: true

- name: gitlab | Ensure GitLab Runner service is enabled and started
  ansible.builtin.systemd:
    name: gitlab-runner
    state: started
    enabled: true

- name: gitlab | Set DOCKER_HOST environment variable for gitlab-runner
  ansible.builtin.lineinfile:
    path: /etc/sysconfig/gitlab-runner
    regexp: '^DOCKER_HOST='
    line: 'DOCKER_HOST=unix:///run/podman/podman.sock'
    create: true
    mode: "0644"

- name: gitlab | Check if GitLab Runner is already registered
  ansible.builtin.command: gitlab-runner list
  register: runner_list
  changed_when: false
  failed_when: false

- name: gitlab | Register GitLab Runner with Podman
  ansible.builtin.command: >
    gitlab-runner register
    --non-interactive
    --url {{ gitlab_url }}
    --registration-token {{ gitlab_runner_token }}
    --description "{{ gitlab_runner_description }}"
    --tag-list "{{ gitlab_runner_tags }}"
    --executor "{{ gitlab_runner_executor }}"
    --docker-image "{{ gitlab_runner_docker_image }}"
    --docker-volumes "{{ gitlab_runner_docker_volumes | join(',') }}"
    --env "DOCKER_HOST=unix:///run/podman/podman.sock"
  when: gitlab_url not in runner_list.stdout
  changed_when: true

- name: gitlab | Restart GitLab Runner
  ansible.builtin.systemd:
    name: gitlab-runner
    state: restarted
