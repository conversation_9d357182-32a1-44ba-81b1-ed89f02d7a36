# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        settings.yml                                                      #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name:  settings | Change hostname
  ansible.builtin.hostname:
    name: "{{ server_name }}"
  become: true
  become_user: root

- name:  settings | Change prompt color
  ansible.builtin.blockinfile:
    path: /etc/bashrc
    marker: "# {mark} ANSIBLE MANAGED BLOCK FOR PS1 UPDATE"
    block: |
      if [ -n "$PS1" ]; then
        export PS1="\[\033[{{ shell_color }}m\]\u@\h\[\033[00m\]:\w\$ "
      fi
  become: true
  become_user: root

- name:  settings | Setup LMCO Proxy
  ansible.builtin.include_role:
    name: lmco.linux.linux_proxy
    public: true
  vars:
    galaxy_linux:
      http_proxy: "{{ http_proxy }}"
      https_proxy: "{{ https_proxy }}"
      no_proxy: "{{ no_proxy_addresses }}"

- name:  settings | Install LMCO Certificate Authority
  ansible.builtin.include_role:
    name: lmco.linux.linux_cert_auth
    public: true
  vars:
    galaxy_linux:
      ca_cert: "{{ ca_cert }}"

- name: settings | Find all files repo directory
  ansible.builtin.find:
    paths: /etc/yum.repos.d
    file_type: file
  register: files_found

- name: settings | Remove the found files
  ansible.builtin.file:
    path: "{{ file.path }}"
    state: absent
  loop: "{{ files_found.files }}"
  loop_control:
    loop_var: file
  become: true
  become_user: root

- name: settings | Copy over repos
  ansible.builtin.template:
    src: galaxy.repo.j2
    dest: /etc/yum.repos.d/galaxy.repo
    mode: "0644"
  become: true
  become_user: root

- name: settings | Add nexus urls to etc hosts
  ansible.builtin.lineinfile:
    path: /etc/hosts
    regexp: '^127\.0\.0\.1'
    line: >
      127.0.0.1 localhost localhost.localdomain localhost4 localhost4.localdomain4
      {{ (list_of_destinations | join(' ')) }} {{ galaxy_nexus_domain }}
  become: true
  become_user: root

- name: settings | Allow priviliged ports
  ansible.posix.sysctl:
    name: net.ipv4.ip_unprivileged_port_start
    value: 23
    state: present
    reload: true
  become: true
  become_user: root
