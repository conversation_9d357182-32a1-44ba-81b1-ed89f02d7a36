# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        nexus.yml                                                         #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: nexus | Install basic packages
  ansible.builtin.yum:
    name:
      - podman
    state: present
  become: true
  become_user: root

- name: nexus | Ensure lingering enabled for podman user
  ansible.builtin.command:
    cmd: loginctl enable-linger
    creates: /var/lib/systemd/linger/{{ server_username }}

- name: nexus | Create necessary directories
  ansible.builtin.file:
    path: "{{ directories }}"
    mode: "0755"
    state: directory
  loop:
    - "{{ haproxy_directory }}"
    - "{{ haproxy_certs_directory }}"
    - "{{ nexus_data_dir }}"
  loop_control:
    loop_var: directories

- name: nexus | Create haproxy.cfg file
  ansible.builtin.template:
    src: haproxy.cfg.j2
    dest: "{{ haproxy_config_file }}"
    mode: '0644'
    lstrip_blocks: true

- name: nexus | Setup certs for nexus
  ansible.builtin.copy:
    dest: "{{ haproxy_certs_directory }}/tls.pem"
    mode: "0644"
    content: |
      {{ lookup('file', full_cert) }}

- name: nexus | Run haproxy container
  containers.podman.podman_container:
    name: "{{ haproxy_container_name }}"
    image: "{{ haproxy_image_name }}"
    state: created
    network: host
    volumes:
      - "{{ haproxy_config_file }}:{{ haproxy_config_filepath }}:z"
      - "{{ haproxy_tls_pem_file }}:{{ haproxy_container_cert_file }}:z"
    generate_systemd:
      container_prefix: ""
      separator: ""
      new: true
      path: ~/.config/systemd/user
  vars:
    haproxy_config_filepath: /usr/local/etc/haproxy/haproxy.cfg

- name: nexus | Start and enabled new podman registry container
  ansible.builtin.systemd:
    name: "{{ haproxy_container_name }}"
    state: restarted
    enabled: true
    daemon_reload: true
    scope: user

- name: nexus | Start new nexus container
  containers.podman.podman_container:
    name: "{{ nexus_container_name }}"
    image: "{{ nexus_image_name }}"
    uidmap:
      - "0:1:200"
      - "200:0:1"
      - "201:201:10000"
    state: created
    network: slirp4netns
    publish: "{{ registries | map(attribute='docker.httpPort') | map('regex_replace', '^(.*)$', '\\1:\\1') | list + [nexus_mapping] }}"
    volumes:
      - "{{ nexus_data_dir }}:/nexus-data:Z"
    healthcheck: curl localhost:{{ nexus_http_port }}/service/rest/v1/status -f
    healthcheck_interval: 30s
    healthcheck_retries: 5
    healthcheck_start_period: 5m
    healthcheck_timeout: 10s
    healthcheck_failure_action: restart
    generate_systemd:
      container_prefix: ""
      separator: ""
      start_timeout: 600
      new: true
      path: ~/.config/systemd/user
  vars:
    nexus_mapping: "{{ nexus_http_port }}:{{ nexus_http_port }}"

- name: nexus | Start and enabled galaxy container
  ansible.builtin.systemd:
    name: "{{ nexus_container_name }}"
    state: restarted
    enabled: true
    daemon_reload: true
    scope: user

- name: nexus | Wait for nexus to become active at https://{{ galaxy_nexus_domain }}/service/rest/v1/status
  ansible.builtin.uri:
    url: https://{{ galaxy_nexus_domain }}/service/rest/v1/status
    method: "GET"
    return_content: true
  register: nexus_response
  retries: 20
  delay: 30
  until: nexus_response.status == 200
