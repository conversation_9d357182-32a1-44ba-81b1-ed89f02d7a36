# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Create nexus server
  lmco.openshift.ec2_create:
    ami_id: "{{ ami_id }}"
    subnet_id: "{{ subnet_id }}"
    ipv4_address: "{{ server_ip }}"
    name: "{{ server_name }}"
    username: "{{ server_username }}"
    password: "{{ server_password }}"
    public_ssh_key: "{{ public_ssh_key }}"
    volume_size: 900

- name: Waiting for SSH to open on boot
  ansible.builtin.wait_for:
    host: "{{ server_ip }}"
    port: 22
    delay: 0
    state: started
    timeout: 600
    sleep: 10

- name: Setup server
  ansible.builtin.include_tasks:
    file: settings.yml
    apply:
      delegate_to: "{{ server_ip }}"
      vars:
        ansible_ssh_user: "{{ server_username }}"
        ansible_ssh_private_key_file: "{{ galaxy_key }}"
        ansible_ssh_extra_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'

- name: Setup nexus server
  ansible.builtin.include_tasks:
    file: nexus.yml
    apply:
      delegate_to: "{{ server_ip }}"
      vars:
        ansible_ssh_user: "{{ server_username }}"
        ansible_ssh_private_key_file: "{{ galaxy_key }}"
        ansible_ssh_extra_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'

- name: Configure Nexus
  ansible.builtin.include_tasks:
    file: configure_nexus.yml
    apply:
      delegate_to: "{{ server_ip }}"
      vars:
        ansible_ssh_user: "{{ server_username }}"
        ansible_ssh_private_key_file: "{{ galaxy_key }}"
        ansible_ssh_extra_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'

- name: Configure Gitlab
  ansible.builtin.include_tasks:
    file: gitlab.yml
    apply:
      delegate_to: "{{ server_ip }}"
      become: true
      become_user: root
      vars:
        ansible_ssh_user: "{{ server_username }}"
        ansible_ssh_private_key_file: "{{ galaxy_key }}"
        ansible_ssh_extra_args: '-o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no'
