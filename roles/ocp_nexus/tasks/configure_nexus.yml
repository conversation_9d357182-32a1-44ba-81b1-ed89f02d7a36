# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        configure_nexus.yml                                               #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: configure_nexus | Get auto created password
  ansible.builtin.slurp:
    path: "{{ nexus_data_dir }}/admin.password"
  register: encoded_galaxy_nexus_password

- name: configure_nexus | Change default password
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/security/users/{{ galaxy_nexus_username }}/change-password"
    method: "PUT"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ encoded_galaxy_nexus_password.content | b64decode }}"
    force_basic_auth: true
    body: "{{ galaxy_nexus_password }}"
    body_format: raw
    headers:
      Content-Type: "text/plain"
    status_code:
      - 204

- name: configure_nexus | Write down new default password in file
  ansible.builtin.copy:
    content: "{{ galaxy_nexus_password }}"
    dest: "{{ nexus_data_dir }}/admin.password"
    mode: "0600"

- name: configure_nexus | Add anonymous access
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/security/anonymous"
    method: "PUT"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      enabled: true
      userId: anonymous
      realmName: NexusAuthorizingRealm
    body_format: json

- name: configure_nexus | Get list of existing repositories
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories"
    method: "GET"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    return_content: true
  register: list_of_existing_repos

- name: configure_nexus | Delete existing repositories
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories/{{ repo_to_delete.name }}"
    method: "DELETE"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    status_code:
      - 204
  loop: "{{ list_of_existing_repos.json }}"
  loop_control:
    loop_var: repo_to_delete
    label: "{{ repo_to_delete.name }}"

- name: configure_nexus | Get ID of Outreach Managment Capability
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_url }}/service/extdirect"
    method: "POST"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      action: capability_Capability
      method: read
      data:
      type: rpc
      tid: 997
    body_format: json
    return_content: true
  register: capabilties_response

- name: configure_nexus | Disable Outreach Management capability
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_url }}/service/extdirect"
    method: "POST"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      action: capability_Capability
      method: disable
      data:
        - "{{ outreach_capability_id }}"
      type: rpc
      tid: 998
    body_format: json
  vars:
    outreach_capability_id: capabilties_response.json.result.data | selectattr('typeId', 'equalto', 'OutreachManagementCapability') | map(attribute='id')

- name: configure_nexus | Fetch CA certificates from URL
  ansible.builtin.uri:
    url: "{{ lmca_certs_url }}"
    return_content: true
  register: lmca_certs_response

- name: configure_nexus | Add Lockheed CAs
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/security/ssl/truststore"
    method: "POST"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body: "{{ lmca }}"
    body_format: json
    status_code:
      - 201
      - 409
  loop: "{{ lockheed_ca_list }}"
  loop_control:
    loop_var: lmca
  vars:
    cert_regex: '-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----'
    lockheed_ca_list: "{{ lmca_certs_response.content | regex_findall(cert_regex, multiline=True) }}"

- name: configure_nexus | Create repositories
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories/{{ repo.format }}/{{ repo.type }}"
    method: "POST"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body: "{{ repo }}"
    body_format: json
    status_code:
      - 201
      - 409
  loop: "{{ nexus_repositories }}"
  loop_control:
    loop_var: repo

- name: configure_nexus | Add LMCO Proxy
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_url }}/service/extdirect"
    method: "POST"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      action: coreui_HttpSettings
      method: update
      data:
        - timeout: "300"
          httpEnabled: true
          httpHost: "{{ http_proxy | urlsplit('hostname') }}"
          httpPort: "80"
          httpAuthEnabled: false
          httpsEnabled: true
          httpsHost: "{{ https_proxy | urlsplit('hostname') }}"
          httpsPort: "80"
          httpsAuthEnabled: false
          nonProxyHosts:
            - "*.lmco.com"
          retries: "2"
      type: rpc
      tid: 999
    body_format: json
    status_code:
      - 200

- name: configure_nexus | Accept the dam EULA
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/system/eula"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
  register: eula_get_response

- name: configure_nexus | Accept the dam EULA
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/system/eula"
    method: "POST"
    user: "{{ galaxy_nexus_username }}"
    password: "{{ galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      accepted: true
      disclaimer: "{{ disclaimer }}"
    body_format: json
    status_code:
      - 204
      - 500
  register: eula_response
  failed_when: eula_response.status == 500 and already_accepted not in eula_response.body
  vars:
    disclaimer: "{{ eula_get_response.json.disclaimer }}"
    already_accepted: EULA has already been accepted
