---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-05-21 Initial                                                #
# Create Date:  2025-05-21                                                        #
# Author:       Ch<PERSON><PERSON><PERSON> (<EMAIL>)               #
# Description:                                                                    #
#               Initial commit for main.yml                                       #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

galaxy_info:
  author: Chembonuh N.C <<EMAIL>>
  description: OpenShift Verify role
  company: Lockheed Martin Galaxy Automation Team
  issue_tracker_url: https://ebstools-jira.us.lmco.com/projects/XBA_GALAXY_AUTO
  license: LICENSE
  min_ansible_version: "2.12.0"

  # ----------------------------------------------------------------------------- #
  # If this is a Container Enabled role, provide minimum Ansible Container version #
  # ----------------------------------------------------------------------------- #
  # min_ansible_container_version:

  # ----------------------------------------------------------------------------- #
  # Provide a list of supported platforms; for each platform a list of versions   #
  # If you don't wish to enumerate all versions for a platform, use 'all'.        #
  # To view available platforms and versions (or releases), visit:                #
  # https://galaxy.ansible.com/api/v1/platforms/                                  #
  # ----------------------------------------------------------------------------- #
  platforms:
    - name: EL    # https://galaxy.ansible.com/api/v1/platforms/?page=6
      versions: [all]

# ------------------------------------------------------------------------------- #
# List tags for your role here, one per line. A tag is a keyword that describes   #
# and categorizes the role. Users find roles by searching for tags. Be sure to    #
# NOTE: A tag is limited to a single word comprised of alphanumeric characters.   #
#       Maximum 20 tags per role.                                                 #
# ------------------------------------------------------------------------------- #
  galaxy_tags:
    - lmco
    - openshift
    - validation
    - ssl
    - cluster
    - development

# ------------------------------------------------------------------------------- #
# Role dependencies here, one per line. Be sure to remove the '[]' above,         #
# if you add dependencies to this list.                                           #
# ------------------------------------------------------------------------------- #
dependencies:
  - role: lmco.openshift.ocp_login

# ------------------------------------------------------------------------------- #
# Role Configuration                                                              #
# ------------------------------------------------------------------------------- #
allow_duplicates: false
