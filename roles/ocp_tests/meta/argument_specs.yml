---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2025-05-21 Initial                                                #
# Create Date:  2025-05-21                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)               #
# Description:                                                                    #
#               Initial commit for argument_specs.yml                             #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

argument_specs:
  main:
    short_description: tests role for OpenShift
    description:
      - lmco.openshift.ocp_tests
    author:
      - Chembonuh N.C <<EMAIL>>
    options:
      galaxy_role:
        type: dict
        description: The dictionary providing the tags that will be used to determine what actions to execute
        required: true
        options:
          name:
            type: str
            required: true
            choices:
              - lmco.openshift.ocp_tests
            description: The name of the role that is being executed.
          tags:
            type: list
            required: true
            description: Tags to determine which tasks to execute.
            choices:
              - installation_validation
              - molecule
      console_url:
        type: str
        description: The URL of the OpenShift console to validate.
        required: true
        default: "input url here"
      validate_certificate:
        type: bool
        description: Whether to validate the console's SSL certificate.
        required: false
        default: true
      check_cluster_operators:
        type: bool
        description: Whether to check the status of OpenShift cluster operators.
        required: false
        default: true
      check_core_services:
        type: bool
        description: Whether to check the status of core services like coredns and haproxy.
        required: false
        default: true
