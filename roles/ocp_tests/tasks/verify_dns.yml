---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        verify_dns.yml                                                    #
# Version:                                                                        #
#               2025-05-21 Initial                                                #
# Create Date:  2025-05-21                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)               #
# Description:                                                                    #
#               Initial commit for verify_dns.yml                                 #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: verify_dns | Check services
  block:
    - name: verify_dns |  Check DNS server response
      ansible.builtin.command: "dig +short example.com @{{ ocp.baremetal_network_dns_ip }} -p 53"
      register: dns_response
      failed_when: dns_response.rc != 0
      changed_when: false

    - name: verify_dns |  Check HAProxy test route
      ansible.builyin.uri:
        url: "http://{{ ocp.haproxy_test_route }}" # To do replace ocp.haproxy_test_route with actual
        method: GET
        status_code: 200
      register: haproxy_response
      failed_when: haproxy_response.status_code != 200

    - name: verify_dns | Check CND (DNS) server response
      ansible.builtin.command: "dig +short example.com @{{ ocp.baremetal_network_dns_ip }} -p {{ dns_port }}"
      register: cnd_response
      failed_when: cnd_response.rc != 0
      changed_when: false
