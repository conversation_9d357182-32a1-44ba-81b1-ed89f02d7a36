---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        verify_nexus.yml                                                  #
# Version:                                                                        #
#               2025-05-21 Initial                                                #
# Create Date:  2025-05-21                                                        #
# Author:       <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)               #
# Description:                                                                    #
#               Initial commit for verify_nexus.yml                               #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: "verify_nexus | Test Nexus API availability"
  ansible.builtin.uri:
    url: http://{{ ocp.galaxy_nexus_hostname }}:{{ ocp.nexus_http_port }}/service/rest/v1/status
    method: "GET"
    return_content: false
  register: nexus_response
  retries: 20
  delay: 30
  until: nexus_response.status == 200

- name: "verify_nexus | Validate Nexus API response"
  ansible.builtin.assert:
    that:
      - nexus_api_response.status == 200
    fail_msg: "Nexus API is not reachable."
    success_msg: "Nexus API is reachable."

# ToDO - figure out a way create a test repo in Nexus
- name: "verify_nexus | Pull image from Nexus"
  ansible.builtin.shell: |
    set -o pipefail
    docker login {{ ocp.galaxy_nexus_hostname | urlsplit('hostname') }}:{{ ocp.nexus_http_port }} \
      -u {{ ocp.galaxy_nexus_username }} -p {{ ocp.galaxy_nexus_password }}
    docker pull {{ ocp.galaxy_nexus_hostname | urlsplit('hostname') }}:{{ ocp.nexus_http_port }}/test-repo/alpine:latest
  register: pull_image_result
  failed_when: "'Error' in pull_image_result.stderr"
  changed_when: false

- name: "verify_nexus | Validate image pull success"
  ansible.builtin.assert:
    that:
      - pull_image_result.rc == 0
    fail_msg: "Image pull failed with error: {{ pull_image_result.stderr }}"
    success_msg: "Image pulled successfully from Nexus."
