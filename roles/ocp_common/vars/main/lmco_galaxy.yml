# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        lmco_galaxy.yml                                                   #
# Version:                                                                        #
#               2023-10-11 <PERSON><PERSON>, <PERSON>                                           #
#               2023-10-17 espy                                                   #
#               2023-10-19 espy                                                   #
#               2023-10-25 espy                                                   #
#               2023-11-01 espy                                                   #
#               2023-11-02 espy                                                   #
#               2023-11-02 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-05 espy                                                   #
#               2023-11-06 espy                                                   #
#               2023-11-06 Sam<PERSON>, <PERSON>                                           #
#               2023-11-07 espy                                                   #
#               2023-11-09 espy                                                   #
#               2023-11-15 espy                                                   #
#               2023-11-21 espy                                                   #
#               2023-11-28 espy                                                   #
#               2023-11-28 <PERSON>, <PERSON> J                                          #
#               2023-12-11 espy                                                   #
#               2023-12-12 <PERSON><PERSON>, <PERSON>                                           #
#               2023-12-19 espy                                                   #
#               2024-01-04 espy                                                   #
#               2024-01-08 espy                                                   #
#               2024-01-11 espy                                                   #
#               2024-01-11 <PERSON><PERSON>, <PERSON>                                           #
#               2024-01-15 <PERSON>, <PERSON> J                                          #
#               2024-01-16 <PERSON>, <PERSON> J                                          #
#               2024-01-17 espy                                                   #
#               2024-01-17 <PERSON>, <PERSON> J                                          #
#               2024-01-18 <PERSON>, <PERSON> J                                          #
#               2024-01-19 Espinoza, <PERSON> M                                    #
#               2024-01-23 espy                                                   #
#               2024-01-24 espy                                                   #
#               2024-01-25 espy                                                   #
#               2024-01-26 espy                                                   #
#               2024-01-30 espy                                                   #
#               2024-02-06 Hunter, Ian J                                          #
#               2024-02-08 espy                                                   #
#               2024-02-10 espy                                                   #
#               2024-02-12 espy                                                   #
#               2024-02-13 espy                                                   #
#               2024-02-14 Sams, Landon                                           #
#               2024-02-15 espy                                                   #
#               2024-02-16 Hunter, Ian J                                          #
#               2024-02-18 Hunter, Ian J                                          #
#               2024-02-27 Grosman, Andrew T                                      #
#               2024-03-04 espy                                                   #
#               2024-03-05 espy                                                   #
#               2024-03-20 espy                                                   #
#               2024-04-03 espy                                                   #
#               2024-04-15 espy                                                   #
#               2024-04-16 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2024-04-28 espy                                                   #
#               2024-04-30 Hunter, Ian J                                          #
#               2024-05-06 espy                                                   #
#               2024-05-07 Sams, Landon                                           #
#               2024-05-08 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-06-05 espy                                                   #
#               2024-06-06 espy                                                   #
#               2024-06-11 espy                                                   #
#               2024-06-18 espy                                                   #
#               2024-06-19 espy                                                   #
#               2024-06-20 espy                                                   #
#               2024-07-03 espy                                                   #
#               2024-07-16 espy                                                   #
#               2024-07-17 espy                                                   #
#               2024-07-18 espy                                                   #
#               2024-07-24 Sams, Landon                                           #
#               2024-07-29 espy                                                   #
#               2024-08-07 espy                                                   #
#               2024-08-12 Espinoza, Michael M                                    #
#               2024-08-12 espy                                                   #
#               2024-08-21 espy                                                   #
#               2024-08-28 espy                                                   #
#               2024-08-29 Espinoza, Michael M                                    #
#               2024-08-29 espy                                                   #
#               2024-09-05 espy                                                   #
#               2024-09-09 espy                                                   #
#               2024-09-11 espy                                                   #
#               2024-10-29 Hunter, Ian J                                          #
#               2024-11-11 Hunter, Ian J                                          #
#               2024-11-21 Sams, Landon                                           #
#               2025-01-24 Sams, Landon                                           #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-11                                                        #
# Author:       Sams, Landon                                                      #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

default_ocp:
  http_proxy: http://proxy-zsgov.external.lmco.com:80
  https_proxy: http://proxy-zsgov.external.lmco.com:80
  standard_no_proxy_addresses:
    - .lmco.com
    - localhost
    - 127.0.0.1
  dns_servers:
    - *************
    - *************
  ntp_servers:
    - "ntp.global.lmco.com"
  baremetal_network_existing_servers: []
  bmc_api: redfish
  lmco_ca_url: "https://crl.external.lmco.com/trust/pem/lmchain/LMChain_pem.pem"
  cluster:
    username:
    password:
    api_key:
    host: https://api.{{ galaxy.ocp.hostname }}:6443
  hostname: "{{ galaxy.ocp.hostname }}"
  haproxy_stats_port: "1936"
  nodeport_range: "30000-32767"
  ocp_user: 'kni'
  bastion:
    credentials:
      ocp_user_password: "space-it"
      ocp_user_authorized_public_key_file: "~/.ssh/id_rsa.pub"
      root_password:
    options:
      shell_color: "00"
    disk_part:
      home: 1024 --grow
      opt: 153600
      root: 25600
      swap: 4096
      tmp: 25600
      var_log_audit: 4096
      var_log: 4096
      var_tmp: 4096
      var: 102400
    storage_drives:
      - nvme0n1
      - nvme1n1
    network:
      interfaces: []
      routes:
        config: []
      external_connection_name: lmi
    bmc:
      ipv4_address:
      username:
      password:
      system_id:
      manager_id:
      virtual_media_id:
      type:
    keepalived:
      role:
      password:
      advertising_interval: 1
      baremetal_vrid: 10
      public_vrid: 11

  openshift_version: "{{ galaxy.ocp.version }}"
  okd_version: 4.15.0-0.okd-2024-03-10-010116
  # okd_version: 4.17.0-okd-scos.0
  distro: >-
    {{
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first == 'redhat_openshift'
      ) and 'ocp'
      or
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='provider') | first == 'okd'
      ) and 'okd'
      or
      'ocp'
    }}

  galaxy_nexus_username: "{{ galaxy.nexus.username }}"
  galaxy_nexus_password: "{{ galaxy.nexus.password }}"
  rhel_major_version: "{{ galaxy.rhel.major_version }}"
  rhel_minor_version: "{{ galaxy.rhel.minor_version }}"
  rhel_patch_version: "{{ galaxy.rhel.patch_version }}"
  additional_no_proxies: []
  sso_issuer:
  nmstate: []

  # Defaults
  # Names and locations
  ca_cert: /etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
  galaxy_ca_cert: /etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
  nodes_mirror_devices: []
  nodes_tang_servers: []
  nodes_tpm_encryption: true
  ocp_install_files: /galaxy_files
  openshift_install_file: "{{ galaxy.ocp.install_file_name }}"
  openshift_install_path: "/opt/openshift"
  working_dir: "{{ lookup('env', 'PWD') }}"
  default_registry_url: "{{ galaxy.registry.initial_url }}"
  registry_hostname: "{{ galaxy.registry.hostname }}"
  galaxy_nexus_hostname: "{{ galaxy.nexus.hostname }}"
  galaxy_proxy: proxy.galaxy.lmco.com
  galaxy_nexus_repo_name: "{{ galaxy.nexus.repo_name }}"
  verson_vars_file: "{{ galaxy.verson_vars_file_name }}"
  bastion_rhel_packages_tarball: "{{ galaxy.rhel.packages_file_name }}"
  registry_crt_file: "{{ galaxy.registry.crt_file }}"
  nexus_http_port: 8081
  registry_port: 8082
  galaxy_proxy_port: 8083
  virtual_media_bastion_http_listen_port: 8084
  virtual_media_bastion_https_listen_port: 8085
  http_non_root_port: 8086
  https_non_root_port: 8087
  nexus_forward_port: 8088
  registry_forward_port: 8089
  dns_port: 8090
  kube_api_port: 8092
  virtual_media_http_dest_ports:
    - 80
    - 6180
  virtual_media_https_dest_ports:
    - 6183
  nexus_image_file: nexus.tar
  nexus_storage: "{{ galaxy.nexus.storage_file_name }}"
  coreos_password:
  platform: >-
    {{
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='target') | first is defined
      and (
        lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='target') | first == 'aws'
      ) and 'aws'
      or
      lmco_openshift_products is defined
      and lmco_openshift_products | selectattr('name', 'equalto', 'galaxy_platform') | map(attribute='target') | first is defined
      and 'baremetal'
      or
      'baremetal'
    }}
  file_transfer_method: http
  basic_packages:
    - bind-utils
    - tcpdump
    - nmap-ncat
    - nmap
    - podman
    - jq
    - traceroute
    - vim
    - lsof
    - net-tools

  # Flags
  bastion_is_gateway: true
  bootstrap_os: true
  create_install_config: true
  expose_nodeports: true
  force_mirror_registry_redeploy: false
  force_redeployment: false
  install_lmco_ca: true
  local_yum_repo: true
  setup_dns: true
  setup_libvirt: true
  setup_networks: true
  setup_ntp: true
  setup_ocp_user: true
  setup_proxy: false
  setup_coreos_cache: true
  setup_rhsm: false
  simulated_disconnected_env: false
  tpm_encryption: true
  update_dns: true
  validate_certs: true
  developer_run: false
  cluster_lb_enabled: true
  bastion_firewall_enabled: true
  bastion_ntp_enabled: true
  bastion_libvirt_enabled: true
  dev_connected: false
  fips_enabled: true

  # Networking
  openshift_public_ip:
  baremetal_network_bastion_id: 4
  baremetal_network_bootstrap_id: 5
  baremetal_network_temp_bridge_name: 'bm-tmp-br'
  baremetal_network_openshift_api_vip_id: 6
  baremetal_network_openshift_ingress_id: 7
  baremetal_network_unique_bastion_id: "{{ groups.bastion.index(inventory_hostname) + 8 if use_unique_id else default_bastion_id }}"
  baremetal_network_cidr: '*************/24'
  master_nodes_starting_ip: 10
  provisioning_network_bastion_id: 1
  provisioning_network_temp_bridge_name: 'prv-tmp-br'
  provisioning_network_cidr: '*************/24'
  worker_nodes_starting_ip: 15
  openshift_cluster: "{{ galaxy.project_name }}"
  banner_top_text: This system is the property of Lockheed Martin and its subsidiaries and affiliates and is intended for use by authorized users only.
  banner_top_color: "#007a33"
  banner_bottom_text: This system is the property of Lockheed Martin and its subsidiaries and affiliates and is intended for use by authorized users only.
  banner_bottom_color: "#007a33"
  banner_login_text: >
    <p>
      You are accessing a U.S. Government (USG) Information System (IS) that is provided for USG-authorized use only.  By using this IS (which includes
      any device attached to this IS), you consent to the following conditions:
    </p>
    <ul style="list-style-type: disc; margin-bottom: 2em;">
      <li style="margin: .5em 0;">The USG routinely intercepts and monitors communications on this IS for purposes including, but not limited to,
      penetration testing, COMSEC monitoring, network operations and defense, personnel misconduct (PM), law enforcement (LE), and
      counterintelligence (CI) investigations.</li>
      <li style="margin: .5em 0;">At any time, the USG may inspect and seize data stored on this IS.</li>
      <li style="margin: .5em 0;">Communications using, or data stored on, this IS are not private, are subject to routine monitoring, interception,
      and search, and may be disclosed or used for any USG-authorized purpose.</li>
      <li style="margin: .5em 0;">This IS includes security measures (e.g., authentication and access controls) to protect USG interests--not for
      your personal benefit or privacy.</li>
      <li style="margin: .5em 0;">Notwithstanding the above, using this IS does not constitute consent to PM, LE or CI investigative searching or
      monitoring of the content of privileged communications, or work product, related to personal representation or services by attorneys, psychotherapists,
      or clergy, and their assistants. Such communications and work product are private and confidential. See User Agreement for details.</li>
    </ul>
  bastion_baremetal_network_interface_name:
  bastion_provisioning_network_interface_name:
  redhat_registry_pull_secret:
  cluster_cert:
  cluster_key:
  galaxy_builder_lmi_ip: "{{ lookup('env', 'GALAXY_BUILD_LMI_IP') }}"
  galaxy_builder_mgmt_ip: "{{ lookup('env', 'GALAXY_BUILD_MGMT_IP') }}"
  galaxy_builder_port: "{{ lookup('env', 'GALAXY_BUILD_PORT') }}"
  baremetal_list: []
  bastion_ssh_password:
  bastion_host_key_file: ~/.ssh/id_rsa
default_bastion_id: "{{ 8 if (groups.bastion is defined and groups.bastion | length > 1) else 4 }}"
use_unique_id: "{{ 'bastion' in inventory_hostname and (groups.bastion | length > 1) }}"
bastion_ocp:
  bastion: >-
    {{
      lmco_host if (((lmco_host | default({})).abstract | default({})).purpose | default({})) == 'bastion'
      else hostvars['bastion-00'].lmco_host | default({})
    }}

ocp_base: "{{ default_ocp | default({}) |
  combine(galaxy_ocp | default({}), recursive=true) |
  combine(lmco_ocp | default({}), recursive=true) |
  combine(bastion_ocp, recursive=true) |
  combine(cli_ocp | default({}), recursive=true) }}"

default_derived_ocp1:
  bastion_public_network_interface: >-
    {{
      ocp_base.bastion.network.interfaces
      | selectattr('name', 'equalto', ocp_base.bastion.network.external_connection_name)
      | first
      | default({})
    }}
  ocp_install_files_url: http://{{ ocp_base.galaxy_builder_lmi_ip }}:{{ ocp_base.galaxy_builder_port }}
  galaxy_nexus_repo: https://nexus.{{ ocp_base.openshift_cluster }}.galaxy.lmco.com/repository/{{ ocp_base.galaxy_nexus_repo_name }}
  cluster_specific_rhel_iso: "{{ ocp_base.openshift_cluster }}-{{ inventory_hostname }}-rhel.iso"
  kubeconfig_file: "{{ ocp_base.working_dir }}/secrets/kubeconfig"
  nodes_luks_threshold: "{{ ocp_base.nodes_tang_servers | length }}"
  rhel_version: "{{ ocp_base.rhel_major_version }}.{{ ocp_base.rhel_minor_version }}.{{ ocp_base.rhel_patch_version }}"
  ocp_rhel_iso_name: "ocp-rhel-{{ ocp_base.rhel_major_version }}.{{ ocp_base.rhel_minor_version }}-x86_64-dvd.iso"
  rhel_dnf_repo_name: "rhel-{{ ocp_base.rhel_major_version }}.{{ ocp_base.rhel_minor_version }}-x86_64-dnf-repo.tar"
  dnf_isos:
    - iso: rhel-8.6-x86_64-dvd.iso
      name: RHEL8
      sdx_file: rhel-8.6.0.json.bz2
      repos:
        - AppStream
        - BaseOS
      version: 8.6.0
    - iso: rhel-9.5-x86_64-dvd.iso
      name: RHEL9
      sdx_file: rhel-9.5.json.bz2
      repos:
        - AppStream
        - BaseOS
      version: 9.5.0
    - iso: Satellite-6.16.0-rhel-9-x86_64.dvd.iso
      name: Satellite
      sdx_file: rhn_satellite_6.16.json.bz2
      repos:
        - Satellite
      version: 6.16.0
  manifest_files_folder: "{{ ocp_base.working_dir }}/manifest_files"
  baremetal_network_bastion_ip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_unique_bastion_id | int) }}"
  baremetal_network_bootstrap_ip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_bootstrap_id) }}"
  baremetal_network_bastion_vip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_bastion_id) }}"
  baremetal_network_dns_ip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_bastion_id) }}"
  baremetal_network_registry_ip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_bastion_id) }}"
  baremetal_network_gateway_ip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_bastion_id) }}"
  baremetal_network_ntp_ip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_bastion_id) }}"
  baremetal_network_openshift_api_vip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_openshift_api_vip_id) }}"
  baremetal_network_openshift_ingress_vip: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_gen(ocp_base.baremetal_network_openshift_ingress_id) }}"
  baremetal_network_prefix_length: "{{ ocp_base.baremetal_network_cidr | lmco.openshift.cidr_prefix_length }}"
  provisioning_network_prefix_length: "{{ ocp_base.provisioning_network_cidr | lmco.openshift.cidr_prefix_length }}"
  provisioning_network_bastion_ip: "{{ ocp_base.provisioning_network_cidr | lmco.openshift.cidr_gen(ocp_base.provisioning_network_bastion_id) }}"
  bastion_bmc_ip: >-
    {{
      (((ocp_base.bastion.network.interfaces | selectattr('name', 'equalto', 'bmc')
      | first).ipv4 | default({})).address | default([]) | first).ip | default(ocp_base.openshift_public_ip)
    }}
  bastion:
    credentials:
      ocp_user_authorized_public_key: "{{ lookup('lmco.openshift.file2', ocp_base.bastion.credentials.ocp_user_authorized_public_key_file) }}"
    keepalived:
      password: "{{ lookup('password', '/dev/null', chars=['ascii_lowercase', 'digits'], length=20, seed=ocp_base.openshift_cluster) }}"
  setup_provisioning_network: "{{ ocp_base.bmc_api != 'redfish-virtualmedia' }}"
  etcd_backup_settings:
    schedule: "55 23 * * *"
    appLabel: "openshift-backup"
    nexus:
      username: "{{ ocp_base.galaxy_nexus_username }}"
      token: "{{ lookup('password', '/dev/null', chars=['ascii_lowercase', 'digits'], length=15, seed=ocp_base.openshift_cluster) }}"
      repository: https://{{ ocp_base.galaxy_nexus_hostname }}/repository/{{ ocp_base.galaxy_nexus_repo_name }}
      folder: backups

ocp_derived: "{{ ocp_base | default({}) | combine(default_derived_ocp1 | default({}), recursive=true) }}"
