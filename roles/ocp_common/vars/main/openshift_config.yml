# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        openshift_config.yml                                              #
# Version:                                                                        #
#               2023-10-02 <PERSON><PERSON><PERSON>, Wayne                                      #
#               2023-10-17 espy                                                   #
#               2023-10-19 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-02                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON>                                                 #
# Description:                                                                    #
#               This is the file that contains the openshift profile and config   #
#               for the galaxy product. These values should be considered         #
#               "constants" and in most cases and generally not be overridden.    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

openshift_profile:
  install_config:
    api_version: v1
    base_domain: '<domain>'
    cluster_name: '<name>'
    machine_network_cidr:
    network_type: OVNKubernetes
    filename: install-config.yaml
    pull_secret_file: pull-secret.txt
  installer:
    user: ocp_user
    group: ocp_user
    directory:
    secrets:
  proxy:
  pull_secret:
  ntp_servers: []
  dns_servers: []
  secrets:
    folder:
    ingress_cert:
    ingress_key:
    api_cert:
    api_key:
  cluster:
    fips: true
    name: test
    domain: test
    user:  # installer


# this is not needed and will be overwritten by the incoming data.
  hosts:
    - name: master-00
      role: master
      sequence: "00"
      os: coreos
    - name: master-01
      role: master
      sequence: "01"
      os: coreos
    - name: master-02
      role: master
      sequence: "02"
      os: coreos
    - name: worker-00
      role: worker
      sequence: "00"
      os: coreos
    - name: worker-01
      role: worker
      sequence: "01"
      os: coreos
    - name: worker-02
      role: worker
      sequence: "02"
      os: coreos

# ------------------------------------------------------------------------------- #
# OpenShift Configuration Data Structure created by combining the galaxy_profile  #
# ------------------------------------------------------------------------------- #
# Note that this will not merge the embedded list so make sure they are complete
openshift_config: '{{ openshift_profile | ansible.builtin.combine(galaxy_openshift | default({}), list_merge="replace", recursive=true) }}'
