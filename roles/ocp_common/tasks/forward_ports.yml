# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        forward_ports.yml                                                 #
# Version:                                                                        #
#               2024-02-21 espy                                                   #
#               2024-06-06 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2024-02-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: forward_ports | Forward firewall ports
  become: true
  become_user: root
  block:
    - name: forward_ports | Forward all external traffic on port {{ port_to_forward.port }}
      ansible.posix.firewalld:
        permanent: true
        state: enabled
        immediate: true
        zone: "{{ forward_zone }}"
        port_forward:
          - port: "{{ port_to_forward.port }}"
            proto: "{{ port_to_forward.proto }}"
            toport: "{{ port_to_forward.toport }}"
      loop: "{{ port_to_forward.zones }}"
      loop_control:
        loop_var: forward_zone
        label: Forwarding {{ port_to_forward.port }} to {{ port_to_forward.toport }} in firewall zone {{ forward_zone }}

    - name: forward_ports | Forward all internal traffic on port {{ port_to_forward.port }}
      ansible.builtin.command:
        cmd: |
          firewall-cmd
          --permanent
          --direct
          --add-rule ipv4 nat OUTPUT 0 -o lo -d {{ dest_ip }} -p {{ port_to_forward.proto }}
          --dport {{ port_to_forward.port }} -j REDIRECT
          --to-ports {{ port_to_forward.toport }}
      register: firewalld_output
      loop: "{{ port_to_forward.dest_ips }}"
      loop_control:
        loop_var: dest_ip
      changed_when: already_enabled not in firewalld_output.stderr
      vars:
        already_enabled: ALREADY_ENABLED

    - name: forward_ports | Restart firewalld
      ansible.builtin.systemd:
        name: firewalld
        state: restarted
