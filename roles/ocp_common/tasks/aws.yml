# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-03-21 espy                                                   #
# Create Date:  2025-03-21                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: aws | Check if captive portal heartbeat is already running
  ansible.builtin.stat:
    path: /tmp/captive_portal_heartbeat.pid
  register: captive_portal_heartbeat

- name: aws | Prompt for Username
  ansible.builtin.pause:
    prompt: Enter Captive Portal username
  register: captive_portal_username
  when: not captive_portal_heartbeat.stat.exists

- name: aws | Prompt for Pin
  ansible.builtin.pause:
    prompt: Enter Captive Portal pin
    echo: false
  register: captive_portal_pin
  when: not captive_portal_heartbeat.stat.exists

- name: aws | Log into captive portal
  lmco.openshift.captive_portal:
    cp_url: "cpauth.us.lmco.com"
    dssignin: "url_default"
    username: "{{ captive_portal_username.user_input }}"
    pin: "{{ captive_portal_pin.user_input }}"
  when: not captive_portal_heartbeat.stat.exists

- name: aws | Create AWS Folder
  ansible.builtin.file:
    path: ~/.aws
    mode: "0700"
    state: directory

- name: aws | Create aws config file
  ansible.builtin.copy:
    dest: ~/.aws/config
    mode: "0600"
    content: |
      [default]
      region = {{ ocp.aws.region }}
      [hosted_zone]
      region = {{ ocp.aws.region }}

- name: aws | Assume openshift role
  amazon.aws.sts_assume_role:
    role_arn: "{{ ocp.aws.galaxy_openshift_ipi_role }}"
    role_session_name: galaxy-{{ ocp.openshift_cluster }}-deploy-session
    duration_seconds: 3600
    aws_ca_bundle: /etc/pki/ca-trust/source/anchors/Combined_pem.pem
    secret_key: "{{ ocp.aws.aws_secret_access_key }}"
    access_key: "{{ ocp.aws.aws_access_key_id }}"
  register: sts_response
  when: ocp.aws.galaxy_openshift_ipi_role is defined

- name: aws | Create aws credentials file
  ansible.builtin.copy:
    dest: ~/.aws/credentials
    mode: "0600"
    content: |
      [default]
      aws_access_key_id     = {{ ocp.aws.aws_access_key_id }}
      aws_secret_access_key = {{ ocp.aws.aws_secret_access_key }}
      [hosted_zone]
      aws_access_key_id     = {{ sts_response.sts_creds.access_key if ocp.aws.galaxy_openshift_ipi_role is defined else ocp.aws.aws_access_key_id }}
      aws_secret_access_key = {{ sts_response.sts_creds.secret_key if ocp.aws.galaxy_openshift_ipi_role is defined else ocp.aws.aws_secret_access_key }}
      {% if ocp.aws.galaxy_openshift_ipi_role is defined %}
      aws_session_token = {{ sts_response.sts_creds.session_token }}
      {% endif %}


- name: aws | Apply aws specific variables
  ansible.builtin.set_fact:
    ocp: "{{ ocp | combine(aws_ocp, recursive=true) }}"
