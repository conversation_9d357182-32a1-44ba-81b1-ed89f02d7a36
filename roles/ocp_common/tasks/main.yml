# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-02 <PERSON><PERSON><PERSON>, <PERSON>                                      #
#               2023-10-11 <PERSON><PERSON>, <PERSON>                                           #
#               2023-10-25 espy                                                   #
#               2023-11-01 espy                                                   #
#               2023-11-01 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-05 espy                                                   #
#               2023-11-07 espy                                                   #
#               2023-11-15 <PERSON><PERSON><PERSON>, <PERSON> T                                      #
#               2023-11-22 espy                                                   #
#               2023-11-30 <PERSON><PERSON><PERSON>, <PERSON> T                                      #
#               2024-01-26 espy                                                   #
#               2024-01-30 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-02-10 espy                                                   #
#               2024-02-12 espy                                                   #
#               2024-02-13 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-08-21 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-02                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON>                                                 #
# Description:                                                                    #
#               This is the linux product task for galaxy_linux product with      #
#               red hat enterprise linux as the provider and compute as the       #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Display Galaxy Inputs
  ansible.builtin.debug:
    msg:
      - default_ocp - {{ default_ocp | default({}) }}
      - ocp_base - {{ ocp_base | default({}) }}
      - default_derived_ocp1 - {{ default_derived_ocp1 | default({}) }}
      - ocp_derived - {{ ocp_derived | default({}) }}
      - galaxy_ocp - {{ galaxy_ocp | default({}) }}
      - lmco_ocp - {{ lmco_ocp | default({}) }}
      - bastion_ocp - {{ bastion_ocp | default({}) }}
      - cli_ocp - {{ cli_ocp | default({}) }}
      - galaxy_hosts - {{ galaxy_hosts | default([]) }}
    verbosity: 1

- name: One time set of ocp variables
  ansible.builtin.set_fact:
    ocp: "{{ ocp_derived | default({}) |
      combine(galaxy_ocp | default({}), recursive=true) |
      combine(lmco_ocp | default({}), recursive=true) |
      combine(bastion_ocp | default({}), recursive=true) |
      combine(cli_ocp | default({}), recursive=true) |
      combine(galaxy_baremetal_list, recursive=true) }}"
  vars:
    temp_list: "{{ galaxy_hosts | default([]) | selectattr('abstract.purpose', 'in', ['master', 'worker']) | list }}"
    galaxy_baremetal_list:
      baremetal_list: "{{ temp_list if temp_list | length > 0 else ocp_derived.baremetal_list }}"

- name: Apply common aws tasks
  ansible.builtin.include_tasks:
    file: aws.yml
    apply:
      delegate_to: localhost
  when: ocp.platform == 'aws'

- name: Verify OCP variable specification
  ansible.builtin.validate_argument_spec:
    argument_spec: "{{ (lookup('ansible.builtin.file', '../meta/ocp_argument_specs.yml') | from_yaml)['argument_specs']['main']['options'] }}"
