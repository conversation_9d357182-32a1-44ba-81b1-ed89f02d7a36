# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        ocp_argument_spec.yml                                             #
# Version:                                                                        #
#               2023-11-10 <PERSON><PERSON><PERSON>, <PERSON> T                                      #
#               2023-11-15 <PERSON><PERSON><PERSON>, <PERSON> T                                      #
#               2023-11-16 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-21 espy                                                   #
#               2023-11-21 <PERSON>, <PERSON> J                                          #
#               2023-11-21 <PERSON><PERSON>, <PERSON>                                           #
#               2023-11-22 espy                                                   #
#               2023-11-28 espy                                                   #
#               2023-12-11 espy                                                   #
#               2023-12-12 <PERSON><PERSON>, <PERSON>                                           #
#               2023-12-14 espy                                                   #
#               2023-12-19 <PERSON><PERSON><PERSON><PERSON>, <PERSON>                                    #
#               2023-12-19 espy                                                   #
#               2024-01-11 <PERSON><PERSON>, <PERSON>                                           #
#               2024-01-16 <PERSON>, <PERSON> J                                          #
#               2024-01-17 espy                                                   #
#               2024-01-17 <PERSON>, <PERSON> J                                          #
#               2024-01-18 espy                                                   #
#               2024-01-18 <PERSON>, <PERSON> J                                          #
#               2024-01-23 espy                                                   #
#               2024-01-23 Sam<PERSON>, <PERSON>                                           #
#               2024-01-24 espy                                                   #
#               2024-01-25 espy                                                   #
#               2024-01-30 espy                                                   #
#               2024-02-06 <PERSON>, <PERSON>                                          #
#               2024-02-12 espy                                                   #
#               2024-02-13 espy                                                   #
#               2024-02-14 <PERSON>s, <PERSON>                                           #
#               2024-02-15 espy                                                   #
#               2024-02-16 <PERSON>, <PERSON> J                                          #
#               2024-02-20 G<PERSON>man, <PERSON> T                                      #
#               2024-02-22 G<PERSON>man, <PERSON> T                                      #
#               2024-02-27 G<PERSON>man, <PERSON> T                                      #
#               2024-03-04 espy                                                   #
#               2024-03-05 espy                                                   #
#               2024-03-07 espy                                                   #
#               2024-03-20 espy                                                   #
#               2024-04-01 Espinoza, Michael M                                    #
#               2024-04-01 espy                                                   #
#               2024-04-02 espy                                                   #
#               2024-04-03 espy                                                   #
#               2024-04-15 espy                                                   #
#               2024-04-16 espy                                                   #
#               2024-04-18 espy                                                   #
#               2024-04-19 espy                                                   #
#               2024-04-28 espy                                                   #
#               2024-05-06 espy                                                   #
#               2024-05-07 Sams, Landon                                           #
#               2024-05-08 espy                                                   #
#               2024-05-22 espy                                                   #
#               2024-05-29 espy                                                   #
#               2024-06-05 espy                                                   #
#               2024-06-06 espy                                                   #
#               2024-06-11 espy                                                   #
#               2024-06-18 espy                                                   #
#               2024-06-19 espy                                                   #
#               2024-06-20 espy                                                   #
#               2024-07-16 espy                                                   #
#               2024-07-17 espy                                                   #
#               2024-07-18 espy                                                   #
#               2024-07-25 Hunter, Ian J                                          #
#               2024-07-25 Warren Wieczorek                                       #
#               2024-07-29 espy                                                   #
#               2024-07-30 espy                                                   #
#               2024-08-07 espy                                                   #
#               2024-08-12 Espinoza, Michael M                                    #
#               2024-08-12 espy                                                   #
#               2024-08-15 espy                                                   #
#               2024-08-21 espy                                                   #
#               2024-08-28 espy                                                   #
#               2024-08-29 espy                                                   #
#               2024-09-05 espy                                                   #
#               2024-09-11 espy                                                   #
#               2024-10-29 Hunter, Ian J                                          #
#               2024-11-21 Sams, Landon                                           #
#               2024-12-03 Sams, Landon                                           #
#               2024-12-19 espy                                                   #
#               2025-01-24 Sams, Landon                                           #
#               2025-01-30 espy                                                   #
# Create Date:  2023-11-10                                                        #
# Author:       Grosman, Andrew T                                                 #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

argument_specs:
  main:
    short_description: Entry point for ocp configuration options
    description: Verify inputs for ocp install
    author: Grosman, Andrew T <<EMAIL>>
    options:
      ocp:
        type: dict
        required: true
        description: OCP config
        no_log: false
        options:
          aws:
            type: dict
            description:
              - AWS Config
            options:
              vpc_id:
                type: str
                description:
                  - VPC id
              az:
                type: str
                description:
                  - Availability zone
              region:
                type: str
                description:
                  - Region
              aws_access_key_id:
                type: str
                description:
                  - Key Id
              aws_secret_access_key:
                type: str
                description:
                  - Access Key
              baremetal_subnet_id:
                type: str
                description:
                  - Subnet id
              galaxy_openshift_ipi_role:
                type: str
                description:
                  - Role used for ipi install
              master_count:
                type: int
                description:
                  - Number of master nodes
              worker_count:
                type: int
                description:
                  - Number of worker nodes
          additional_no_proxies:
            type: list
            description:
              - This controls what addresses do not use the proxy setup in ocp.http_proxy (and variations)
              - This affects the bastion server and several of its services as well as the openshift cluster.
            elements: str
          standard_no_proxy_addresses:
            type: str
            description: Stand no proxies
          baremetal_list:
            required: false
            type: list
            description:
              - This contains a detailed list of all the machines in which openshift will be installed on
              - This include the master and worker nodes
              - template
            elements: dict
            options:
              abstract:
                required: true
                type: dict
                description:
                  - General information on the node
                options:
                  name:
                    required: true
                    type: str
                    description:
                      - This will be used as the hostname for the openshift cluster nodes
                      - This is also used as the name for the node in the openshift installation
                  purpose:
                    required: true
                    type: str
                    description:
                      - Node purpose (master/worker/etc.)
                      - This determines what kind of node this will be in the cluster
                    choices:
                      - master
                      - worker
                  description:
                    required: false
                    type: str
                    description:
                      - Node description
              bmc:
                required: true
                type: dict
                description:
                  - Baseboard Management Configuration (iLo/iDrac/etc.)
                options:
                  domain_name:
                    required: false
                    type: str
                    description:
                      - BMC Domain Name
                  ipv4_address:
                    required: true
                    type: str
                    description:
                      - ipv4 BMC Address
                      - regex ^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$
                  name:
                    required: false
                    type: str
                    description:
                      - BMC Name
                      - Conventionally set to the serial number to identify each node
                  type:
                    required: true
                    type: str
                    description:
                      - BMC Type such as idrac ilo ipmi etc
                    choices:
                      - ilo
                      - ipmi
                  username:
                    required: true
                    type: str
                    description:
                      - BMC username
                  password:
                    required: true
                    type: str
                    description:
                      - BMC Password
                  system_id:
                    required: true
                    type: str
                    description: BMC Redfish System ID
                  manager_id:
                    required: false
                    type: int
                    description: BMC Redfish Manager ID
                  power:
                    required: false
                    type: str
                    description:
                      - Server power state to set to after running compute module
              openshift:
                required: true
                type: dict
                description:
                  - Openshift Configuration
                options:
                  baremetal_network_mac_address:
                    type: str
                    description:
                      - The mac address of the nic which is connected to the baremetal network
                  baremetal_interface_name:
                    type: str
                    description:
                      - The interface name of the nic which is connected to the baremetal network
                  provisioning_network_mac_address:
                    required: false
                    type: str
                    description:
                      - The mac address of nic connected to the provisioning network
                  coreos_installation_device:
                    required: true
                    type: str
                    description: The storage device to install openshift on
              network_config:
                required: false
                type: dict
                description: The nmstate network configuration for the node
          banner_top_text:
            type: str
            description:
              - OpenShift top banner text
          banner_top_color:
            type: str
            description:
              - OpenShift top banner color
          banner_bottom_text:
            type: str
            description:
              - OpenShift bottom banner text
          banner_bottom_color:
            type: str
            description:
              - OpenShift bottom banner color
          banner_login_text:
            type: str
            description:
              - OpenShift banner login text
          sso_client_id:
            type: str
            description:
              - SSO Client ID
              - Client ID is issued from SSO server
          sso_issuer:
            type: str
            description:
              - SSO Issuer URL
              - Supplies client ID
          sso_name:
            type: str
            description:
              - SSO Name
          sso_secret:
            type: str
            description:
              - SSO Secret
          sso_type:
            type: str
            description:
              - SSO Type
            choices:
              - "OpenID"
          cluster_admins:
            type: list
            description:
              - List of user IDs to map to the cluster-admin role
            elements: str
          baremetal_network_existing_servers:
            type: list
            description:
              - List of server ips that already exist on this network
            elements: str
          baremetal_network_bastion_id:
            type: int
            description:
              - Baremetal network bastion ID
              - Conventionally is the last octet
              - Overridden by baremetal_network_bastion_ip
          bastion_public_network_interface:
            type: dict
            description:
              - the nmstate dict that describes the public network on the bastion
          baremetal_network_bastion_ip:
            type: str
            description:
              - Baremetal network bastion IP address
              - Overrides baremetal_network_bastion_id
          baremetal_network_bastion_vip:
            type: str
            description:
              - Baremetal network bastion VIP address
          baremetal_network_bootstrap_id:
            type: int
            description:
              - Baremetal network bootstrap ID
              - Overridden by baremetal_network_bootstrap_ip
          baremetal_network_bootstrap_ip:
            type: str
            description:
              - Baremetal network bootstrap IP address
              - Overrides baremetal_network_bootstrap_id
          baremetal_network_temp_bridge_name:
            type: str
            description:
              - Baremetal network bridge name
              - Temporary network bridge setup during install
              - Baremetal by default
          baremetal_network_registry_ip:
            type: str
            description:
              - Baremetal network registry IP address
              - Defaults to bastion
              - Different services can be on different, non-bastion nodes
          baremetal_network_cidr:
            type: str
            description:
              - Baremetal network CIDR
          baremetal_network_dns_ip:
            type: str
            description:
              - Baremetal network DNS IP address
              - Defaults to bastion
              - Different services can be on different, non-bastion nodes
          baremetal_network_gateway_ip:
            type: str
            description:
              - Baremetal network gateway IP address
              - Defaults to bastion
              - Different services can be on different, non-bastion nodes
          baremetal_network_ntp_ip:
            type: str
            description:
              - Baremetal network NTP IP address
              - Defaults to bastion
              - Different services can be on different, non-bastion nodes
          baremetal_network_openshift_api_vip:
            type: str
            description:
              - Baremetal Network OpenShift API VIP
              - Overrides baremetal_network_openshift_api_vip_id
          baremetal_network_openshift_api_vip_id:
            type: int
            description:
              - Baremetal network OpenShift API VIP ID
              - Overridden by baremetal_network_openshift_api_vip
          baremetal_network_openshift_ingress_id:
            type: int
            description:
              - Baremetal network OpenShift ingress ID
              - Overridden by baremetal_network_openshift_ingress_vip
          baremetal_network_openshift_ingress_vip:
            type: str
            description:
              - Overrides baremetal_network_openshift_ingress_id
              - Baremetal Network OpenShift ingress VIP
          bastion_baremetal_network_interface_name:
            type: str
            description:
              - Interface name of bastion that connects to baremetal network
          provisioning_network_prefix_length:
            type: int
            description:
              - Provisioning network CIDR prefix length
          baremetal_network_prefix_length:
            type: int
            description:
              - Baremetal network CIDR's prefix length
          baremetal_network_unique_bastion_id:
            type: int
            description:
              - Unique ID for bastion
              - Installs with multiple bastions require unique IDs
          bastion_bmc_ip:
            type: str
            description:
              - BMC network bastion IP address
              - Defaults to None
          bastion_host_key_file:
            type: str
            description:
              - Bastion host SSH key file
          bastion_is_gateway:
            type: bool
            description:
              - Bastion is gateway
              - Have bastion act as the network gateway for the cluster
          bastion_provisioning_network_interface_name:
            type: str
            description:
              - Bastion provisioning network interface name
          bastion_public_ip:
            type: str
            description:
              - Bastion public IP address
              - Externally facing IP
          bastion_ssh_password:
            type: str
            description:
              - Bastion SSH password
          bmc_api:
            required: true
            type: str
            description:
              - BMC API
              - API type for OpenShift install
            choices:
              - "redfish"
              - "ipmi"
              - "redfish-virtualmedia"
              - "aws"
          bootstrap_os:
            type: bool
            description:
              - Bootstrap operating system
              - Start from scratch with bastion operating system
          ca_cert:
            type: str
            description:
              - CA certificate for customer
              - File path
          galaxy_ca_cert:
            type: str
            description:
              - CA certificate used for galaxy builder
              - File path
          galaxy_builder_lmi_ip:
            type: str
            description:
              - Cluster builder IP address on the lmi (public) network
              - Machine running ansible automation (cluster builder)
          galaxy_builder_mgmt_ip:
            type: str
            description:
              - Cluster builder IP address on the management network
              - Machine running ansible automation (cluster builder)
          galaxy_builder_port:
            type: str
            description:
              - Cluster builder port number
          cluster_specific_rhel_iso:
            type: str
            description:
              - Cluster specific RHEL ISO
              - Filename of RHEL ISO for cluster
          cluster:
            type: dict
            description:
              - OpenShift cluster details
            options:
              host:
                type: str
                description:
                  - Cluster host
              username:
                type: str
                description:
                  - Cluster username
              password:
                type: str
                description:
                  - Cluster password
              api_key:
                type: str
                description:
                  - OpenShift API Server URL API Token
                  - This is an option for logging into an openshift cluster that can be provided for the openshift login role
          cluster_cert:
            type: str
            description:
              - Filepath on ansible host to OpenShift cluster wildcard certificate
          cluster_key:
            type: str
            description:
              - Filepath on ansible host to cluster builder wildcard key
          create_install_config:
            type: bool
            description:
              - Create install configuration
          developer_run:
            type: bool
            description:
              - Developer run
              - Decides whether to install files from ISO or HTTPD server
          dev_connected:
            type: bool
            description:
              - Connected for development purposes
          dnf_isos:
            type: list
            description:
              - List of ISOs to include in the DNF repos
            elements: dict
            options:
              iso:
                type: str
                description:
                  - ISO file name
              name:
                type: str
                description:
                  - Name of the repo
              repos:
                type: list
                description:
                  - List of repos on the iso to include in the DNF repo on nexus
              sdx_file:
                type: str
                description:
                  - Name of the SPDX file
              version:
                type: str
                description:
                  - Software version
          dns_servers:
            type: list
            description:
              - List of DNS servers
            elements: str
          etcd_backup_settings:
            type: dict
            description:
              - Backup settings for etcd
            options:
              appLabel:
                type: str
                description:
                  - AppLabel for backup Resources for the backup service in openshift
              nexus:
                type: dict
                description:
                  - Nexus configuration
                options:
                  folder:
                    type: str
                    description:
                      - Folder in nexus to backup the openshift cluster to
                  repository:
                    type: str
                    description:
                      - Url for the repository to upload backup to
                  username:
                    type: str
                    description:
                      - Username for the nexus repositoryto upload backup to
                  token:
                    type: str
                    description:
                      - Password for the nexus repositoryto upload backup to
              schedule:
                type: str
                description:
                  - Crontab syntax schedule
                  - This is the schedule this service will use to backup the cluster to nexus
          bastion:
            type: dict
            description:
              - Bastion information
              - template
            options:
              abstract:
                type: dict
                description:
                  - General Information on the galaxy server
                options:
                  name:
                    type: str
                    description: The name of the bastion (Not used)
                  description:
                    type: str
                    description: Description (not used)
                  purpose:
                    type: str
                    description:
                      - Should always be bastion
              credentials:
                type: dict
                description:
                  - Credentials
                options:
                  root_password:
                    type: str
                    description: The password to be set for the root user on the bastion
                  ocp_user_authorized_public_key_file:
                    type: str
                    description: The authorized public key file to be set for the OCP user on the bastion
                  ocp_user_authorized_public_key:
                    type: str
                    description: The authorized public key to be set for the OCP user on the bastion
                  ocp_user_password:
                    type: str
                    description: The password to be set for the OCP user on the bastion
              disk_part:
                type: dict
                description:
                  - Disk partition information
                options:
                  home:
                    description: Size of /home
                    type: str
                  opt:
                    description: Size of /opt
                    type: str
                  root:
                    description: Size of /root
                    type: str
                  swap:
                    description: Size of swap
                    type: str
                  tmp:
                    description: Size of /tmp
                    type: str
                  var_log_audit:
                    description: Size of /var/log/audit
                    type: str
                  var_log:
                    description: Size of /var/log
                    type: str
                  var_tmp:
                    description: Size of /var/tmp
                    type: str
                  var:
                    description: Size of /var
                    type: str
              options:
                type: dict
                description:
                  - Options
                options:
                  shell_color:
                    type: str
                    description: represents color 0;32
              storage_drives:
                type: list
                elements: str
                description:
                  - Storage Drives
                  - Format sda, sdb, nvme1, nvme2, etc.
              network:
                type: dict
                description:
                  - Network configuration information
                options:
                  external_connection_name:
                    type: str
                    description: THe name of the connection to external networks
                  routes:
                    type: dict
                    description: Nmstate routes dict
                  interfaces:
                    type: list
                    elements: dict
                    description:
                      - NMState interfaces for the bastion
              bmc:
                type: dict
                description:
                  - BMC Config
                options:
                  domain_name:
                    type: str
                    description:
                      - BMC Domain Name
                  ipv4_address:
                    type: str
                    description:
                      - ipv4 BMC Address
                      - regex ^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}$
                  manager_id:
                    type: int
                    description:
                      - This is the ID of the manager of the system
                      - The manager in this context is the server that manages the system
                      - For example in HP machines the manager would be the iLo
                      - In most cases the manager ID is 1
                      - This is referenced when used for manager resources such as a virtual cd rom.
                  name:
                    type: str
                    description:
                      - BMC Name
                      - Conventionally set to the serial number to identify each node
                      - Used in the compute module
                  type:
                    type: str
                    description:
                      - BMC Type such as idrac ilo ipmi etc
                    choices:
                      - ilo
                      - ipmi
                      - null
                  username:
                    type: str
                    description:
                      - BMC username
                  password:
                    type: str
                    description:
                      - BMC Password
                  system_id:
                    type: str
                    description:
                      - BMC Redfish System ID
                      - Typically 1 for HP systems since theres usually one API per server
                  power:
                    type: str
                    description:
                      - BMC power
                      - The power state to set to when running openshift compute module
                  virtual_media_id:
                    type: int
                    description:
                      - ID Number of the redfish media
                      - ex. virtual media 1 is floppy, virtual media 2 is dvd
              keepalived:
                type: dict
                description:
                  - Keep Alive Config
                options:
                  role:
                    type: str
                    description:
                      - Role of the bastion keepalived instance
                    choices:
                      - "MASTER"
                      - "BACKUP"
                      - null
                  advertising_interval:
                    type: int
                    description: Advertixing interval in seconds
                  password:
                    type: str
                    description: Password for keepalived connection
                  baremetal_vrid:
                    type: int
                    description: VRID for baremetal network
                  public_vrid:
                    type: int
                    description: VRID for public network
              aws:
                type: dict
                description:
                  - AWS Config
                options:
                  ami_id:
                    type: str
                    description:
                      - Image id
          expose_nodeports:
            type: bool
            description:
              - Expose the node ports through HAProxy
          force_mirror_registry_redeploy:
            type: bool
            description:
              - Force redeploy of mirror registry
          force_redeployment:
            type: bool
            description:
              - Force redeployment
              - Checks if OS is running, forces redeployment
          haproxy_stats_port:
            type: int
            description:
              - Stats port for HAProxy
          openshift_public_ip:
            type: str
            description:
              - IP addressed used for OpenShift cluster
          http_proxy:
            type: str
            description:
              - Definition for http proxy
              - Used for bastion and openshift cluster
          https_proxy:
            type: str
            description:
              - Definition for https proxy
              - Used for bastion and openshift cluster
          install_lmco_ca:
            type: bool
            description:
              - Determines whether or not to install LMCO CA on bastion
          kubeconfig_file:
            type: str
            description:
              - Kubeconfig file
              - Location of where to save kubeconfig file on ansible host
          lmco_ca_url:
            type: str
            description:
              - LMCO CA certificate location
          local_yum_repo:
            type: bool
            description:
              - Install local Yum repository
          master_nodes_starting_ip:
            type: int
            description:
              - Master nodes starting IP address on the baremtal network
          nmstate:
            type: list
            description:
              - Definition of NMState configurations to be applied to the cluster
            elements: dict
            options:
              name:
                type: str
                description:
                  - Name of the interface
              spec:
                type: dict
                description:
                  - List of NMState configurations
          nodes_luks_threshold:
            type: int
            description:
              - Node LUKS threshold
              - Threshold number of authentication factors to decrypt drives of OpenShift nodes
          nodes_mirror_devices:
            type: list
            description:
              - List of devices for mirroring drives on OpenShift nodes
              - ex. mirror sda and sdb
            elements: str
          nodes_tang_servers:
            type: list
            description:
              - OpenShift node's list of Tang servers
            elements: str
          nodes_tpm_encryption:
            type: bool
            description:
              - Whether or not to used TPM to encrypt OpenShift nodes drives
          nodeport_range:
            type: str
            description:
              - Range of ports that will be available for nodeports in the cluster
          ntp_servers:
            type: list
            description:
              - List of NTP servers
            elements: str
          nexus_storage:
            type: str
            description:
              - OCP Registry Storage file which contains the image registry
          galaxy_nexus_repo_name:
            type: str
            description:
              - The name of the nexus galaxy repository
          bastion_rhel_packages_tarball:
            type: str
            description:
              - The name of the bastion rhel packages tarball
          registry_hostname:
            type: str
            description: The url for the galaxy registry
          default_registry_url:
            type: str
            description: The url for the galaxy registry
          galaxy_nexus_repo:
            type: str
            description:
              - The url of the galaxy nexus repository for binaries
          galaxy_nexus_hostname:
            type: str
            description: The url for the nexus repository
          galaxy_proxy:
            type: str
            description: The url for the galaxy proxy for virtual media installs
          galaxy_proxy_port:
            type: int
            description: The port for the galaxy proxy for virtual media installs
          virtual_media_bastion_http_listen_port:
            type: int
            description: The port for the http virtual media proxy
          virtual_media_bastion_https_listen_port:
            type: int
            description: The port for the https virtual media proxy
          virtual_media_http_dest_ports:
            type: list
            description: The ports openshift uses for virtual media on http
            elements: int
          virtual_media_https_dest_ports:
            type: list
            description: The ports openshift uses for virtual media on https
            elements: int
          http_non_root_port:
            type: int
            description: The port to listen for http traffic that gets forwarded from the firewall rules
          https_non_root_port:
            type: int
            description: The port to listen for https traffic that gets forwarded from the firewall rules
          nexus_forward_port:
            type: int
            description: The intermediate port to forward nexus traffic to
          registry_forward_port:
            type: int
            description: The intermediate port to forward registry traffic to
          nexus_http_port:
            type: int
            description: The port nexus listens one
          dns_port:
            type: int
            description: The port coredns listens to for dns requests
          kube_api_port:
            type: int
            description: The port the cluster api listens to for kube api requests
          nexus_image_file:
            type: str
            description: The file that contains the nesus image
          ocp_install_files:
            type: str
            description:
              - OCP install files folder name
          ocp_install_files_url:
            type: str
            description:
              - OCP install files url
          registry_port:
            type: int
            description:
              - Port for galaxy registry
          coreos_password:
            type: str
            description:
              - Password for coreos.  Will not be set if not provided
          ocp_rhel_iso_name:
            type: str
            description:
              - OCP RHEL ISO name
          ocp_user:
            type: str
            description:
              - OCP user name
          verson_vars_file:
            type: str
            description:
              - This is the name of the file that holds the sha values for the openshift install
          openshift_cluster:
            type: str
            description:
              - OpenShift cluster name
              - This is used to determine the domain name of the cluster
          hostname:
            type: str
            description:
              - This is the whole openshift domain name
          fips_enabled:
            type: bool
            description:
              - Enable FIPS
          openshift_install_file:
            type: str
            description:
              - This is the name of the openshift binary that install openshift
          openshift_install_path:
            type: str
            description:
              - OThe location of where to install openshift on the bastion
          openshift_version:
            required: true
            type: str
            description:
              - Version of OpenShift to deploy
            choices:
              - "4.14.31"
              - "4.18.20"
          distro:
            required: true
            type: str
            description:
              - Version of kubernetes to deploy
            choices:
              - "ocp"
              - "okd"
          okd_version:
            type: str
            description:
              - Version of OKD to deploy
          provisioning_network_bastion_id:
            type: str
            description:
              - Provisioning network bastion ID
              - This is the last octet of the ip address of the bastion on the provisioning network
          provisioning_network_bastion_ip:
            type: str
            description:
              - Provisioning network bastion IP address
          provisioning_network_temp_bridge_name:
            type: str
            description:
              - Provisioning network bridge name
          provisioning_network_cidr:
            type: str
            description:
              - Provisioning network CIDR
          galaxy_nexus_username:
            type: str
            description:
              - Initial user for Nexus
          galaxy_nexus_password:
            type: str
            description:
              - Nexus password
          redhat_registry_pull_secret:
            type: str
            description:
              - RedHat registry pull secret
          registry_key:
            type: str
            description:
              - Private key for registry cert
          rhel_dnf_repo_name:
            type: str
            description:
              - The name of the file that has the rhel dnf repo
          rhel_version:
            type: str
            description:
              - Version of RHEL to use
            choices:
              - 9.4.0
              - 9.5.0
          platform:
            type: str
            description:
              - PLatform like aws or baremetal
            choices:
              - aws
              - baremetal
          file_transfer_method:
            type: str
            description:
              - Method for transfering files from builder to bastion
            choices:
              - ssh
              - http
          basic_packages:
            type: list
            description:
              - List of packages to install in bastion
          rhel_major_version:
            type: int
            description:
              - Major version of RHEL to use
          rhel_minor_version:
            type: int
            description:
              - Minor version of RHEL to use
          rhel_patch_version:
            type: int
            description:
              - Patch version of RHEL to use
          setup_dns:
            type: bool
            description:
              - Enables/disables the setup of a bastion hosted dns server
          setup_libvirt:
            type: bool
            description:
              - Enables/disables the installation of libvirt
          cluster_lb_enabled:
            type: bool
            description:
              - Enables/disables the installation of haproxy
          bastion_firewall_enabled:
            type: bool
            description:
              - Enables/disables the use of firewalld
          bastion_ntp_enabled:
            type: bool
            description:
              - Enables/disables the use of ntp
          bastion_libvirt_enabled:
            type: bool
            description:
              - Enables/disables the use of libvirt
          setup_networks:
            type: bool
            description:
              - Enables/disables the network setup
          setup_ntp:
            type: bool
            description:
              - Enables/disables the setting up of NTP
          setup_ocp_user:
            type: bool
            description:
              - Enables/disables the setting up of a ocp user
          setup_proxy:
            type: bool
            description:
              - Enables/disables the setting up of the bastion proxy
          setup_coreos_cache:
            type: bool
            description:
              - Enables/disables the setting up the coreos cache
          setup_rhsm:
            type: bool
            description:
              - Enables/disables the setting up of RHSM
          simulated_disconnected_env:
            type: bool
            description:
              - Enables disables simulated disconnected environment set up
          tpm_encryption:
            type: bool
            description:
              - Enables disables bastion tpm encryption
          update_dns:
            type: bool
            description:
              - Enables/disables updating the local DNS of the bastion
          validate_certs:
            type: bool
            description:
              - Enables/disables cert validation in many scenarios
          setup_provisioning_network:
            type: bool
            description:
              - Enables/disables provisioning network
          worker_nodes_starting_ip:
            type: int
            description:
              - Worker nodes starting IP address in the baremetal network.
              - The worker nodes will use ip addresses in the baremetal network cidr starting from this specified ip
          working_dir:
            type: str
            description:
              - Working directory
          manifest_files_folder:
            type: str
            description:
              - Directory for manifests files
          registry_crt_file:
            type: str
            description: Filepath for registry tls.crt file
