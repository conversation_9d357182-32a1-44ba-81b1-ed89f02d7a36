# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-19 espy                                                   #
#               2023-10-25 espy                                                   #
#               2024-02-08 espy                                                   #
#               2024-02-12 espy                                                   #
#               2024-04-03 espy                                                   #
#               2024-06-27 espy                                                   #
#               2024-07-16 espy                                                   #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-19                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Validate bastion
  ansible.builtin.include_tasks:
    file: hardware.yml
  vars:
    machine:
      bmc_address: "{{ ocp.bastion.bmc.ipv4_address }}"
      bmc_username: "{{ ocp.bastion.bmc.username }}"
      bmc_password: "{{ ocp.bastion.bmc.password }}"
      bmc_system_id: "{{ ocp.bastion.bmc.system_id }}"
      bmc_type: "{{ ocp.bastion.bmc.type }}"

- name: Validate cluster nodes
  ansible.builtin.include_tasks:
    file: hardware.yml
  loop: "{{ ocp.baremetal_list }}"
  loop_control:
    loop_var: machine

- name: Validate provided certs
  lmco.openshift.validate_certs:
    cert_data: "{{ lookup('file', ocp.cluster_cert, rstrip=false) }}"
    key_data: "{{ lookup('file', ocp.cluster_key, rstrip=false) }}"
    ca_data: "{{ lookup('file', ocp.ca_cert, rstrip=false) }}"
    domains:
      - "*.{{ ocp.hostname }}"
      - "*.apps.{{ ocp.hostname }}"
      - "{{ ocp.hostname }}"
