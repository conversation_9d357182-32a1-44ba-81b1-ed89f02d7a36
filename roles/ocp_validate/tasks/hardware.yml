# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2023-10-12 espy                                                   #
#               2023-10-16 espy                                                   #
#               2024-04-03 espy                                                   #
#               2024-04-27 <PERSON><PERSON><PERSON><PERSON>, <PERSON> M                                    #
#               2025-01-30 espy                                                   #
# Create Date:  2023-10-12                                                        #
# Author:       espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: hardware | Validate iLO version
  ansible.builtin.uri:
    url: https://{{ machine.bmc_address }}/redfish/v1/Managers/{{ machine.bmc_system_id }}/
    method: GET
    user: "{{ machine.bmc_username }}"
    password: "{{ machine.bmc_password }}"
    force_basic_auth: true
    validate_certs: false
    return_content: true
  register: hp_redfish_results
  environment:
    no_proxy: "{{ machine.bmc_address }}"
  when: machine.bmc_type is defined and machine.bmc_type and machine.bmc_type == 'ilo'

- name: hardware | Fail if version doesn't match 2.60 or above regex
  ansible.builtin.fail:
    msg: ILO version [{{ hp_redfish_results.json.FirmwareVersion }}] is not supported
  when: valid_version
  vars:
    ilo6_pattern: '^iLO 6 v((1\.((5\d)|([6-9]\d)))|(([2-9]\.\d\d?)))$'
    ilo5_pattern: '^iLO 5 v((2\.((6\d)|([7-9]\d)))|(([3-9]\.\d\d?)))$'
    ilo4_pattern: '^iLO 4 v2.82$'
    json: 'json'
    valid_version: >-
      {{
        hp_redfish_results is defined and
        json in hp_redfish_results and
        hp_redfish_results.json.FirmwareVersion is not match(ilo6_pattern) and
        hp_redfish_results.json.FirmwareVersion is not match(ilo5_pattern) and
        hp_redfish_results.json.FirmwareVersion is not match(ilo4_pattern)
      }}
